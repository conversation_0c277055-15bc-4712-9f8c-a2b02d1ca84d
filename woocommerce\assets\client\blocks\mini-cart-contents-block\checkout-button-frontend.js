"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[6476],{9616:(e,t,c)=>{c.r(t),c.d(t,{default:()=>h});var o=c(8331),s=c(9874),n=c(4921),l=c(371),a=c(3224),r=c(3993);const i=(0,c(7723).__)("Go to checkout","woocommerce");var u=c(2805),k=c(790);const h=({className:e,checkoutButtonLabel:t,style:c})=>{const h=(0,l.p)({style:c}),{dispatchOnProceedToCheckout:d}=(0,a.e)();return o.tn?(0,k.jsx)(s.A,{className:(0,n.A)(e,h.className,"wc-block-mini-cart__footer-checkout"),variant:(0,u.I)(e,"contained"),style:h.style,href:o.tn,onClick:e=>{d().then((t=>{t.some(r.isErrorResponse)&&e.preventDefault()}))},children:t||i}):null}},2805:(e,t,c)=>{c.d(t,{G:()=>n,I:()=>s});var o=c(3993);const s=(e="",t)=>e.includes("is-style-outline")?"outlined":e.includes("is-style-fill")?"contained":t,n=e=>e.some((e=>Array.isArray(e)?n(e):(0,o.isObject)(e)&&null!==e.key))}}]);