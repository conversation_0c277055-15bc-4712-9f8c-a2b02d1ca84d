/*! For license information please see 2508.js.LICENSE.txt */
(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[2508],{2929:(e,t,n)=>{"use strict";n.d(t,{e:()=>p});const r={setOnLoadError:e=>{},setOnLoaderStart:e=>{}},i={"account-onboarding":{setFullTermsOfServiceUrl:e=>{},setRecipientTermsOfServiceUrl:e=>{},setPrivacyPolicyUrl:e=>{},setSkipTermsOfServiceCollection:e=>{},setCollectionOptions:e=>{},setOnExit:e=>{},setOnStepChange:e=>{}},"account-management":{setCollectionOptions:e=>{}},"notification-banner":{setCollectionOptions:e=>{},setOnNotificationsChange:e=>{}},"issuing-card":{setDefaultCard:e=>{},setCardSwitching:e=>{},setFetchEphemeralKey:e=>{},setShowSpendControls:e=>{}},"issuing-cards-list":{setFetchEphemeralKey:e=>{},setShowSpendControls:e=>{},setIssuingProgram:e=>{}},"financial-account":{setFinancialAccount:e=>{}},"financial-account-transactions":{setFinancialAccount:e=>{}},payments:{setDefaultFilters:e=>{}},"payment-details":{setPayment:e=>{},setOnClose:e=>{}},"tax-settings":{setHideProductTaxCodeSelector:e=>{},setDisplayHeadOfficeCountries:e=>{},setOnTaxSettingsUpdated:e=>{}},"tax-registrations":{setOnAfterTaxRegistrationAdded:e=>{},setDisplayCountries:e=>{}}},o={"account-onboarding":"stripe-connect-account-onboarding",payments:"stripe-connect-payments","payment-details":"stripe-connect-payment-details",payouts:"stripe-connect-payouts","payouts-list":"stripe-connect-payouts-list",balances:"stripe-connect-balances","account-management":"stripe-connect-account-management","notification-banner":"stripe-connect-notification-banner","issuing-card":"stripe-connect-issuing-card","issuing-cards-list":"stripe-connect-issuing-cards-list","financial-account":"stripe-connect-financial-account","financial-account-transactions":"stripe-connect-financial-account-transactions",documents:"stripe-connect-documents","tax-registrations":"stripe-connect-tax-registrations","tax-settings":"stripe-connect-tax-settings"},s="loadConnect was called but an existing Connect.js script already exists in the document; existing script parameters will be used",c="https://connect-js.stripe.com/v1.0/connect.js";let u=null;const a=(e,t)=>{var n;const s=(()=>{try{return t.fetchClientSecret()}catch(e){return Promise.reject(e)}})(),c=null!==(n=t.metaOptions)&&void 0!==n?n:{},u=e.then((e=>e.initialize(Object.assign(Object.assign({},t),{metaOptions:Object.assign(Object.assign({},c),{eagerClientSecretPromise:s})}))));return{create:e=>{let t=o[e];t||(t=e);const n=document.createElement(t),s=(e=>e in i)(e)?i[e]:{},c=Object.assign(Object.assign({},s),r);for(const e in c)n[e]=function(t){u.then((()=>{this[`${e}InternalOnly`](t)}))};return u.then((t=>{if(!n.isConnected&&!n.setConnector){const e=n.style.display;n.style.display="none",document.body.appendChild(n),document.body.removeChild(n),n.style.display=e}if(!n||!n.setConnector)throw new Error(`Element ${e} was not transformed into a custom element. Are you using a documented component? See https://docs.stripe.com/connect/supported-embedded-components for a list of supported components`);n.setConnector(t.connect)})),n},update:e=>{u.then((t=>{t.update(e)}))},debugInstance:()=>u,logout:()=>u.then((e=>e.logout()))}},l=e=>(window.StripeConnect=window.StripeConnect||{},window.StripeConnect.optimizedLoading=!0,{initialize:t=>{var n;const r=null!==(n=t.metaOptions)&&void 0!==n?n:{};return e.init(Object.assign(Object.assign({},t),{metaOptions:Object.assign(Object.assign({},r),{sdk:!0,sdkOptions:{sdkVersion:"3.3.21"}})}))}}),d=Promise.resolve().then((()=>(null!==u||(u=new Promise(((e,t)=>{if("undefined"!=typeof window)if(window.StripeConnect&&console.warn(s),window.StripeConnect){const t=l(window.StripeConnect);e(t)}else try{let n=document.querySelectorAll(`script[src="${c}"]`)[0]||document.querySelectorAll('script[src="https://connect-js.stripe.com/v0.1/connect.js"]')[0]||null;n?console.warn(s):n||(n=(()=>{const e=document.createElement("script");if(e.src=c,!document.head)throw new Error("Expected document.head not to be null. Connect.js requires a <head> element.");return document.head.appendChild(e),e})()),n.addEventListener("load",(()=>{if(window.StripeConnect){const t=l(window.StripeConnect);e(t)}else t(new Error("Connect.js did not load the necessary objects"))})),n.addEventListener("error",(()=>{t(new Error("Failed to load Connect.js"))}))}catch(e){t(e)}else t("ConnectJS won't load when rendering code in the server - it can only be loaded on a browser. This error is expected when loading ConnectJS in SSR environments, like NextJS. It will have no impact in the UI, however if you wish to avoid it, you can switch to the `pure` version of the connect.js loader: https://github.com/stripe/connect-js#importing-loadconnect-without-side-effects.")}))),u)));let f=!1;d.catch((e=>{f||console.warn(e)}));const p=e=>(f=!0,a(d,e))},86948:(e,t,n)=>{"use strict";n.d(t,{MT:()=>u,hw:()=>d});var r=n(51609),i=n.n(r);function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var s=r.createContext(null);s.displayName="ConnectComponents";var c,u=function(e){var t=e.connectInstance,n=e.children;return r.createElement(s.Provider,{value:{connectInstance:t}},n)},a=function(e){var t,n,i=(t=r.useState(null),n=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,s,c=[],u=!0,a=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(c.push(r.value),c.length!==t);u=!0);}catch(e){a=!0,i=e}finally{try{if(!u&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(a)throw i}}return c}}(t,n)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),c=i[0],u=i[1],a=function(){var e=r.useContext(s);if(!e)throw new Error("Could not find Components context; You need to wrap the part of your app in an <ConnectComponentsProvider> provider.");return e}().connectInstance,l=r.useRef(null),d=r.createElement("div",{style:{width:"100%"},ref:l});return r.useLayoutEffect((function(){if(null!==l.current&&null===c){var t=a.create(e);if(u(t),null!==t){try{t.setAttribute("reactSdkAnalytics","3.3.21")}catch(e){console.log("Error setting React Sdk version with error message: ",e)}for(;l.current.firstChild;)l.current.removeChild(l.current.firstChild);l.current.appendChild(t)}}}),[a,e]),{wrapper:d,component:c}},l=function(e,t,n){i().useEffect((function(){if(e)try{n(e,t)}catch(e){return void console.error("Error when calling setter! ",e)}}),[e,t,n])},d=function(e){var t=e.onExit,n=e.recipientTermsOfServiceUrl,r=e.fullTermsOfServiceUrl,i=e.privacyPolicyUrl,o=e.skipTermsOfServiceCollection,s=e.collectionOptions,c=e.onLoadError,u=e.onLoaderStart,d=e.onStepChange,f=a("account-onboarding"),p=f.wrapper,h=f.component;return l(h,n,(function(e,t){return e.setRecipientTermsOfServiceUrl(t)})),l(h,r,(function(e,t){return e.setFullTermsOfServiceUrl(t)})),l(h,i,(function(e,t){return e.setPrivacyPolicyUrl(t)})),l(h,o,(function(e,t){return e.setSkipTermsOfServiceCollection(t)})),l(h,s,(function(e,t){return e.setCollectionOptions(t)})),l(h,t,(function(e,t){return e.setOnExit(t)})),l(h,u,(function(e,t){e.setOnLoaderStart(t)})),l(h,c,(function(e,t){e.setOnLoadError(t)})),l(h,d,(function(e,t){return e.setOnStepChange(t)})),p};!function(e){e.exit="exit",e.close="close",e.instantPayoutCreated="instantpayoutcreated"}(c||(c={}))},90700:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5573),i=n(39793);const o=(0,i.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)(r.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})})},56537:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5573),i=n(39793);const o=(0,i.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)(r.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})})},47804:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5573),i=n(39793);const o=(0,i.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(r.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"})})},73290:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5573),i=n(39793);const o=(0,i.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(r.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"})})},21913:(e,t,n)=>{"use strict";function r(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}n.d(t,{WM:()=>xe});var i=n(98937),o=n(60331),s=n.n(o),c=n(51609),u=n.n(c);n(66911);const a=e=>"object"==typeof e&&null!=e&&1===e.nodeType,l=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,d=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const n=getComputedStyle(e,null);return l(n.overflowY,t)||l(n.overflowX,t)||(e=>{const t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},f=(e,t,n,r,i,o,s,c)=>o<e&&s>t||o>e&&s<t?0:o<=e&&c<=n||s>=t&&c>=n?o-e-r:s>t&&c<n||o<e&&c>n?s-t+i:0,p=e=>{const t=e.parentElement;return null==t?e.getRootNode().host||null:t};var h=n(95029),g=0;function m(){}function v(e,t,n){return e===t||t instanceof n.Node&&e.contains&&e.contains(t)}function y(e,t){var n;function r(){n&&clearTimeout(n)}function i(){for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];r(),n=setTimeout((function(){n=null,e.apply(void 0,o)}),t)}return i.cancel=r,i}function b(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t.some((function(t){return t&&t.apply(void 0,[e].concat(r)),e.preventDownshiftDefault||e.hasOwnProperty("nativeEvent")&&e.nativeEvent.preventDownshiftDefault}))}}function w(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){t.forEach((function(t){"function"==typeof t?t(e):t&&(t.current=e)}))}}function I(e,t){return e&&t?Object.keys(e).reduce((function(n,r){return n[r]=function(e,t){return void 0!==e[t]}(t,r)?t[r]:e[r],n}),{}):e}function O(e,t,n,r,i){void 0===i&&(i=!1);var o=n.length;if(0===o)return-1;var s=o-1;("number"!=typeof e||e<0||e>s)&&(e=t>0?-1:s+1);var c=e+t;c<0?c=i?s:0:c>s&&(c=i?0:s);var u=x(c,t<0,n,r,i);return-1===u?e>=o?-1:e:u}function x(e,t,n,r,i){void 0===i&&(i=!1);var o=n.length;if(t){for(var s=e;s>=0;s--)if(!r(n[s],s))return s}else for(var c=e;c<o;c++)if(!r(n[c],c))return c;return i?x(t?o-1:0,t,n,r):-1}function C(e,t,n,r){return void 0===r&&(r=!0),n&&t.some((function(t){return t&&(v(t,e,n)||r&&v(t,n.document.activeElement,n))}))}var S=y((function(e){E(e).textContent=""}),500);function E(e){var t=e.getElementById("a11y-status-message");return t||((t=e.createElement("div")).setAttribute("id","a11y-status-message"),t.setAttribute("role","status"),t.setAttribute("aria-live","polite"),t.setAttribute("aria-relevant","additions text"),Object.assign(t.style,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px"}),e.body.appendChild(t),t)}var k={highlightedIndex:-1,isOpen:!1,selectedItem:null,inputValue:""};var T=y((function(e,t){!function(e,t){e&&t&&(E(t).textContent=e,S(t))}(e,t)}),200),M="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?c.useLayoutEffect:c.useEffect,j="useId"in u()?function(e){var t=e.id,n=e.labelId,r=e.menuId,i=e.getItemId,o=e.toggleButtonId,s=e.inputId,a="downshift-"+u().useId();return t||(t=a),(0,c.useRef)({labelId:n||t+"-label",menuId:r||t+"-menu",getItemId:i||function(e){return t+"-item-"+e},toggleButtonId:o||t+"-toggle-button",inputId:s||t+"-input"}).current}:function(e){var t=e.id,n=void 0===t?"downshift-"+String(g++):t,r=e.labelId,i=e.menuId,o=e.getItemId,s=e.toggleButtonId,u=e.inputId;return(0,c.useRef)({labelId:r||n+"-label",menuId:i||n+"-menu",getItemId:o||function(e){return n+"-item-"+e},toggleButtonId:s||n+"-toggle-button",inputId:u||n+"-input"}).current};function D(e){return""+e.slice(0,1).toUpperCase()+e.slice(1)}function A(e){var t=(0,c.useRef)(e);return t.current=e,t}function P(e,t,n,r){var o=(0,c.useRef)(),s=(0,c.useRef)(),u=(0,c.useCallback)((function(t,n){s.current=n,t=I(t,n.props);var r=e(t,n);return n.props.stateReducer(t,(0,i.A)({},n,{changes:r}))}),[e]),a=(0,c.useReducer)(u,t,n),l=a[0],d=a[1],f=A(t),p=(0,c.useCallback)((function(e){return d((0,i.A)({props:f.current},e))}),[f]),h=s.current;return(0,c.useEffect)((function(){var e=I(o.current,null==h?void 0:h.props);h&&o.current&&!r(e,l)&&function(e,t,n){var r=e.props,o=e.type,s={};Object.keys(t).forEach((function(r){!function(e,t,n,r){var o=t.props,s=t.type,c="on"+D(e)+"Change";o[c]&&void 0!==r[e]&&r[e]!==n[e]&&o[c]((0,i.A)({type:s},r))}(r,e,t,n),n[r]!==t[r]&&(s[r]=n[r])})),r.onStateChange&&Object.keys(s).length&&r.onStateChange((0,i.A)({type:o},s))}(h,e,l),o.current=l}),[l,h,r]),[l,p]}var R={itemToString:function(e){return e?String(e):""},itemToKey:function(e){return e},stateReducer:function(e,t){return t.changes},scrollIntoView:function(e,t){if(e){var n=((e,t)=>{var n,r,i,o;if("undefined"==typeof document)return[];const{scrollMode:s,block:c,inline:u,boundary:l,skipOverflowHiddenElements:h}=t,g="function"==typeof l?l:e=>e!==l;if(!a(e))throw new TypeError("Invalid target");const m=document.scrollingElement||document.documentElement,v=[];let y=e;for(;a(y)&&g(y);){if(y=p(y),y===m){v.push(y);break}null!=y&&y===document.body&&d(y)&&!d(document.documentElement)||null!=y&&d(y,h)&&v.push(y)}const b=null!=(r=null==(n=window.visualViewport)?void 0:n.width)?r:innerWidth,w=null!=(o=null==(i=window.visualViewport)?void 0:i.height)?o:innerHeight,{scrollX:I,scrollY:O}=window,{height:x,width:C,top:S,right:E,bottom:k,left:T}=e.getBoundingClientRect(),{top:M,right:j,bottom:D,left:A}=(e=>{const t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);let P="start"===c||"nearest"===c?S-M:"end"===c?k+D:S+x/2-M+D,R="center"===u?T+C/2-A+j:"end"===u?E+j:T-A;const B=[];for(let e=0;e<v.length;e++){const t=v[e],{height:n,width:r,top:i,right:o,bottom:a,left:l}=t.getBoundingClientRect();if("if-needed"===s&&S>=0&&T>=0&&k<=w&&E<=b&&S>=i&&k<=a&&T>=l&&E<=o)return B;const d=getComputedStyle(t),p=parseInt(d.borderLeftWidth,10),h=parseInt(d.borderTopWidth,10),g=parseInt(d.borderRightWidth,10),y=parseInt(d.borderBottomWidth,10);let M=0,j=0;const D="offsetWidth"in t?t.offsetWidth-t.clientWidth-p-g:0,A="offsetHeight"in t?t.offsetHeight-t.clientHeight-h-y:0,L="offsetWidth"in t?0===t.offsetWidth?0:r/t.offsetWidth:0,V="offsetHeight"in t?0===t.offsetHeight?0:n/t.offsetHeight:0;if(m===t)M="start"===c?P:"end"===c?P-w:"nearest"===c?f(O,O+w,w,h,y,O+P,O+P+x,x):P-w/2,j="start"===u?R:"center"===u?R-b/2:"end"===u?R-b:f(I,I+b,b,p,g,I+R,I+R+C,C),M=Math.max(0,M+O),j=Math.max(0,j+I);else{M="start"===c?P-i-h:"end"===c?P-a+y+A:"nearest"===c?f(i,a,n,h,y+A,P,P+x,x):P-(i+n/2)+A/2,j="start"===u?R-l-p:"center"===u?R-(l+r/2)+D/2:"end"===u?R-o+g+D:f(l,o,r,p,g+D,R,R+C,C);const{scrollLeft:e,scrollTop:s}=t;M=0===V?0:Math.max(0,Math.min(s+M/V,t.scrollHeight-n/V+A)),j=0===L?0:Math.max(0,Math.min(e+j/L,t.scrollWidth-r/L+D)),P+=s-M,R+=e-j}B.push({el:t,top:M,left:j})}return B})(e,{boundary:t,block:"nearest",scrollMode:"if-needed"});n.forEach((function(e){var t=e.el,n=e.top,r=e.left;t.scrollTop=n,t.scrollLeft=r}))}},environment:"undefined"==typeof window?void 0:window};function B(e,t,n){void 0===n&&(n=k);var r=e["default"+D(t)];return void 0!==r?r:n[t]}function L(e,t,n){void 0===n&&(n=k);var r=e[t];if(void 0!==r)return r;var i=e["initial"+D(t)];return void 0!==i?i:B(e,t,n)}function V(e){var t=L(e,"selectedItem"),n=L(e,"isOpen"),r=function(e){var t=L(e,"highlightedIndex");return t>-1&&e.isItemDisabled(e.items[t],t)?-1:t}(e),i=L(e,"inputValue");return{highlightedIndex:r<0&&t&&n?e.items.findIndex((function(n){return e.itemToKey(n)===e.itemToKey(t)})):r,isOpen:n,selectedItem:t,inputValue:i}}function K(e,t,n){var r=e.items,i=e.initialHighlightedIndex,o=e.defaultHighlightedIndex,s=e.isItemDisabled,c=e.itemToKey,u=t.selectedItem,a=t.highlightedIndex;return 0===r.length?-1:void 0===i||a!==i||s(r[i],i)?void 0===o||s(r[o],o)?u?r.findIndex((function(e){return c(u)===c(e)})):n<0&&!s(r[r.length-1],r.length-1)?r.length-1:n>0&&!s(r[0],0)?0:-1:o:i}var F=function(){return m};function _(e,t,n,r){void 0===r&&(r={});var i,o=r.document,s=(i=u().useRef(!0),u().useEffect((function(){return i.current=!1,function(){i.current=!0}}),[]),i.current);(0,c.useEffect)((function(){if(e&&!s&&o){var n=e(t);T(n,o)}}),n),(0,c.useEffect)((function(){return function(){var e,t;T.cancel(),(t=null==(e=o)?void 0:e.getElementById("a11y-status-message"))&&t.remove()}}),[o])}var H=m;function z(e,t,n){var r;void 0===n&&(n=!0);var o=(null==(r=e.items)?void 0:r.length)&&t>=0;return(0,i.A)({isOpen:!1,highlightedIndex:-1},o&&(0,i.A)({selectedItem:e.items[t],isOpen:B(e,"isOpen"),highlightedIndex:B(e,"highlightedIndex")},n&&{inputValue:e.itemToString(e.items[t])}))}function W(e,t){return e.isOpen===t.isOpen&&e.inputValue===t.inputValue&&e.highlightedIndex===t.highlightedIndex&&e.selectedItem===t.selectedItem}function U(e){var t=B(e,"highlightedIndex");return t>-1&&e.isItemDisabled(e.items[t],t)?-1:t}var N={environment:s().shape({addEventListener:s().func.isRequired,removeEventListener:s().func.isRequired,document:s().shape({createElement:s().func.isRequired,getElementById:s().func.isRequired,activeElement:s().any.isRequired,body:s().any.isRequired}).isRequired,Node:s().func.isRequired}),itemToString:s().func,itemToKey:s().func,stateReducer:s().func},q=(0,i.A)({},N,{getA11yStatusMessage:s().func,highlightedIndex:s().number,defaultHighlightedIndex:s().number,initialHighlightedIndex:s().number,isOpen:s().bool,defaultIsOpen:s().bool,initialIsOpen:s().bool,selectedItem:s().any,initialSelectedItem:s().any,defaultSelectedItem:s().any,id:s().string,labelId:s().string,menuId:s().string,getItemId:s().func,toggleButtonId:s().string,onSelectedItemChange:s().func,onHighlightedIndexChange:s().func,onStateChange:s().func,onIsOpenChange:s().func,scrollIntoView:s().func});(0,h.Cl)((0,h.Cl)({},q),{items:s().array.isRequired,isItemDisabled:s().func});var J=(0,h.Cl)((0,h.Cl)({},R),{isItemDisabled:function(){return!1}}),$=m,G=0,Y=1,X=2,Q=3,Z=4,ee=5,te=6,ne=7,re=8,ie=9,oe=10,se=11,ce=12,ue=13,ae=14,le=15,de=16,fe=17,pe=18,he=19,ge=20,me=21,ve=Object.freeze({__proto__:null,FunctionCloseMenu:fe,FunctionOpenMenu:de,FunctionReset:me,FunctionSelectItem:he,FunctionSetHighlightedIndex:pe,FunctionSetInputValue:ge,FunctionToggleMenu:le,ItemClick:ae,ItemMouseMove:ue,MenuMouseLeave:ce,ToggleButtonBlur:se,ToggleButtonClick:G,ToggleButtonKeyDownArrowDown:Y,ToggleButtonKeyDownArrowUp:X,ToggleButtonKeyDownCharacter:Q,ToggleButtonKeyDownEnd:te,ToggleButtonKeyDownEnter:ne,ToggleButtonKeyDownEscape:Z,ToggleButtonKeyDownHome:ee,ToggleButtonKeyDownPageDown:oe,ToggleButtonKeyDownPageUp:ie,ToggleButtonKeyDownSpaceButton:re});function ye(e,t){var n,r,o=t.type,s=t.props,c=t.altKey;switch(o){case ae:r={isOpen:B(s,"isOpen"),highlightedIndex:U(s),selectedItem:s.items[t.index]};break;case Q:var u=t.key,a=""+e.inputValue+u;r={inputValue:a,highlightedIndex:function(e){for(var t=e.keysSoFar,n=e.highlightedIndex,r=e.items,i=e.itemToString,o=e.isItemDisabled,s=t.toLowerCase(),c=0;c<r.length;c++){var u=(c+n+(t.length<2?1:0))%r.length,a=r[u];if(void 0!==a&&i(a).toLowerCase().startsWith(s)&&!o(a,u))return u}return n}({keysSoFar:a,highlightedIndex:!e.isOpen&&e.selectedItem?s.items.findIndex((function(t){return s.itemToKey(t)===s.itemToKey(e.selectedItem)})):e.highlightedIndex,items:s.items,itemToString:s.itemToString,isItemDisabled:s.isItemDisabled}),isOpen:!0};break;case Y:r={highlightedIndex:e.isOpen?O(e.highlightedIndex,1,s.items,s.isItemDisabled):c&&null==e.selectedItem?-1:K(s,e,1),isOpen:!0};break;case X:r=e.isOpen&&c?z(s,e.highlightedIndex,!1):{highlightedIndex:e.isOpen?O(e.highlightedIndex,-1,s.items,s.isItemDisabled):K(s,e,-1),isOpen:!0};break;case ne:case re:r=z(s,e.highlightedIndex,!1);break;case ee:r={highlightedIndex:x(0,!1,s.items,s.isItemDisabled),isOpen:!0};break;case te:r={highlightedIndex:x(s.items.length-1,!0,s.items,s.isItemDisabled),isOpen:!0};break;case ie:r={highlightedIndex:O(e.highlightedIndex,-10,s.items,s.isItemDisabled)};break;case oe:r={highlightedIndex:O(e.highlightedIndex,10,s.items,s.isItemDisabled)};break;case Z:r={isOpen:!1,highlightedIndex:-1};break;case se:r=(0,i.A)({isOpen:!1,highlightedIndex:-1},e.highlightedIndex>=0&&(null==(n=s.items)?void 0:n.length)&&{selectedItem:s.items[e.highlightedIndex]});break;case he:r={selectedItem:t.selectedItem};break;default:return function(e,t,n){var r,o=t.type,s=t.props;switch(o){case n.ItemMouseMove:r={highlightedIndex:t.disabled?-1:t.index};break;case n.MenuMouseLeave:r={highlightedIndex:-1};break;case n.ToggleButtonClick:case n.FunctionToggleMenu:r={isOpen:!e.isOpen,highlightedIndex:e.isOpen?-1:K(s,e,0)};break;case n.FunctionOpenMenu:r={isOpen:!0,highlightedIndex:K(s,e,0)};break;case n.FunctionCloseMenu:r={isOpen:!1};break;case n.FunctionSetHighlightedIndex:r={highlightedIndex:s.isItemDisabled(s.items[t.highlightedIndex],t.highlightedIndex)?-1:t.highlightedIndex};break;case n.FunctionSetInputValue:r={inputValue:t.inputValue};break;case n.FunctionReset:r={highlightedIndex:U(s),isOpen:B(s,"isOpen"),selectedItem:B(s,"selectedItem"),inputValue:B(s,"inputValue")};break;default:throw new Error("Reducer called without proper action type.")}return(0,i.A)({},e,r)}(e,t,ve)}return(0,i.A)({},e,r)}var be=["onClick"],we=["onMouseLeave","refKey","ref"],Ie=["onBlur","onClick","onPress","onKeyDown","refKey","ref"],Oe=["item","index","onMouseMove","onClick","onMouseDown","onPress","refKey","disabled","ref"];function xe(e){void 0===e&&(e={}),$(e,xe);var t=(0,i.A)({},J,e),n=t.scrollIntoView,o=t.environment,s=t.getA11yStatusMessage,u=function(e,t,n,r){var i=P(e,t,n,r),o=i[0],s=i[1];return[I(o,t),s]}(ye,t,V,W),a=u[0],l=u[1],d=a.isOpen,f=a.highlightedIndex,p=a.selectedItem,h=a.inputValue,g=(0,c.useRef)(null),v=(0,c.useRef)(null),O=(0,c.useRef)({}),x=(0,c.useRef)(null),S=j(t),E=A({state:a,props:t}),k=(0,c.useCallback)((function(e){return O.current[S.getItemId(e)]}),[S]);_(s,a,[d,f,p,h],o);var T=function(e){var t=e.highlightedIndex,n=e.isOpen,r=e.itemRefs,i=e.getItemNodeFromIndex,o=e.menuElement,s=e.scrollIntoView,u=(0,c.useRef)(!0);return M((function(){t<0||!n||!Object.keys(r.current).length||(!1===u.current?u.current=!0:s(i(t),o))}),[t]),u}({menuElement:v.current,highlightedIndex:f,isOpen:d,itemRefs:O,scrollIntoView:n,getItemNodeFromIndex:k});(0,c.useEffect)((function(){return x.current=y((function(e){e({type:ge,inputValue:""})}),500),function(){x.current.cancel()}}),[]),(0,c.useEffect)((function(){h&&x.current(l)}),[l,h]),H({props:t,state:a}),(0,c.useEffect)((function(){L(t,"isOpen")&&g.current&&g.current.focus()}),[]);var D=function(e,t,n){var r=(0,c.useRef)({isMouseDown:!1,isTouchMove:!1,isTouchEnd:!1});return(0,c.useEffect)((function(){if(!e)return m;var i=n.map((function(e){return e.current}));function o(){r.current.isTouchEnd=!1,r.current.isMouseDown=!0}function s(n){r.current.isMouseDown=!1,C(n.target,i,e)||t()}function c(){r.current.isTouchEnd=!1,r.current.isTouchMove=!1}function u(){r.current.isTouchMove=!0}function a(n){r.current.isTouchEnd=!0,r.current.isTouchMove||C(n.target,i,e,!1)||t()}return e.addEventListener("mousedown",o),e.addEventListener("mouseup",s),e.addEventListener("touchstart",c),e.addEventListener("touchmove",u),e.addEventListener("touchend",a),function(){e.removeEventListener("mousedown",o),e.removeEventListener("mouseup",s),e.removeEventListener("touchstart",c),e.removeEventListener("touchmove",u),e.removeEventListener("touchend",a)}}),[n,e,t]),r.current}(o,(0,c.useCallback)((function(){E.current.state.isOpen&&l({type:se})}),[l,E]),(0,c.useMemo)((function(){return[v,g]}),[v.current,g.current])),R=F("getMenuProps","getToggleButtonProps");(0,c.useEffect)((function(){d||(O.current={})}),[d]);var B=(0,c.useMemo)((function(){return{ArrowDown:function(e){e.preventDefault(),l({type:Y,altKey:e.altKey})},ArrowUp:function(e){e.preventDefault(),l({type:X,altKey:e.altKey})},Home:function(e){e.preventDefault(),l({type:ee})},End:function(e){e.preventDefault(),l({type:te})},Escape:function(){E.current.state.isOpen&&l({type:Z})},Enter:function(e){e.preventDefault(),l({type:E.current.state.isOpen?ne:G})},PageUp:function(e){E.current.state.isOpen&&(e.preventDefault(),l({type:ie}))},PageDown:function(e){E.current.state.isOpen&&(e.preventDefault(),l({type:oe}))}," ":function(e){e.preventDefault();var t=E.current.state;t.isOpen?t.inputValue?l({type:Q,key:" "}):l({type:re}):l({type:G})}}}),[l,E]),K=(0,c.useCallback)((function(){l({type:le})}),[l]),z=(0,c.useCallback)((function(){l({type:fe})}),[l]),U=(0,c.useCallback)((function(){l({type:de})}),[l]),N=(0,c.useCallback)((function(e){l({type:pe,highlightedIndex:e})}),[l]),q=(0,c.useCallback)((function(e){l({type:he,selectedItem:e})}),[l]),ve=(0,c.useCallback)((function(){l({type:me})}),[l]),Ce=(0,c.useCallback)((function(e){l({type:ge,inputValue:e})}),[l]),Se=(0,c.useCallback)((function(e){var t=void 0===e?{}:e,n=t.onClick,o=r(t,be);return(0,i.A)({id:S.labelId,htmlFor:S.toggleButtonId,onClick:b(n,(function(){var e;null==(e=g.current)||e.focus()}))},o)}),[S]),Ee=(0,c.useCallback)((function(e,t){var n,o=void 0===e?{}:e,s=o.onMouseLeave,c=o.refKey,u=void 0===c?"ref":c,a=o.ref,d=r(o,we),f=(void 0===t?{}:t).suppressRefError;return R("getMenuProps",void 0!==f&&f,u,v),(0,i.A)(((n={})[u]=w(a,(function(e){v.current=e})),n.id=S.menuId,n.role="listbox",n["aria-labelledby"]=d&&d["aria-label"]?void 0:""+S.labelId,n.onMouseLeave=b(s,(function(){l({type:ce})})),n),d)}),[l,R,S]),ke=(0,c.useCallback)((function(e,t){var n,o=void 0===e?{}:e,s=o.onBlur,c=o.onClick;o.onPress;var u=o.onKeyDown,a=o.refKey,d=void 0===a?"ref":a,f=o.ref,p=r(o,Ie),h=(void 0===t?{}:t).suppressRefError,m=void 0!==h&&h,v=E.current.state,y=(0,i.A)(((n={})[d]=w(f,(function(e){g.current=e})),n["aria-activedescendant"]=v.isOpen&&v.highlightedIndex>-1?S.getItemId(v.highlightedIndex):"",n["aria-controls"]=S.menuId,n["aria-expanded"]=E.current.state.isOpen,n["aria-haspopup"]="listbox",n["aria-labelledby"]=p&&p["aria-label"]?void 0:""+S.labelId,n.id=S.toggleButtonId,n.role="combobox",n.tabIndex=0,n.onBlur=b(s,(function(){v.isOpen&&!D.isMouseDown&&l({type:se})})),n),p);return p.disabled||(y.onClick=b(c,(function(){l({type:G})})),y.onKeyDown=b(u,(function(e){var t=function(e){var t=e.key,n=e.keyCode;return n>=37&&n<=40&&0!==t.indexOf("Arrow")?"Arrow"+t:t}(e);t&&B[t]?B[t](e):function(e){return/^\S{1}$/.test(e)}(t)&&l({type:Q,key:t})}))),R("getToggleButtonProps",m,d,g),y}),[l,S,E,D,R,B]),Te=(0,c.useCallback)((function(e){var t,n=void 0===e?{}:e,o=n.item,s=n.index,c=n.onMouseMove,u=n.onClick,a=n.onMouseDown;n.onPress;var d=n.refKey,f=void 0===d?"ref":d,p=n.disabled,h=n.ref,g=r(n,Oe);void 0!==p&&console.warn('Passing "disabled" as an argument to getItemProps is not supported anymore. Please use the isItemDisabled prop from useSelect.');var m=E.current,v=m.state,y=m.props,I=function(e,t,n,r){var i,o;if(void 0===e){if(void 0===t)throw new Error(r);i=n[t],o=t}else o=void 0===t?n.indexOf(e):t,i=e;return[i,o]}(o,s,y.items,"Pass either item or index to getItemProps!"),x=I[0],C=I[1],k=y.isItemDisabled(x,C),M=(0,i.A)(((t={})[f]=w(h,(function(e){e&&(O.current[S.getItemId(C)]=e)})),t["aria-disabled"]=k,t["aria-selected"]=x===v.selectedItem,t.id=S.getItemId(C),t.role="option",t),g);return k||(M.onClick=b(u,(function(){l({type:ae,index:C})}))),M.onMouseMove=b(c,(function(){D.isTouchEnd||C===v.highlightedIndex||(T.current=!1,l({type:ue,index:C,disabled:k}))})),M.onMouseDown=b(a,(function(e){return e.preventDefault()})),M}),[E,S,D,T,l]);return{getToggleButtonProps:ke,getLabelProps:Se,getMenuProps:Ee,getItemProps:Te,toggleMenu:K,openMenu:U,closeMenu:z,setHighlightedIndex:N,selectItem:q,reset:ve,setInputValue:Ce,highlightedIndex:f,isOpen:d,selectedItem:p,inputValue:h}}xe.stateChangeTypes=ve,(0,i.A)({},q,{items:s().array.isRequired,isItemDisabled:s().func,inputValue:s().string,defaultInputValue:s().string,initialInputValue:s().string,inputId:s().string,onInputValueChange:s().func}),(0,i.A)({},R,{isItemDisabled:function(){return!1}}),s().array,s().array,s().array,s().func,s().number,s().number,s().number,s().func,s().func,s().string,s().string},8181:(e,t,n)=>{"use strict";t.A=function(e){var t=e.size,n=void 0===t?24:t,r=e.onClick,c=(e.icon,e.className),u=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],0<=t.indexOf(n)||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,o),a=["gridicon","gridicons-cross-small",c,!1,!1,!1].filter(Boolean).join(" ");return i.default.createElement("svg",s({className:a,height:n,width:n,onClick:r},u,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),i.default.createElement("g",null,i.default.createElement("path",{d:"M17.705 7.705l-1.41-1.41L12 10.59 7.705 6.295l-1.41 1.41L10.59 12l-4.295 4.295 1.41 1.41L12 13.41l4.295 4.295 1.41-1.41L13.41 12l4.295-4.295z"})))};var r,i=(r=n(51609))&&r.__esModule?r:{default:r},o=["size","onClick","icon","className"];function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},s.apply(this,arguments)}},46608:(e,t,n)=>{"use strict";t.A=function(e){var t=e.size,n=void 0===t?24:t,r=e.onClick,c=(e.icon,e.className),u=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],0<=t.indexOf(n)||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,o),a=["gridicon","gridicons-info-outline",c,!!function(e){return 0==e%18}(n)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return i.default.createElement("svg",s({className:a,height:n,width:n,onClick:r},u,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),i.default.createElement("g",null,i.default.createElement("path",{d:"M13 9h-2V7h2v2zm0 2h-2v6h2v-6zm-1-7c-4.411 0-8 3.589-8 8s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8m0-2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"})))};var r,i=(r=n(51609))&&r.__esModule?r:{default:r},o=["size","onClick","icon","className"];function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},s.apply(this,arguments)}},92991:(e,t,n)=>{"use strict";t.A=function(e){var t=e.size,n=void 0===t?24:t,r=e.onClick,c=(e.icon,e.className),u=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],0<=t.indexOf(n)||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,o),a=["gridicon","gridicons-notice",c,!!function(e){return 0==e%18}(n)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return i.default.createElement("svg",s({className:a,height:n,width:n,onClick:r},u,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),i.default.createElement("g",null,i.default.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 15h-2v-2h2v2zm0-4h-2l-.5-6h3l-.5 6z"})))};var r,i=(r=n(51609))&&r.__esModule?r:{default:r},o=["size","onClick","icon","className"];function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},s.apply(this,arguments)}},40055:(e,t,n)=>{"use strict";var r=n(42528);function i(){}function o(){}o.resetWarningCache=i,e.exports=function(){function e(e,t,n,i,o,s){if(s!==r){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:i};return n.PropTypes=n,n}},60331:(e,t,n)=>{e.exports=n(40055)()},42528:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},5387:(e,t)=>{"use strict";Symbol.for("react.element"),Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.server_context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.suspense_list"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.for("react.offscreen");Symbol.for("react.module.reference")},66911:(e,t,n)=>{"use strict";n(5387)}}]);