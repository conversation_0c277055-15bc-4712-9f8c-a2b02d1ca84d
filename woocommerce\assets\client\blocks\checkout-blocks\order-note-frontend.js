"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[552],{4855:(e,o,s)=>{s.r(o),s.d(o,{default:()=>i});var r=s(4921),c=s(7723),t=s(4656),a=s(6473),n=s(7143),d=s(7594),l=s(6087),u=s(790);const h=({disabled:e,onChange:o,placeholder:s,value:r})=>{const[a,n]=(0,l.useState)(""!==r),[d,h]=(0,l.useState)("");return(0,u.jsxs)("div",{className:"wc-block-checkout__add-note",children:[(0,u.jsx)(t.CheckboxControl,{disabled:e,label:(0,c.__)("Add a note to your order","woocommerce"),checked:a,onChange:e=>{n(e),e?r!==d&&o(d):(o(""),h(r))}}),a&&(0,u.jsx)(t.Textarea,{disabled:e,onTextChange:o,placeholder:s,value:r})]})},i=({className:e})=>{const{needsShipping:o}=(0,a.m)(),{isProcessing:s,orderNotes:l}=(0,n.useSelect)((e=>{const o=e(d.checkoutStore);return{isProcessing:o.isProcessing(),orderNotes:o.getOrderNotes()}})),{__internalSetOrderNotes:i}=(0,n.useDispatch)(d.checkoutStore);return(0,u.jsx)(t.FormStep,{id:"order-notes",showStepNumber:!1,className:(0,r.A)("wc-block-checkout__order-notes",e),disabled:s,children:(0,u.jsx)(h,{disabled:s,onChange:i,placeholder:o?(0,c.__)("Notes about your order, e.g. special notes for delivery.","woocommerce"):(0,c.__)("Notes about your order.","woocommerce"),value:l})})}}}]);