"use strict";var __importDefault=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.Edit=Edit;const element_1=require("@wordpress/element"),components_1=require("@wordpress/components"),block_templates_1=require("@woocommerce/block-templates"),tracks_1=require("@woocommerce/tracks"),core_data_1=require("@wordpress/core-data"),sanitize_html_1=require("../../../utils/sanitize-html"),use_product_entity_prop_1=__importDefault(require("../../../hooks/use-product-entity-prop")),constants_1=require("../../../constants");function Edit({attributes:e,context:{postType:t}}){const o=(0,block_templates_1.useWooBlockProps)(e),{_templateBlockId:r,label:l,property:n,disabled:_,disabledCopy:c,checkedValue:s,uncheckedValue:i}=e,[a,d]=(0,use_product_entity_prop_1.default)(n,{postType:t,fallbackValue:!1}),p=(0,core_data_1.useEntityId)("postType",t),[u]=(0,core_data_1.useEntityProp)("postType",t,"parent_id");function m(){return void 0!==s?s===a:a}let h=null;return e?.help&&(h=(0,element_1.createElement)("div",{dangerouslySetInnerHTML:{__html:(0,sanitize_html_1.sanitizeHTML)(e.help)?.__html}})),e?.checkedHelp&&m()&&(h=(0,element_1.createElement)("div",{dangerouslySetInnerHTML:{__html:(0,sanitize_html_1.sanitizeHTML)(e.checkedHelp)?.__html}})),e?.uncheckedHelp&&!m()&&(h=(0,element_1.createElement)("div",{dangerouslySetInnerHTML:{__html:(0,sanitize_html_1.sanitizeHTML)(e.uncheckedHelp)?.__html}})),(0,element_1.createElement)("div",{...o},(0,element_1.createElement)(components_1.ToggleControl,{label:l,checked:m(),disabled:_,onChange:function(e){(0,tracks_1.recordEvent)("product_toggle_click",{block_id:r,source:constants_1.TRACKS_SOURCE,product_id:u>0?u:p}),d(e?void 0!==s?s:e:void 0!==i?i:e)},help:h}),_&&(0,element_1.createElement)("p",{className:"wp-block-woocommerce-product-toggle__disable-copy",dangerouslySetInnerHTML:(0,sanitize_html_1.sanitizeHTML)(c)}))}