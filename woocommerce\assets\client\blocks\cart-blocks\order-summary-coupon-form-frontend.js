"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[452],{8965:(n,o,s)=>{s.r(o),s.d(o,{default:()=>l});var a=s(5486),c=s(5954),e=s(5703),t=s(4656),p=s(790);const l=({className:n})=>{const o=(0,e.getSetting)("couponsEnabled",!0),{applyCoupon:s,isApplyingCoupon:l}=(0,c.k)("wc/cart");return o?(0,p.jsx)(t.TotalsWrapper,{className:n,children:(0,p.jsx)(a._i,{onSubmit:s,isLoading:l,instanceId:"coupon"})}):null}}}]);