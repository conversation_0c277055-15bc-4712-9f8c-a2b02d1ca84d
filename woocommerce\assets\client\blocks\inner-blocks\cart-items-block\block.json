{"name": "woocommerce/cart-items-block", "version": "1.0.0", "title": "Cart Items", "description": "Column containing cart items.", "category": "woocommerce", "supports": {"align": false, "html": false, "multiple": false, "reusable": false, "inserter": false, "lock": false}, "attributes": {"lock": {"type": "object", "default": {"remove": true, "move": true}}}, "parent": ["woocommerce/filled-cart-block"], "textdomain": "woocommerce", "$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3}