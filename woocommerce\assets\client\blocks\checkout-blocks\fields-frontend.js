"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[8330],{1259:(c,e,o)=>{o.r(e),o.d(e,{default:()=>l});var s=o(4921),t=o(7723),r=o(2902),a=o(7052),k=o(6087),n=o(4199),h=o(790);const l=({children:c,className:e})=>{const{dispatchCheckoutEvent:o}=(0,a.y)(),{showFormStepNumbers:l}=(0,n.O)();return(0,k.useEffect)((()=>{o("render-checkout-form")}),[]),(0,h.jsx)(r.A,{className:(0,s.A)("wc-block-checkout__main",e),children:(0,h.jsx)("form",{"aria-label":(0,t.__)("Checkout","woocommerce"),className:(0,s.A)("wc-block-components-form wc-block-checkout__form",{"wc-block-checkout__form--with-step-numbers":l}),children:c})})}}}]);