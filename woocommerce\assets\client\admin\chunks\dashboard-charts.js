"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[1133],{620:(e,o,r)=>{r.d(o,{Qc:()=>i,eg:()=>m,uW:()=>_});var t=r(27723),c=r(52619),a=r(47143),l=r(27752),s=r(33958);const{addCesSurveyForAnalytics:n}=(0,a.dispatch)(l.STORE_KEY),m=(0,c.applyFilters)("woocommerce_admin_coupons_report_charts",[{key:"orders_count",label:(0,t.__)("Discounted orders","woocommerce"),order:"desc",orderby:"orders_count",type:"number"},{key:"amount",label:(0,t.__)("Amount","woocommerce"),order:"desc",orderby:"amount",type:"currency"}]),i=(0,c.applyFilters)("woocommerce_admin_coupon_report_advanced_filters",{filters:{},title:(0,t._x)("Coupons match <select/> filters","A sentence describing filters for Coupons. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")}),d=[{label:(0,t.__)("All coupons","woocommerce"),value:"all"},{label:(0,t.__)("Single coupon","woocommerce"),value:"select_coupon",chartMode:"item-comparison",subFilters:[{component:"Search",value:"single_coupon",chartMode:"item-comparison",path:["select_coupon"],settings:{type:"coupons",param:"coupons",getLabels:s.U4,labels:{placeholder:(0,t.__)("Type to search for a coupon","woocommerce"),button:(0,t.__)("Single Coupon","woocommerce")}}}]},{label:(0,t.__)("Comparison","woocommerce"),value:"compare-coupons",settings:{type:"coupons",param:"coupons",getLabels:s.U4,labels:{title:(0,t.__)("Compare Coupon Codes","woocommerce"),update:(0,t.__)("Compare","woocommerce"),helpText:(0,t.__)("Check at least two coupon codes below to compare","woocommerce")},onClick:n}}];Object.keys(i.filters).length&&d.push({label:(0,t.__)("Advanced filters","woocommerce"),value:"advanced"});const _=(0,c.applyFilters)("woocommerce_admin_coupons_report_filters",[{label:(0,t.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:d}])},80170:(e,o,r)=>{r.d(o,{Qc:()=>n,eg:()=>l,uW:()=>s});var t=r(27723),c=r(52619),a=r(33958);const l=(0,c.applyFilters)("woocommerce_admin_downloads_report_charts",[{key:"download_count",label:(0,t.__)("Downloads","woocommerce"),type:"number"}]),s=(0,c.applyFilters)("woocommerce_admin_downloads_report_filters",[{label:(0,t.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,t.__)("All downloads","woocommerce"),value:"all"},{label:(0,t.__)("Advanced filters","woocommerce"),value:"advanced"}]}]),n=(0,c.applyFilters)("woocommerce_admin_downloads_report_advanced_filters",{title:(0,t._x)("Downloads match <select/> filters","A sentence describing filters for Downloads. See screen shot for context: https://cloudup.com/ccxhyH2mEDg","woocommerce"),filters:{product:{labels:{add:(0,t.__)("Product","woocommerce"),placeholder:(0,t.__)("Search","woocommerce"),remove:(0,t.__)("Remove product filter","woocommerce"),rule:(0,t.__)("Select a product filter match","woocommerce"),title:(0,t.__)("<title>Product</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select product","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","products","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","products","woocommerce")}],input:{component:"Search",type:"products",getLabels:a.p0}},customer:{labels:{add:(0,t.__)("Username","woocommerce"),placeholder:(0,t.__)("Search customer username","woocommerce"),remove:(0,t.__)("Remove customer username filter","woocommerce"),rule:(0,t.__)("Select a customer username filter match","woocommerce"),title:(0,t.__)("<title>Username</title> <rule/> <filter />","woocommerce"),filter:(0,t.__)("Select customer username","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","customer usernames","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","customer usernames","woocommerce")}],input:{component:"Search",type:"usernames",getLabels:a.wd}},order:{labels:{add:(0,t.__)("Order #","woocommerce"),placeholder:(0,t.__)("Search order number","woocommerce"),remove:(0,t.__)("Remove order number filter","woocommerce"),rule:(0,t.__)("Select an order number filter match","woocommerce"),title:(0,t.__)("<title>Order #</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select order number","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","order numbers","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","order numbers","woocommerce")}],input:{component:"Search",type:"orders",getLabels:async e=>{const o=e.split(",");return await o.map((e=>({id:e,label:"#"+e})))}}},ip_address:{labels:{add:(0,t.__)("IP Address","woocommerce"),placeholder:(0,t.__)("Search IP address","woocommerce"),remove:(0,t.__)("Remove IP address filter","woocommerce"),rule:(0,t.__)("Select an IP address filter match","woocommerce"),title:(0,t.__)("<title>IP Address</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select IP address","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","IP addresses","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","IP addresses","woocommerce")}],input:{component:"Search",type:"downloadIps",getLabels:async e=>{const o=e.split(",");return await o.map((e=>({id:e,label:e})))}}}}})},95519:(e,o,r)=>{r.d(o,{Qc:()=>_,eg:()=>m,uW:()=>p});var t=r(27723),c=r(52619),a=r(47143),l=r(27752),s=r(33958);const{addCesSurveyForAnalytics:n}=(0,a.dispatch)(l.STORE_KEY),m=(0,c.applyFilters)("woocommerce_admin_products_report_charts",[{key:"items_sold",label:(0,t.__)("Items sold","woocommerce"),order:"desc",orderby:"items_sold",type:"number"},{key:"net_revenue",label:(0,t.__)("Net sales","woocommerce"),order:"desc",orderby:"net_revenue",type:"currency"},{key:"orders_count",label:(0,t.__)("Orders","woocommerce"),order:"desc",orderby:"orders_count",type:"number"}]),i={label:(0,t.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,t.__)("All products","woocommerce"),value:"all"},{label:(0,t.__)("Single product","woocommerce"),value:"select_product",chartMode:"item-comparison",subFilters:[{component:"Search",value:"single_product",chartMode:"item-comparison",path:["select_product"],settings:{type:"products",param:"products",getLabels:s.p0,labels:{placeholder:(0,t.__)("Type to search for a product","woocommerce"),button:(0,t.__)("Single product","woocommerce")}}}]},{label:(0,t.__)("Comparison","woocommerce"),value:"compare-products",chartMode:"item-comparison",settings:{type:"products",param:"products",getLabels:s.p0,labels:{helpText:(0,t.__)("Check at least two products below to compare","woocommerce"),placeholder:(0,t.__)("Search for products to compare","woocommerce"),title:(0,t.__)("Compare Products","woocommerce"),update:(0,t.__)("Compare","woocommerce")},onClick:n}}]},d={showFilters:e=>"single_product"===e.filter&&!!e.products&&e["is-variable"],staticParams:["filter","products","chartType","paged","per_page"],param:"filter-variations",filters:[{label:(0,t.__)("All variations","woocommerce"),chartMode:"item-comparison",value:"all"},{label:(0,t.__)("Single variation","woocommerce"),value:"select_variation",subFilters:[{component:"Search",value:"single_variation",path:["select_variation"],settings:{type:"variations",param:"variations",getLabels:s.b8,labels:{placeholder:(0,t.__)("Type to search for a variation","woocommerce"),button:(0,t.__)("Single variation","woocommerce")}}}]},{label:(0,t.__)("Comparison","woocommerce"),chartMode:"item-comparison",value:"compare-variations",settings:{type:"variations",param:"variations",getLabels:s.b8,labels:{helpText:(0,t.__)("Check at least two variations below to compare","woocommerce"),placeholder:(0,t.__)("Search for variations to compare","woocommerce"),title:(0,t.__)("Compare Variations","woocommerce"),update:(0,t.__)("Compare","woocommerce")}}}]},_=(0,c.applyFilters)("woocommerce_admin_products_report_advanced_filters",{filters:{},title:(0,t._x)("Products Match <select/> Filters","A sentence describing filters for Products. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")});Object.keys(_.filters).length&&(i.filters.push({label:(0,t.__)("Advanced Filters","woocommerce"),value:"advanced"}),d.filters.push({label:(0,t.__)("Advanced Filters","woocommerce"),value:"advanced"}));const p=(0,c.applyFilters)("woocommerce_admin_products_report_filters",[i,d])},94693:(e,o,r)=>{r.d(o,{Qc:()=>l,eg:()=>a,uW:()=>n});var t=r(27723),c=r(52619);const a=(0,c.applyFilters)("woocommerce_admin_revenue_report_charts",[{key:"gross_sales",label:(0,t.__)("Gross sales","woocommerce"),order:"desc",orderby:"gross_sales",type:"currency",isReverseTrend:!1},{key:"refunds",label:(0,t.__)("Returns","woocommerce"),order:"desc",orderby:"refunds",type:"currency",isReverseTrend:!0},{key:"coupons",label:(0,t.__)("Coupons","woocommerce"),order:"desc",orderby:"coupons",type:"currency",isReverseTrend:!1},{key:"net_revenue",label:(0,t.__)("Net sales","woocommerce"),orderby:"net_revenue",type:"currency",isReverseTrend:!1,labelTooltipText:(0,t.__)("Full refunds are not deducted from tax or net sales totals","woocommerce")},{key:"taxes",label:(0,t.__)("Taxes","woocommerce"),order:"desc",orderby:"taxes",type:"currency",isReverseTrend:!1,labelTooltipText:(0,t.__)("Full refunds are not deducted from tax or net sales totals","woocommerce")},{key:"shipping",label:(0,t.__)("Shipping","woocommerce"),orderby:"shipping",type:"currency",isReverseTrend:!1},{key:"total_sales",label:(0,t.__)("Total sales","woocommerce"),order:"desc",orderby:"total_sales",type:"currency",isReverseTrend:!1}]),l=(0,c.applyFilters)("woocommerce_admin_revenue_report_advanced_filters",{filters:{},title:(0,t._x)("Revenue Matches <select/> Filters","A sentence describing filters for Revenue. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")}),s=[];Object.keys(l.filters).length&&(s.push({label:(0,t.__)("All Revenue","woocommerce"),value:"all"}),s.push({label:(0,t.__)("Advanced Filters","woocommerce"),value:"advanced"}));const n=(0,c.applyFilters)("woocommerce_admin_revenue_report_filters",[{label:(0,t.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>s.length>0,filters:s}])},9622:(e,o,r)=>{r.d(o,{Qc:()=>_,eg:()=>d,uW:()=>u});var t=r(27723),c=r(52619),a=r(27752),l=r(40314),s=r(47143),n=r(33958),m=r(32639);const{addCesSurveyForAnalytics:i}=(0,s.dispatch)(a.STORE_KEY),d=(0,c.applyFilters)("woocommerce_admin_taxes_report_charts",[{key:"total_tax",label:(0,t.__)("Total tax","woocommerce"),order:"desc",orderby:"total_tax",type:"currency"},{key:"order_tax",label:(0,t.__)("Order tax","woocommerce"),order:"desc",orderby:"order_tax",type:"currency"},{key:"shipping_tax",label:(0,t.__)("Shipping tax","woocommerce"),order:"desc",orderby:"shipping_tax",type:"currency"},{key:"orders_count",label:(0,t.__)("Orders","woocommerce"),order:"desc",orderby:"orders_count",type:"number"}]),_=(0,c.applyFilters)("woocommerce_admin_taxes_report_advanced_filters",{filters:{},title:(0,t._x)("Taxes match <select/> filters","A sentence describing filters for Taxes. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")}),p=[{label:(0,t.__)("All taxes","woocommerce"),value:"all"},{label:(0,t.__)("Comparison","woocommerce"),value:"compare-taxes",chartMode:"item-comparison",settings:{type:"taxes",param:"taxes",getLabels:(0,n.Dn)(l.NAMESPACE+"/taxes",(e=>({id:e.id,key:e.id,label:(0,m.H)(e)}))),labels:{helpText:(0,t.__)("Check at least two tax codes below to compare","woocommerce"),placeholder:(0,t.__)("Search for tax codes to compare","woocommerce"),title:(0,t.__)("Compare Tax Codes","woocommerce"),update:(0,t.__)("Compare","woocommerce")},onClick:i}}];Object.keys(_.filters).length&&p.push({label:(0,t.__)("Advanced filters","woocommerce"),value:"advanced"});const u=(0,c.applyFilters)("woocommerce_admin_taxes_report_filters",[{label:(0,t.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:p}])},31861:(e,o,r)=>{r.r(o),r.d(o,{default:()=>E});var t=r(27723),c=r(4921),a=r(86087),l=r(92272),s=r(9294),n=r(56427),m=r(98846),i=r(40314),d=r(77374),_=r(83306),p=r(96476),u=r(15703),h=r(14908),w=r(55737),b=r(39793);class y extends a.Component{handleChartClick=()=>{const{selectedChart:e}=this.props;(0,p.getHistory)().push(this.getChartPath(e))};getChartPath(e){return(0,p.getNewPath)({chart:e.key},"/analytics/"+e.endpoint,(0,p.getPersistedQuery)())}render(){const{charts:e,endpoint:o,path:r,query:c,selectedChart:a,filters:l}=this.props;return a?(0,b.jsx)("div",{role:"presentation",className:"woocommerce-dashboard__chart-block-wrapper",onClick:this.handleChartClick,children:(0,b.jsxs)(n.Card,{className:"woocommerce-dashboard__chart-block",children:[(0,b.jsx)(n.CardHeader,{children:(0,b.jsx)(h.Text,{as:"h3",size:16,weight:600,color:"#23282d",children:a.label})}),(0,b.jsxs)(n.CardBody,{size:null,children:[(0,b.jsx)("a",{className:"screen-reader-text",href:(0,u.getAdminLink)(this.getChartPath(a)),children:(0,t.sprintf)((0,t.__)("%s Report","woocommerce"),a.label)}),(0,b.jsx)(w.A,{charts:e,endpoint:o,query:c,interactiveLegend:!1,legendPosition:"bottom",path:r,selectedChart:a,showHeaderControls:!1,filters:l})]})]})}):null}}const v=y;var f=r(52619),g=r(13560),x=r(95519),k=r(94693),C=r(620),S=r(9622),T=r(80170);const j={revenue:k.eg,orders:g.eg,products:x.eg,coupons:C.eg,taxes:S.eg,downloads:T.eg},F=[{label:(0,t.__)("Total sales","woocommerce"),report:"revenue",key:"total_sales"},{label:(0,t.__)("Net sales","woocommerce"),report:"revenue",key:"net_revenue"},{label:(0,t.__)("Orders","woocommerce"),report:"orders",key:"orders_count"},{label:(0,t.__)("Average order value","woocommerce"),report:"orders",key:"avg_order_value"},{label:(0,t.__)("Items sold","woocommerce"),report:"products",key:"items_sold"},{label:(0,t.__)("Returns","woocommerce"),report:"revenue",key:"refunds"},{label:(0,t.__)("Discounted orders","woocommerce"),report:"coupons",key:"orders_count"},{label:(0,t.__)("Gross discounted","woocommerce"),report:"coupons",key:"amount"},{label:(0,t.__)("Total tax","woocommerce"),report:"taxes",key:"total_tax"},{label:(0,t.__)("Order tax","woocommerce"),report:"taxes",key:"order_tax"},{label:(0,t.__)("Shipping tax","woocommerce"),report:"taxes",key:"shipping_tax"},{label:(0,t.__)("Shipping","woocommerce"),report:"revenue",key:"shipping"},{label:(0,t.__)("Downloads","woocommerce"),report:"downloads",key:"download_count"}],O=(0,f.applyFilters)("woocommerce_admin_dashboard_charts_filter",F.map((e=>({...j[e.report].find((o=>o.key===e.key)),label:e.label,endpoint:e.report})))),A=({hiddenBlocks:e,onToggleHiddenBlock:o})=>O.map((r=>{const t=r.endpoint+"_"+r.key,c=!e.includes(t);return(0,b.jsx)(m.MenuItem,{checked:c,isCheckbox:!0,isClickable:!0,onInvoke:()=>{o(t)(),(0,_.recordEvent)("dash_charts_chart_toggle",{status:c?"off":"on",key:t})},children:r.label},r.endpoint+"_"+r.key)})),P=({chartInterval:e,setInterval:o,query:r,defaultDateRange:c})=>{const a=(0,d.getAllowedIntervalsForQuery)(r,c);if(!a||a.length<1)return null;const l={hour:(0,t.__)("By hour","woocommerce"),day:(0,t.__)("By day","woocommerce"),week:(0,t.__)("By week","woocommerce"),month:(0,t.__)("By month","woocommerce"),quarter:(0,t.__)("By quarter","woocommerce"),year:(0,t.__)("By year","woocommerce")};return(0,b.jsx)(n.SelectControl,{className:"woocommerce-chart__interval-select",value:e,options:a.map((e=>({value:e,label:l[e]}))),"aria-label":"Chart period",onChange:o})},I=({hiddenBlocks:e,path:o,query:r,filters:t})=>{const c=O.reduce(((e,o)=>(void 0===e[o.endpoint]&&(e[o.endpoint]=[]),e[o.endpoint].push(o),e)),{});return(0,b.jsx)("div",{className:"woocommerce-dashboard__columns",children:O.map((a=>e.includes(a.endpoint+"_"+a.key)?null:(0,b.jsx)(v,{charts:c[a.endpoint],endpoint:a.endpoint,path:o,query:r,selectedChart:a,filters:t},a.endpoint+"_"+a.key)))})},E=e=>{const{controls:o,hiddenBlocks:r,isFirst:d,isLast:p,onMove:u,onRemove:h,onTitleBlur:w,onTitleChange:y,onToggleHiddenBlock:v,path:f,title:g,titleInput:x,filters:k,defaultDateRange:C}=e,{updateUserPreferences:S,...T}=(0,i.useUserPreferences)(),[j,F]=(0,a.useState)(T.dashboard_chart_type||"line"),[O,E]=(0,a.useState)(T.dashboard_chart_interval||"day"),R={...e.query,chartType:j,interval:O},B=e=>()=>{F(e),S({dashboard_chart_type:e}),(0,_.recordEvent)("dash_charts_type_toggle",{chart_type:e})};return(0,b.jsxs)("div",{className:"woocommerce-dashboard__dashboard-charts",children:[(0,b.jsxs)(m.SectionHeader,{title:g||(0,t.__)("Charts","woocommerce"),menu:(0,b.jsx)(m.EllipsisMenu,{label:(0,t.__)("Choose which charts to display","woocommerce"),placement:"bottom-end",renderContent:({onToggle:e})=>(0,b.jsxs)(a.Fragment,{children:[(0,b.jsx)(m.MenuTitle,{children:(0,t.__)("Charts","woocommerce")}),A({hiddenBlocks:r,onToggleHiddenBlock:v}),(0,b.jsx)(o,{onToggle:e,onMove:u,onRemove:h,isFirst:d,isLast:p,onTitleBlur:w,onTitleChange:y,titleInput:x})]})}),className:"has-interval-select",children:[P({chartInterval:O,setInterval:e=>{E(e),S({dashboard_chart_interval:e}),(0,_.recordEvent)("dash_charts_interval",{interval:e})},query:R,defaultDateRange:C}),(0,b.jsxs)(n.NavigableMenu,{className:"woocommerce-chart__types",orientation:"horizontal",role:"menubar",children:[(0,b.jsx)(n.Button,{className:(0,c.A)("woocommerce-chart__type-button",{"woocommerce-chart__type-button-selected":!R.chartType||"line"===R.chartType}),title:(0,t.__)("Line chart","woocommerce"),"aria-checked":"line"===R.chartType,role:"menuitemradio",tabIndex:"line"===R.chartType?0:-1,onClick:B("line"),children:(0,b.jsx)(l.A,{})}),(0,b.jsx)(n.Button,{className:(0,c.A)("woocommerce-chart__type-button",{"woocommerce-chart__type-button-selected":"bar"===R.chartType}),title:(0,t.__)("Bar chart","woocommerce"),"aria-checked":"bar"===R.chartType,role:"menuitemradio",tabIndex:"bar"===R.chartType?0:-1,onClick:B("bar"),children:(0,b.jsx)(s.A,{})})]})]}),I({hiddenBlocks:r,path:f,query:R,filters:k})]})}},92272:(e,o,r)=>{o.A=function(e){var o=e.size,r=void 0===o?24:o,t=e.onClick,s=(e.icon,e.className),n=function(e,o){if(null==e)return{};var r,t,c=function(e,o){if(null==e)return{};var r,t,c={},a=Object.keys(e);for(t=0;t<a.length;t++)r=a[t],0<=o.indexOf(r)||(c[r]=e[r]);return c}(e,o);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(t=0;t<a.length;t++)r=a[t],0<=o.indexOf(r)||Object.prototype.propertyIsEnumerable.call(e,r)&&(c[r]=e[r])}return c}(e,a),m=["gridicon","gridicons-line-graph",s,!1,!1,!1].filter(Boolean).join(" ");return c.default.createElement("svg",l({className:m,height:r,width:r,onClick:t},n,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),c.default.createElement("g",null,c.default.createElement("path",{d:"M3 19h18v2H3zm3-3c1.1 0 2-.9 2-2 0-.5-.2-1-.5-1.3L8.8 10H9c.5 0 1-.2 1.3-.5l2.7 1.4v.1c0 1.1.9 2 2 2s2-.9 2-2c0-.5-.2-.9-.5-1.3L17.8 7h.2c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2c0 .5.2 1 .5 1.3L15.2 9H15c-.5 0-1 .2-1.3.5L11 8.2V8c0-1.1-.9-2-2-2s-2 .9-2 2c0 .5.2 1 .5 1.3L6.2 12H6c-1.1 0-2 .9-2 2s.9 2 2 2z"})))};var t,c=(t=r(51609))&&t.__esModule?t:{default:t},a=["size","onClick","icon","className"];function l(){return l=Object.assign?Object.assign.bind():function(e){for(var o,r=1;r<arguments.length;r++)for(var t in o=arguments[r])Object.prototype.hasOwnProperty.call(o,t)&&(e[t]=o[t]);return e},l.apply(this,arguments)}},9294:(e,o,r)=>{o.A=function(e){var o=e.size,r=void 0===o?24:o,t=e.onClick,s=(e.icon,e.className),n=function(e,o){if(null==e)return{};var r,t,c=function(e,o){if(null==e)return{};var r,t,c={},a=Object.keys(e);for(t=0;t<a.length;t++)r=a[t],0<=o.indexOf(r)||(c[r]=e[r]);return c}(e,o);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(t=0;t<a.length;t++)r=a[t],0<=o.indexOf(r)||Object.prototype.propertyIsEnumerable.call(e,r)&&(c[r]=e[r])}return c}(e,a),m=["gridicon","gridicons-stats-alt",s,!1,!1,!!function(e){return 0==e%18}(r)&&"needs-offset-y"].filter(Boolean).join(" ");return c.default.createElement("svg",l({className:m,height:r,width:r,onClick:t},n,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),c.default.createElement("g",null,c.default.createElement("path",{d:"M21 21H3v-2h18v2zM8 10H4v7h4v-7zm6-7h-4v14h4V3zm6 3h-4v11h4V6z"})))};var t,c=(t=r(51609))&&t.__esModule?t:{default:t},a=["size","onClick","icon","className"];function l(){return l=Object.assign?Object.assign.bind():function(e){for(var o,r=1;r<arguments.length;r++)for(var t in o=arguments[r])Object.prototype.hasOwnProperty.call(o,t)&&(e[t]=o[t]);return e},l.apply(this,arguments)}}}]);