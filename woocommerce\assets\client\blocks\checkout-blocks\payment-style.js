"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[6073],{67004:(e,t,s)=>{s.r(t),s.d(t,{default:()=>b});var c=s(4921),o=s(2328),i=s(41616),l=s(14656),n=s(47143),r=s(47594),a=s(68696),h=s(94199),p=s(83819),d=s(26671),u=s(10790);const b=(0,i.withFilteredAttributes)(d.A)((({title:e,description:t,children:s,className:i})=>{const{showFormStepNumbers:d}=(0,h.O)(),b=(0,n.useSelect)((e=>e(r.checkoutStore).isProcessing())),{cartNeedsPayment:k}=(0,o.V)();return k?(0,u.jsxs)(l.FormStep,{id:"payment-method",disabled:b,className:(0,c.A)("wc-block-checkout__payment-method",i),title:e,description:t,showStepNumber:d,children:[(0,u.jsx)(l.StoreNoticesContainer,{context:a.tG.PAYMENTS}),(0,u.jsx)(p.A,{}),s]}):null}))}}]);