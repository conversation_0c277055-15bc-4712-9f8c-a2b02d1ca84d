"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[1226],{12974:(e,t,o)=>{o.d(t,{Ay:()=>a});var n=o(13240);const s=["a","b","em","i","strong","p","br"],i=["target","href","rel","name","download"],a=e=>({__html:(0,n.sanitize)(e,{ALLOWED_TAGS:s,ALLOWED_ATTR:i})})},75753:(e,t,o)=>{o.d(t,{LO:()=>_,PE:()=>u,S:()=>m,CS:()=>p}),o(18982);var n=o(27723),s=o(56427),i=o(86087),a=o(47143),r=o(40314),c=o(96476),l=o(1069),d=o(39793);const m=({gatewayId:e,gatewayState:t,settingsHref:o,onboardingHref:m,isOffline:_,acceptIncentive:u=()=>{},gatewayHasRecommendedPaymentMethods:p,installingPlugin:g,buttonText:h=(0,n.__)("Enable","woocommerce"),incentive:y=null,setOnboardingModalOpen:w,onboardingType:v})=>{const[x,b]=(0,i.useState)(!1),{createErrorNotice:j}=(0,a.dispatch)("core/notices"),{togglePaymentGateway:f,invalidateResolutionForStoreSelector:k}=(0,a.useDispatch)(r.paymentSettingsStore),S=()=>{j((0,n.__)("An error occurred. You will be redirected to the settings page, try enabling the payment gateway there.","woocommerce"),{type:"snackbar",explicitDismiss:!0})};return(0,d.jsx)(s.Button,{variant:"primary",isBusy:x,disabled:x||!!g,onClick:s=>{if(s.preventDefault(),t.enabled)return;(0,l.TH)("provider_enable_click",{provider_id:e});const i=window.woocommerce_admin.nonces?.gateway_toggle||"";if(!i)return S(),void(window.location.href=o);b(!0),y&&u(y.promo_id),f(e,window.woocommerce_admin.ajax_url,i).then((s=>{if("needs_setup"===s.data)if(t.account_connected)j((0,n.__)("The provider could not be enabled. Check the Manage page for details.","woocommerce"),{type:"snackbar",explicitDismiss:!0,actions:[{label:(0,n.__)("Manage","woocommerce"),url:o}]});else if((0,l.TH)("provider_enable",{provider_id:e}),"native_in_context"===v&&w)w(!0);else{if(!p)return void(window.location.href=m);(0,c.getHistory)().push((0,c.getNewPath)({},"/payment-methods"))}k(_?"getOfflinePaymentGateways":"getPaymentProviders"),b(!1)})).catch((()=>{b(!1),S(),window.location.href=o}))},href:o,children:h})},_=({acceptIncentive:e,installingPlugin:t,buttonText:o=(0,n.__)("Activate payments","woocommerce"),incentive:a=null,setOnboardingModalOpen:r,onboardingType:c})=>{const[m,_]=(0,i.useState)(!1);return(0,d.jsx)(s.Button,{variant:"primary",isBusy:m,disabled:m||!!t,onClick:()=>{_(!0),(0,l.AC)().then((()=>{a&&e(a.promo_id),"native_in_context"===c?(r(!0),_(!1)):window.location.href=(0,l.ZV)()})).catch((()=>{_(!1)}))},children:o})},u=({gatewayId:e,gatewayState:t,onboardingState:o,settingsHref:m,onboardingHref:_,gatewayHasRecommendedPaymentMethods:u,installingPlugin:p,buttonText:g=(0,n.__)("Complete setup","woocommerce"),setOnboardingModalOpen:h,onboardingType:y})=>{const[w,v]=(0,i.useState)(!1),{select:x}=(0,a.useSelect)((e=>({select:e})),[]),b=t.account_connected,j=o.started,f=o.completed;return(0,i.useEffect)((()=>{"woocommerce_payments"!==e||"native_in_context"!==y||f||x(r.woopaymentsOnboardingStore).getOnboardingData()}),[e,y,f,x]),(0,d.jsx)(s.Button,{variant:"primary",isBusy:w,disabled:w||!!p,onClick:()=>{if((0,l.TH)("provider_complete_setup_click",{provider_id:e,onboarding_started:o.started,onboarding_completed:o.completed,onboarding_test_mode:o.test_mode}),v(!0),"native_in_context"===y)h(!0);else{if(b&&j)return b&&j&&!f?void(window.location.href=_):void(window.location.href=m);if(!u)return void(window.location.href=_);(0,c.getHistory)().push((0,c.getNewPath)({},"/payment-methods"))}v(!1)},children:g},e)},p=({gatewayId:e,settingsHref:t,isInstallingPlugin:o,buttonText:i=(0,n.__)("Manage","woocommerce")})=>(0,d.jsx)(s.Button,{variant:"secondary",href:t,disabled:o,onClick:()=>{(0,l.TH)("provider_manage_click",{provider_id:e})},children:i})},1275:(e,t,o)=>{o.d(t,{v:()=>l});var n=o(18537),s=o(56427),i=o(86087),a=o(12974),r=o(1069),c=o(39793);const l=({method:e,paymentMethodsState:t,setPaymentMethodsState:o,isExpanded:l,initialVisibilityStatus:d,...m})=>{var _,u,p,g;const h=(0,i.useRef)(null);void 0===d&&null===h.current&&void 0!==t[e.id]&&(h.current=(0,r.TO)(e,t[e.id]));const y=void 0!==d?null!=d&&d:null!==(_=h.current)&&void 0!==_&&_;return l||y?(0,c.jsx)("div",{id:e.id,className:"woocommerce-list__item woocommerce-list__item-enter-done",...m,children:(0,c.jsxs)("div",{className:"woocommerce-list__item-inner",children:["apple_google"!==e.id&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"woocommerce-list__item-before",children:(0,c.jsx)("img",{src:e.icon,alt:e.title+" logo"})}),(0,c.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,c.jsx)("span",{className:"woocommerce-list__item-title",children:e.title}),(0,c.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,a.Ay)((0,n.decodeEntities)(e.description))})]})]}),"apple_google"===e.id&&(0,c.jsxs)("div",{className:"woocommerce-list__item-multi",children:[(0,c.jsxs)("div",{className:"woocommerce-list__item-multi-row multi-row-space",children:[(0,c.jsx)("div",{className:"woocommerce-list__item-before",children:(0,c.jsx)("img",{src:e.icon,alt:e.title+" logo"})}),(0,c.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,c.jsx)("span",{className:"woocommerce-list__item-title",children:e.title}),(0,c.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,a.Ay)((0,n.decodeEntities)(e.description))})]})]}),(0,c.jsxs)("div",{className:"woocommerce-list__item-multi-row",children:[(0,c.jsx)("div",{className:"woocommerce-list__item-before",children:(0,c.jsx)("img",{src:e.extraIcon,alt:e.extraTitle+" logo"})}),(0,c.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,c.jsx)("span",{className:"woocommerce-list__item-title",children:e.extraTitle}),(0,c.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,a.Ay)((0,n.decodeEntities)(null!==(u=e.extraDescription)&&void 0!==u?u:""))})]})]})]}),(0,c.jsx)("div",{className:"woocommerce-list__item-after",children:(0,c.jsx)("div",{className:"woocommerce-list__item-after__actions wc-settings-prevent-change-event",children:(0,c.jsx)(s.ToggleControl,{checked:null!==(p=t[e.id])&&void 0!==p&&p,onChange:n=>{o({...t,[e.id]:n})},disabled:null!==(g=e.required)&&void 0!==g&&g,label:""})})})]})}):null}},60327:(e,t,o)=>{o.r(t),o.d(t,{SettingsPaymentsMain:()=>it,default:()=>at});var n=o(51609),s=o.n(n),i=o(27723),a=o(40314),r=o(47143),c=o(86087),l=o(1455),d=o.n(l),m=o(96476),_=o(56427);function u(e){const{createNotice:t}=(0,r.dispatch)("core/notices");e.error_data&&e.errors&&Object.keys(e.errors).length?Object.keys(e.errors).forEach((o=>{t("error",e.errors[o].join(" "))})):e.message&&t(e.code?"error":"success",e.message)}var p=o(72553),g=o(18537),h=o(39793);const y=()=>(0,h.jsxs)("div",{className:"other-payment-gateways__content__grid-item",children:[(0,h.jsx)("div",{className:"grid-item-placeholder__img"}),(0,h.jsxs)("div",{className:"other-payment-gateways__content__grid-item__content grid-item-placeholder__content",children:[(0,h.jsx)("span",{className:"grid-item-placeholder__title"}),(0,h.jsx)("span",{className:"grid-item-placeholder__description"}),(0,h.jsx)("div",{className:"grid-item-placeholder__actions"})]})]});var w=o(98846),v=o(56109),x=o(1069);const b=({variant:e,suggestionId:t})=>{const[o,n]=(0,c.useState)(!1),s=(0,c.useRef)(null),a=e=>{const o=e.target.closest(".woocommerce-official-extension-badge__container");s.current&&o!==s.current||(n((e=>!e)),(0,x.TH)("official_badge_click",{suggestion_id:t}))};return(0,h.jsx)(w.Pill,{className:"woocommerce-official-extension-badge",children:(0,h.jsxs)("span",{className:"woocommerce-official-extension-badge__container",tabIndex:0,role:"button",ref:s,onClick:a,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||a(e)},children:[(0,h.jsx)("img",{src:v.GZ+"images/icons/official-extension.svg",alt:(0,i.__)("Official WooCommerce extension badge","woocommerce")}),"expanded"===e&&(0,h.jsx)("span",{children:(0,i.__)("Official","woocommerce")}),o&&(0,h.jsx)(_.Popover,{className:"woocommerce-official-extension-badge-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{n(!1)},children:(0,h.jsx)("div",{className:"components-popover__content-container",children:(0,h.jsx)("p",{children:(0,c.createInterpolateElement)((0,i.__)("This is an Official WooCommerce payment extension. <learnMoreLink />","woocommerce"),{learnMoreLink:(0,h.jsx)(w.Link,{href:"https://woocommerce.com/learn-more-about-official-partner-badging/",target:"_blank",rel:"noreferrer",type:"external",onClick:()=>{(0,x.TH)("official_badge_learn_more_click",{suggestion_id:t})},children:(0,i.__)("Learn more","woocommerce")})})})})})]})})};var j=o(24148),f=o(73290);const k=({status:e,message:t,popoverContent:o})=>{const[n,s]=(0,c.useState)(!1),a=(0,c.useRef)(null),r=e=>{const t=e.target.closest(".woocommerce-status-badge__icon-container");a.current&&t!==a.current||s((e=>!e))};return(0,h.jsxs)(w.Pill,{className:`woocommerce-status-badge ${(()=>{switch(e){case"active":case"has_incentive":return"woocommerce-status-badge--success";case"needs_setup":case"test_mode":case"test_account":return"woocommerce-status-badge--warning";case"recommended":case"inactive":return"woocommerce-status-badge--info";default:return""}})()}`,children:[t||(()=>{switch(e){case"active":return(0,i.__)("Active","woocommerce");case"inactive":return(0,i.__)("Inactive","woocommerce");case"needs_setup":return(0,i.__)("Action needed","woocommerce");case"test_mode":return(0,i.__)("Test mode","woocommerce");case"test_account":return(0,i.__)("Test account","woocommerce");case"recommended":return(0,i.__)("Recommended","woocommerce");default:return""}})(),o&&(0,h.jsxs)("span",{className:"woocommerce-status-badge__icon-container",tabIndex:0,role:"button",ref:a,onClick:r,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||r(e)},children:[(0,h.jsx)(j.A,{className:"woocommerce-status-badge-icon",size:16,icon:f.A}),n&&(0,h.jsx)(_.Popover,{className:"woocommerce-status-badge-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{s(!1)},children:(0,h.jsx)("div",{className:"components-popover__content-container",children:o})})]})]})},S=({incentive:e})=>(0,h.jsx)(k,{status:"has_incentive",message:e.badge,popoverContent:(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("p",{className:"woocommerce-incentive-popover__title",children:e.title}),(0,h.jsx)("p",{className:"woocommerce-incentive-popover__terms",children:(0,c.createInterpolateElement)((0,i.__)("See <termsLink /> for details.","woocommerce"),{termsLink:(0,h.jsx)(w.Link,{href:e.tc_url,target:"_blank",rel:"noreferrer",type:"external",children:(0,i.__)("Terms and Conditions","woocommerce")})})})]})}),N=({suggestions:e,suggestionCategories:t,installingPlugin:o,setUpPlugin:n,isFetching:s,morePaymentOptionsLink:a})=>{const r=new URLSearchParams(window.location.search),l="expanded"===r.get("other_pes_section"),[d,m]=(0,c.useState)(l),[u,w]=(0,c.useState)(""),v=(0,c.useRef)(null),j=(e,t)=>{const o=e.target.closest(".other-payment-gateways__content__title__icon-container");v.current&&o!==v.current||w(t===u?"":t)},f=()=>{w("")},k=()=>{const e=!d;(0,x.TH)("other_payment_options_section_click",{action:e?"expand":"collapse"}),m(e),r.set("other_pes_section",e?"expanded":"collapsed"),window.history.replaceState({},document.title,window.location.pathname+"?"+r.toString())},N=(0,c.useMemo)((()=>t.map((t=>({category:t,suggestions:e.filter((e=>e._type===t.id))})))),[e,t]),C=(0,c.useMemo)((()=>s?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("div",{className:"other-payment-gateways__header__title-image-placeholder"}),(0,h.jsx)("div",{className:"other-payment-gateways__header__title-image-placeholder"}),(0,h.jsx)("div",{className:"other-payment-gateways__header__title-image-placeholder"})]}):N.map((({suggestions:e})=>0===e.length?null:e.map((e=>(0,h.jsx)("img",{src:e.icon,alt:e.title+" small logo",width:"24",height:"24",className:"other-payment-gateways__header__title-image"},e.id)))))),[N,s]),P=(0,c.useMemo)((()=>s?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(y,{}),(0,h.jsx)(y,{}),(0,h.jsx)(y,{})]}):N.map((({category:e,suggestions:t})=>0===t.length?null:(0,h.jsxs)("div",{className:"other-payment-gateways__content__category-container",children:[(0,h.jsxs)("div",{className:"other-payment-gateways__content__title",children:[(0,h.jsx)("h3",{className:"other-payment-gateways__content__title__h3",children:(0,g.decodeEntities)(e.title)}),(0,h.jsxs)("span",{className:"other-payment-gateways__content__title__icon-container",onClick:t=>j(t,e.id),onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||j(t,e.id)},tabIndex:0,role:"button",ref:v,children:[(0,h.jsx)(p.A,{icon:"info-outline",className:"other-payment-gateways__content__title__icon"}),e.id===u&&(0,h.jsx)(_.Popover,{className:"other-payment-gateways__content__title-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:f,children:(0,h.jsx)("div",{className:"components-popover__content-container",children:(0,h.jsx)("p",{children:(0,g.decodeEntities)(e.description)})})})]})]}),(0,h.jsx)("div",{className:"other-payment-gateways__content__grid",children:t.map((e=>(0,h.jsxs)("div",{className:"other-payment-gateways__content__grid-item",children:[(0,h.jsx)("img",{className:"other-payment-gateways__content__grid-item-image",src:e.icon,alt:(0,g.decodeEntities)(e.title)+" logo"}),(0,h.jsxs)("div",{className:"other-payment-gateways__content__grid-item__content",children:[(0,h.jsxs)("span",{className:"other-payment-gateways__content__grid-item__content__title",children:[e.title,e?._incentive&&(0,h.jsx)(S,{incentive:e._incentive}),(0,h.jsx)(b,{variant:"expanded",suggestionId:e.id})]}),(0,h.jsx)("span",{className:"other-payment-gateways__content__grid-item__content__description",children:(0,g.decodeEntities)(e.description)}),(0,h.jsx)("div",{className:"other-payment-gateways__content__grid-item__content__actions",children:(0,h.jsx)(_.Button,{variant:"link",onClick:()=>{var t;return n(e,null,"not_installed"===e.plugin.status&&null!==(t=e._links?.attach?.href)&&void 0!==t?t:null)},isBusy:o===e.id,disabled:!!o,children:o===e.id?(0,i.__)("Installing","woocommerce"):(0,i.__)("Install","woocommerce")})})]})]},e.id)))})]},e.id)))),[N,o,n,s,u]);return(0,h.jsxs)("div",{className:"other-payment-gateways"+(d?" is-expanded":""),children:[(0,h.jsxs)("div",{className:"other-payment-gateways__header",onClick:k,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||k()},role:"button",tabIndex:0,"aria-expanded":d,children:[(0,h.jsxs)("div",{className:"other-payment-gateways__header__title",children:[(0,h.jsx)("span",{children:(0,i.__)("Other payment options","woocommerce")}),!d&&(0,h.jsx)(h.Fragment,{children:C})]}),(0,h.jsx)(p.A,{className:"other-payment-gateways__header__arrow",icon:d?"chevron-up":"chevron-down"})]}),d&&(0,h.jsxs)("div",{className:"other-payment-gateways__content",children:[P,(0,h.jsx)("div",{className:"other-payment-gateways__content__external-icon",children:a})]})]})};var C=o(4921),P=o(15703),T=o(46608),I=o(36849),O=o(21913),F=o(29491),A=o(90700),E=o(72744);const B=(e,t)=>{const o=t,{changes:n,type:s,props:i}=o,{items:a}=i,{selectedItem:r}=e;switch(s){case O.WM.stateChangeTypes.ItemClick:return{...n,isOpen:!0,highlightedIndex:e.highlightedIndex};case O.WM.stateChangeTypes.ToggleButtonKeyDownArrowDown:return{selectedItem:a[r?Math.min(a.indexOf(r)+1,a.length-1):0],isOpen:!0};case O.WM.stateChangeTypes.ToggleButtonKeyDownArrowUp:return{selectedItem:a[r?Math.max(a.indexOf(r)-1,0):a.length-1],isOpen:!0};default:return n}},D=e=>e.normalize("NFD").replace(/[\u0300-\u036f]/g,""),M=({name:e,className:t,label:o,describedBy:s,options:a,onChange:r,value:l,placeholder:d,children:m})=>{var u;const[p,g]=(0,n.useState)(""),y=(0,F.useThrottle)((0,c.useCallback)(((e,t)=>new Set(t.filter((t=>{var o;return`${D(null!==(o=t.name)&&void 0!==o?o:"")}`.toLowerCase().includes(D(e.toLowerCase()))})))),[]),200),w=""!==p?null!==(u=y(p,a))&&void 0!==u?u:new Set:new Set(a),{getToggleButtonProps:x,getMenuProps:b,getItemProps:f,isOpen:k,highlightedIndex:S,selectedItem:N,closeMenu:P}=(0,O.WM)({initialSelectedItem:l,items:[...w],stateReducer:B}),T=((e,t)=>{const o=t.find((t=>t.key===e));return o?.name?o.name:""})(l.key,a),I=N?N.key:"",M=(0,n.useRef)(null),L=(0,n.useRef)(null),W=(0,c.useCallback)((e=>{const t=M.current,o=t?.querySelector(`[data-index="${e}"]`);o&&o.scrollIntoView({block:"nearest"})}),[M]),H=""!==p,R=b({className:"components-country-select-control__menu","aria-hidden":!k,ref:M}),U=(0,c.useCallback)((e=>{e.stopPropagation(),r(I),P()}),[r,I,P]),G=(0,c.useCallback)((e=>{e.stopPropagation(),"Enter"===e.key&&r(I)}),[r,I]),q=(0,c.useCallback)((e=>{e.preventDefault(),""!==p&&g(""),null!==N&&setTimeout((()=>{W(a.indexOf(N))}),10)}),[p,N]);return(0,c.useEffect)((()=>{if(k&&null!==N){const e=Array.from(w).indexOf(N);W(e)}}),[k]),(0,h.jsxs)("div",{className:(0,C.A)("woopayments components-country-select-control",t),children:[(0,h.jsxs)(_.Button,{...x({"aria-label":o,"aria-labelledby":void 0,"aria-describedby":s||(T?(0,i.sprintf)((0,i.__)("Currently selected: %s","woocommerce"),T):(0,i.__)("No selection","woocommerce")),className:(0,C.A)("components-country-select-control__button",{placeholder:!T}),name:e,onKeyDown:G}),children:[(0,h.jsxs)("span",{className:"components-country-select-control__button-value",children:[(0,h.jsx)("span",{className:"components-country-select-control__label",children:o}),T||d]}),(0,h.jsx)(j.A,{icon:A.A,className:"components-custom-select-control__button-icon"})]}),(0,h.jsx)("div",{...R,children:k&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)("div",{className:"components-country-select-control__search wc-settings-prevent-change-event",children:[(0,h.jsx)("input",{className:"components-country-select-control__search--input",ref:L,type:"text",value:p,onChange:({target:e})=>g(e.value),tabIndex:-1,placeholder:(0,i.__)("Search","woocommerce")}),(0,h.jsx)("button",{className:"components-country-select-control__search--input-suffix",onClick:q,children:(z=H,z?(0,h.jsx)("img",{src:v.GZ+"images/icons/clear.svg",alt:(0,i.__)("Clear search","woocommerce")}):(0,h.jsx)("img",{src:v.GZ+"images/icons/search.svg",alt:(0,i.__)("Search","woocommerce")}))})]}),(0,h.jsx)("div",{className:"components-country-select-control__list",children:[...w].map(((e,t)=>(0,n.createElement)("div",{...f({item:e,index:t,key:e.key,className:(0,C.A)(e.className,"components-country-select-control__item",{"is-highlighted":t===S}),"data-index":t,style:e.style}),key:e.key},e.key===I&&(0,h.jsx)(j.A,{icon:E.A,className:"components-country-select-control__item-icon"}),m?m(e):e.name)))}),(0,h.jsx)("div",{className:"components-country-select-control__apply",children:(0,h.jsx)("button",{className:"components-button is-primary",onClick:U,children:(0,i.__)("Apply","woocommerce")})})]})})]});var z};var L=o(51881),W=o(15698),H=o(85816),R=o(12974);const U=({providerId:e,pluginFile:t,isSuggestion:o,suggestionId:n="",suggestionHideUrl:s="",onToggle:l,links:d=[],canResetAccount:m=!1,setResetAccountModalVisible:u=()=>{},isEnabled:p=!1})=>{const{deactivatePlugin:g}=(0,r.useDispatch)(a.pluginsStore),[y,w]=(0,c.useState)(!1),[v,b]=(0,c.useState)(!1),[j,f]=(0,c.useState)(!1),{invalidateResolutionForStoreSelector:k,togglePaymentGateway:S,hidePaymentExtensionSuggestion:N}=(0,r.useDispatch)(a.paymentSettingsStore),{createErrorNotice:C,createSuccessNotice:P}=(0,r.useDispatch)("core/notices"),T={pricing:(0,i.__)("See pricing & fees","woocommerce"),about:(0,i.__)("Learn more","woocommerce"),terms:(0,i.__)("See Terms of Service","woocommerce"),support:(0,i.__)("Get support","woocommerce"),documentation:(0,i.__)("View documentation","woocommerce")},I=d.filter((e=>{switch(e._type){case"pricing":return!0;case"terms":case"about":return!p;case"documentation":case"support":return p;default:return!1}}));return(0,h.jsxs)(h.Fragment,{children:[I.map((t=>{const o=T[t._type];return o?(0,h.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,h.jsx)(_.Button,{target:"_blank",href:t.url,onClick:()=>{(0,x.TH)("provider_context_link_click",{provider_id:e,suggestion_id:n,link_type:t._type})},children:o})},t._type):null})),!!I.length&&(0,h.jsx)(_.CardDivider,{}),o&&(0,h.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,h.jsx)(_.Button,{onClick:()=>{f(!0),N(s).then((()=>{k("getPaymentProviders"),f(!1),l()})).catch((()=>{C((0,i.__)("Failed to hide the payment extension suggestion.","woocommerce")),f(!1),l()}))},isBusy:j,disabled:j,children:(0,i.__)("Hide suggestion","woocommerce")})},"hide-suggestion"),m&&(0,h.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,h.jsx)(_.Button,{onClick:()=>{u(!0),l()},className:"components-button__danger",children:(0,i.__)("Reset account","woocommerce")})},"reset-account"),!o&&!p&&(0,h.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,h.jsx)(_.Button,{className:"components-button__danger",onClick:()=>{(0,x.TH)("provider_deactivate_click",{provider_id:e,suggestion_id:n}),w(!0),g(t).then((()=>{(0,x.TH)("provider_deactivate",{provider_id:e,suggestion_id:n}),P((0,i.__)("The provider plugin was successfully deactivated.","woocommerce")),k("getPaymentProviders"),w(!1),l()})).catch((()=>{C((0,i.__)("Failed to deactivate the provider plugin.","woocommerce")),w(!1),l()}))},isBusy:y,disabled:!t||y,children:(0,i.__)("Deactivate","woocommerce")})},"deactivate"),!o&&p&&(0,h.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,h.jsx)(_.Button,{className:"components-button__danger",onClick:()=>{(0,x.TH)("provider_disable_click",{provider_id:e,suggestion_id:n});const t=window.woocommerce_admin.nonces?.gateway_toggle||"";t?(b(!0),S(e,window.woocommerce_admin.ajax_url,t).then((()=>{(0,x.TH)("provider_disable",{provider_id:e,suggestion_id:n}),k("getPaymentProviders"),b(!1),l()})).catch((()=>{C((0,i.__)("Failed to disable the plugin.","woocommerce")),b(!1),l()}))):C((0,i.__)("Failed to disable the plugin.","woocommerce"))},isBusy:v,disabled:v,children:(0,i.__)("Disable","woocommerce")})},"disable")]})},G=({isOpen:e,onClose:t,isTestMode:o})=>{const[n,s]=(0,c.useState)(!1),{invalidateResolutionForStoreSelector:l}=(0,r.useDispatch)(a.paymentSettingsStore),{invalidateResolutionForStoreSelector:d}=(0,r.useDispatch)(a.woopaymentsOnboardingStore),{createNotice:m}=(0,r.useDispatch)("core/notices");return(0,h.jsx)(h.Fragment,{children:e&&(0,h.jsxs)(_.Modal,{title:(0,i.__)("Reset your test account","woocommerce"),className:"woocommerce-woopayments-modal",isDismissible:!0,onRequestClose:t,children:[(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content",children:[(0,h.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,h.jsx)("div",{children:(0,h.jsx)("span",{children:o?(0,i.sprintf)((0,i.__)("When you reset your test account, all payment data — including your %s account details, test transactions, and payouts history — will be lost. Your order history will remain. This action cannot be undone, but you can create a new test account at any time.","woocommerce"),"WooPayments"):(0,i.sprintf)((0,i.__)("When you reset your account, all payment data — including your %s account details, test transactions, and payouts history — will be lost. Your order history will remain. This action cannot be undone, but you can create a new test account at any time.","woocommerce"),"WooPayments")})})}),(0,h.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,h.jsx)("h3",{children:(0,i.__)("Are you sure you'd like to continue?","woocommerce")})})]}),(0,h.jsx)("div",{className:"woocommerce-woopayments-modal__actions",children:(0,h.jsx)(_.Button,{className:"danger",variant:"secondary",isBusy:n,disabled:n,onClick:()=>{s(!0),(0,x.pF)().then((()=>{l("getPaymentProviders"),d("getOnboardingData")})).catch((()=>{m("error",(0,i.__)("Failed to reset your WooPayments account.","woocommerce"),{isDismissible:!0})})).finally((()=>{s(!1),t()}))},children:(0,i.__)("Yes, reset account","woocommerce")})})]})})},q=({isOpen:e,devMode:t,onClose:o})=>{const[n,s]=(0,c.useState)(!1),[a,r]=(0,c.useState)(!1);return(0,h.jsx)(h.Fragment,{children:e&&(0,h.jsxs)(_.Modal,{title:(0,i.__)("You're ready to test payments!","woocommerce"),className:"woocommerce-woopayments-modal",isDismissible:!0,onRequestClose:o,children:[(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content",children:[(0,h.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,h.jsx)("div",{className:"woocommerce-woopayments-modal__content__item__description",children:(0,h.jsx)("p",{children:(0,I.A)({mixedString:(0,i.__)("We've created a test account for you so that you can begin testing payments on your store. {{break/}}Not sure what to test? Take a look at {{link}}how to test payments{{/link}}.","woocommerce"),components:{link:(0,h.jsx)(w.Link,{href:"https://woocommerce.com/document/woopayments/testing-and-troubleshooting/sandbox-mode/",target:"_blank",rel:"noreferrer",type:"external"}),break:(0,h.jsx)("br",{})}})})})}),(0,h.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,h.jsx)("h2",{children:(0,i.__)("What's next:","woocommerce")})}),(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,h.jsx)("img",{src:v.GZ+"images/icons/store.svg",alt:"store icon"}),(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,h.jsx)("h3",{children:(0,i.__)("Continue your store setup","woocommerce")}),(0,h.jsx)("div",{children:(0,i.__)("Finish completing the tasks required to launch your store.","woocommerce")})]})]}),!t&&(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,h.jsx)("img",{src:v.GZ+"images/icons/dollar.svg",alt:"dollar icon"}),(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,h.jsx)("h3",{children:(0,i.__)("Activate payments","woocommerce")}),(0,h.jsx)("div",{children:(0,h.jsx)("p",{children:(0,I.A)({mixedString:(0,i.__)("Provide some additional details about your business so you can begin accepting real payments. {{link}}Learn more{{/link}}","woocommerce"),components:{link:(0,h.jsx)(w.Link,{href:"https://woocommerce.com/document/woopayments/startup-guide/#sign-up-process",target:"_blank",rel:"noreferrer",type:"external"})}})})})]})]})]}),(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__actions",children:[(0,h.jsx)(_.Button,{variant:"primary",isBusy:a,disabled:a,onClick:()=>{(0,x.TH)("continue_store_setup_click",{provider_id:"woocommerce_payments"}),r(!0),window.location.href=(0,P.getAdminLink)("admin.php?page=wc-admin")},children:(0,i.__)("Continue store setup","woocommerce")}),!t&&(0,h.jsx)(_.Button,{variant:"secondary",isBusy:n,disabled:n,onClick:()=>{(0,x.TH)("switch_to_live_account_click",{provider_id:"woocommerce_payments"}),s(!0),window.location.href=(0,x.ZV)()},children:(0,i.__)("Activate payments","woocommerce")})]})]})})},z=({provider:e,label:t})=>{const[o,n]=(0,c.useState)(!1),s=(0,x.j4)(e.id)&&"gateway"===e._type&&e.state?.account_connected&&(e.onboarding?.state?.test_mode||!e.onboarding?.state?.completed);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(w.EllipsisMenu,{label:t,renderContent:({onToggle:t})=>(0,h.jsx)(U,{providerId:e.id,pluginFile:e.plugin.file,isSuggestion:"suggestion"===e._type,suggestionId:e._suggestion_id||"",suggestionHideUrl:"suggestion"===e._type?e._links?.hide?.href:"",links:e.links,onToggle:t,isEnabled:e.state?.enabled,canResetAccount:s,setResetAccountModalVisible:n}),focusOnMount:!0}),(0,h.jsx)(G,{isOpen:o,onClose:()=>n(!1),isTestMode:e.onboarding?.state?.test_mode})]})},$=({suggestion:e,installingPlugin:t,setUpPlugin:o,pluginInstalled:n,acceptIncentive:s,shouldHighlightIncentive:a=!1,...r})=>{const c=(0,x.O2)(e)?e._incentive:null;let l=(0,i.__)("Install","woocommerce");return n?l=(0,i.__)("Enable","woocommerce"):t===e.id&&(l=(0,i.__)("Installing","woocommerce")),(0,h.jsx)("div",{id:e.id,className:"transitions-disabled woocommerce-list__item woocommerce-list__item-enter-done "+((0,x.O2)(e)&&a?"has-incentive":""),...r,children:(0,h.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,h.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,h.jsx)(W.Gh,{}),e.icon&&(0,h.jsx)("img",{className:"woocommerce-list__item-image",src:e.icon,alt:e.title+" logo"})]}),(0,h.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,h.jsxs)("span",{className:"woocommerce-list__item-title",children:[e.title," ",!(0,x.O2)(e)&&(0,x.j4)(e.id)&&(0,h.jsx)(k,{status:"recommended"}),c&&(0,h.jsx)(S,{incentive:c}),(0,h.jsx)(b,{variant:"expanded",suggestionId:e.id})]}),(0,h.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,R.Ay)((0,g.decodeEntities)(e.description))}),(0,x.j4)(e.id)&&(0,h.jsx)(H.WooPaymentsMethodsLogos,{maxElements:10,tabletWidthBreakpoint:1080,mobileWidthBreakpoint:768,isWooPayEligible:(0,x.bU)(e)})]}),(0,h.jsx)("div",{className:"woocommerce-list__item-buttons",children:(0,h.jsx)("div",{className:"woocommerce-list__item-buttons__actions",children:(0,h.jsx)(_.Button,{variant:"primary",onClick:()=>{var t,i;n&&(0,x.TH)("provider_enable_click",{provider_id:e.id,suggestion_id:e.id}),c&&s(c.promo_id),o(e,null!==(t=e.onboarding?._links?.onboard?.href)&&void 0!==t?t:null,n?null:null!==(i=e._links?.attach?.href)&&void 0!==i?i:null)},isBusy:t===e.id,disabled:!!t,children:l})})}),(0,h.jsx)("div",{className:"woocommerce-list__item-after",children:(0,h.jsx)("div",{className:"woocommerce-list__item-after__actions",children:(0,h.jsx)(z,{label:(0,i.__)("Payment provider actions","woocommerce"),provider:e})})})]})})};var K=o(75753);const Z=({buttonText:e=(0,i.__)("Reactivate payments","woocommerce"),settingsHref:t})=>{const[o,n]=(0,c.useState)(!1),{createSuccessNotice:s,createErrorNotice:l}=(0,r.dispatch)("core/notices"),{invalidateResolutionForStoreSelector:m}=(0,r.useDispatch)(a.paymentSettingsStore);return(0,h.jsx)(_.Button,{variant:"primary",isBusy:o,disabled:o,onClick:e=>{e.preventDefault(),n(!0),d()({path:"/wc/v3/payments/settings",method:"POST",data:{is_test_mode_enabled:!1}}).then((()=>{s((0,i.sprintf)((0,i.__)("%s is now processing live payments (real payment methods and charges).","woocommerce"),"WooPayments"),{type:"snackbar",explicitDismiss:!1}),m("getPaymentProviders"),n(!1)})).catch((()=>{n(!1),l((0,i.sprintf)((0,i.__)("An error occurred. You will be redirected to the %s settings page to manage payments processing mode from there.","woocommerce"),"WooPayments"),{type:"snackbar",explicitDismiss:!0}),window.location.href=t}))},href:t,children:e})},V=({gateway:e,installingPlugin:t,acceptIncentive:o,shouldHighlightIncentive:n,setIsOnboardingModalOpen:s,...a})=>{var r;const c=(0,x.j4)(e.id),l=(0,x.O2)(e)?e._incentive:null,d=(null!==(r=e.onboarding.recommended_payment_methods)&&void 0!==r?r:[]).length>0,m=!e.state.account_connected||e.state.account_connected&&!e.onboarding.state.started||e.state.account_connected&&e.onboarding.state.started&&!e.onboarding.state.completed;return(0,h.jsx)("div",{id:e.id,className:`transitions-disabled woocommerce-list__item woocommerce-list__item-enter-done woocommerce-item__payment-gateway ${c?"woocommerce-item__woocommerce-payments":""} ${(0,x.O2)(e)&&n?"has-incentive":""}`,...a,children:(0,h.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,h.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,h.jsx)(W.Gh,{}),e.icon&&(0,h.jsx)("img",{className:"woocommerce-list__item-image",src:e.icon,alt:e.title+" logo"})]}),(0,h.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,h.jsxs)("span",{className:"woocommerce-list__item-title",children:[e.title,l?(0,h.jsx)(S,{incentive:l}):(0,h.jsx)(k,{status:!e.state.enabled&&e.state.needs_setup?"needs_setup":e.state.enabled?e.onboarding.state.test_mode?"test_account":e.state.test_mode?"test_mode":"active":"inactive"}),e._suggestion_id&&(0,h.jsx)(b,{variant:"expanded",suggestionId:e._suggestion_id}),e.supports?.includes("subscriptions")&&(0,h.jsx)(_.Tooltip,{placement:"top",text:(0,i.__)("Supports recurring payments","woocommerce"),children:(0,h.jsx)("img",{className:"woocommerce-list__item-recurring-payments-icon",src:v.GZ+"images/icons/recurring-payments.svg",alt:(0,i.__)("Icon to indicate support for recurring payments","woocommerce")})})]}),(0,h.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,R.Ay)((0,g.decodeEntities)(e.description))}),c&&(0,h.jsx)(H.WooPaymentsMethodsLogos,{maxElements:10,tabletWidthBreakpoint:1080,mobileWidthBreakpoint:768,isWooPayEligible:(0,x.bU)(e)})]}),(0,h.jsx)("div",{className:"woocommerce-list__item-buttons",children:(0,h.jsxs)("div",{className:"woocommerce-list__item-buttons__actions",children:[!e.state.enabled&&!m&&(0,h.jsx)(K.S,{gatewayId:e.id,gatewayState:e.state,settingsHref:e.management._links.settings.href,onboardingHref:e.onboarding._links.onboard.href,isOffline:!1,gatewayHasRecommendedPaymentMethods:d,installingPlugin:t,incentive:l,acceptIncentive:o,setOnboardingModalOpen:s,onboardingType:e.onboarding.type}),!m&&(0,h.jsx)(K.CS,{gatewayId:e.id,settingsHref:e.management._links.settings.href,isInstallingPlugin:!!t}),m&&(0,h.jsx)(K.PE,{gatewayId:e.id,gatewayState:e.state,onboardingState:e.onboarding.state,settingsHref:e.management._links.settings.href,onboardingHref:e.onboarding._links.onboard.href,gatewayHasRecommendedPaymentMethods:d,installingPlugin:t,setOnboardingModalOpen:s,onboardingType:e.onboarding.type}),(0,x.j4)(e.id)&&!e.state.dev_mode&&e.state.account_connected&&e.onboarding.state.completed&&e.onboarding.state.test_mode&&(0,h.jsx)(K.LO,{acceptIncentive:o,installingPlugin:t,incentive:l,setOnboardingModalOpen:s,onboardingType:e.onboarding.type}),(0,x.j4)(e.id)&&!e.state.dev_mode&&e.state.account_connected&&e.onboarding.state.completed&&!e.onboarding.state.test_mode&&e.state.test_mode&&(0,h.jsx)(Z,{settingsHref:e.management._links.settings.href})]})}),(0,h.jsx)("div",{className:"woocommerce-list__item-after",children:(0,h.jsx)("div",{className:"woocommerce-list__item-after__actions",children:(0,h.jsx)(z,{label:(0,i.__)("Payment Provider Options","woocommerce"),provider:e})})})]})})},Y=({providers:e,installedPluginSlugs:t,installingPlugin:o,setUpPlugin:n,acceptIncentive:s,shouldHighlightIncentive:i,updateOrdering:r,setIsOnboardingModalOpen:c})=>(0,h.jsx)(W.q6,{items:e,className:"settings-payment-gateways__list",setItems:r,children:e.map((e=>{switch(e._type){case a.PaymentProviderType.Suggestion:const r=e,l=t.includes(e.plugin.slug);return(0,h.jsx)(W.Uq,{id:r.id,children:$({suggestion:r,installingPlugin:o,setUpPlugin:n,pluginInstalled:l,acceptIncentive:s,shouldHighlightIncentive:i})},r.id);case a.PaymentProviderType.Gateway:const d=e;return(0,h.jsx)(W.Uq,{id:e.id,children:V({gateway:d,installingPlugin:o,acceptIncentive:s,shouldHighlightIncentive:i,setIsOnboardingModalOpen:c})},e.id);case a.PaymentProviderType.OfflinePmsGroup:const m=e;return(0,h.jsx)(W.Uq,{id:m.id,children:(0,h.jsx)("div",{id:m.id,className:"transitions-disabled woocommerce-list__item clickable-list-item enter-done",onClick:()=>{window.location.href=m.management._links.settings.href},children:(0,h.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,h.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,h.jsx)(W.Gh,{}),(0,h.jsx)("img",{src:m.icon,alt:m.title+" logo"})]}),(0,h.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,h.jsx)("span",{className:"woocommerce-list__item-title",children:m.title}),(0,h.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:{__html:m.description}})]}),(0,h.jsx)("div",{className:"woocommerce-list__item-after centered no-buttons",children:(0,h.jsx)("div",{className:"woocommerce-list__item-after__actions",children:(0,h.jsx)("a",{className:"woocommerce-list__item-after__actions__arrow",href:m.management._links.settings.href,children:(0,h.jsx)(p.A,{icon:"chevron-right"})})})})]})})},m.id);default:return null}}))}),J=({providers:e,installedPluginSlugs:t,installingPlugin:o,setUpPlugin:n,acceptIncentive:s,shouldHighlightIncentive:l,updateOrdering:m,isFetching:u,businessRegistrationCountry:p,setBusinessRegistrationCountry:y,setIsOnboardingModalOpen:v})=>{var b;const{invalidateResolution:j}=(0,r.useDispatch)(a.paymentSettingsStore),{invalidateResolution:f}=(0,r.useDispatch)(a.woopaymentsOnboardingStore),[k,S]=(0,c.useState)(!1),N=(0,c.useRef)(null),O=(window.wcSettings?.admin?.preloadSettings?.general?.woocommerce_default_country||"US").split(":")[0],F=(0,c.useMemo)((()=>Object.entries(window.wcSettings.countries||[]).map((([e,t])=>({key:e,name:(0,g.decodeEntities)(t),types:[]}))).sort(((e,t)=>e.name.localeCompare(t.name)))),[]),A=O!==p,E=(0,C.A)("settings-payment-gateways__header-select-container",{"has-alert":A}),B=e=>{const t=e.target.closest(".settings-payment-gateways__header-select-container--indicator");N.current&&t!==N.current||((0,x.TH)("business_location_indicator_click",{store_country:O,business_country:p||""}),S((e=>!e)))};return(0,h.jsxs)("div",{className:"settings-payment-gateways",children:[(0,h.jsxs)("div",{className:"settings-payment-gateways__header",children:[(0,h.jsx)("div",{className:"settings-payment-gateways__header-title",children:(0,i.__)("Payment providers","woocommerce")}),(0,h.jsxs)("div",{className:E,children:[(0,h.jsx)(M,{className:"woocommerce-select-control__country",label:(0,i.__)("Business location:","woocommerce"),placeholder:"",value:null!==(b=F.find((e=>e.key===p)))&&void 0!==b?b:{key:"US",name:"United States (US)"},options:F,onChange:e=>{d()({path:a.WC_ADMIN_NAMESPACE+"/settings/payments/country",method:"POST",data:{location:e}}).then((()=>{y(e),window.wcSettings.admin.woocommerce_payments_nox_profile&&(window.wcSettings.admin.woocommerce_payments_nox_profile.business_country_code=e),j("getPaymentProviders",[e]),f("getOnboardingData",[])}))}}),A&&(0,h.jsxs)("div",{className:"settings-payment-gateways__header-select-container--indicator",tabIndex:0,role:"button",ref:N,onClick:B,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||B(e)},children:[(0,h.jsx)("div",{className:"settings-payment-gateways__header-select-container--indicator-icon",children:(0,h.jsx)(T.A,{})}),k&&(0,h.jsx)(_.Popover,{className:"settings-payment-gateways__header-select-container--indicator-popover",placement:"top-end",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{S(!1)},children:(0,h.jsx)("div",{className:"components-popover__content-container",children:(0,h.jsx)("p",{children:(0,I.A)({mixedString:(0,i.__)("Your business location does not match your store location. {{link}}Edit store location.{{/link}}","woocommerce"),components:{link:(0,h.jsx)(w.Link,{href:(0,P.getAdminLink)("admin.php?page=wc-settings&tab=general"),target:"_blank",type:"external",onClick:()=>{(0,x.TH)("business_location_popover_edit_store_location_click",{store_country:O,business_country:p||""})}})}})})})})]})]})]}),u?(0,h.jsx)(L.i,{rows:5}):(0,h.jsx)(Y,{providers:e,installedPluginSlugs:t,installingPlugin:o,setUpPlugin:n,acceptIncentive:s,shouldHighlightIncentive:l,updateOrdering:m,setIsOnboardingModalOpen:v})]})},X=({incentive:e,provider:t,onboardingUrl:o,onDismiss:s,onAccept:a,setUpPlugin:r})=>{const[l,d]=(0,c.useState)(!1),[m,u]=(0,c.useState)(!1),[p,g]=(0,c.useState)(!1),y="wc_settings_payments__banner";return(0,n.useEffect)((()=>{var o;(0,x.TH)("incentive_show",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(o=t._suggestion_id)&&void 0!==o?o:"",display_context:y})}),[e,t]),l||(0,x.$8)(e,y)||m?null:(0,h.jsx)(_.Card,{className:"woocommerce-incentive-banner",isRounded:!0,children:(0,h.jsxs)("div",{className:"woocommerce-incentive-banner__content",children:[(0,h.jsx)("div",{className:"woocommerce-incentive-banner__image",children:(0,h.jsx)("img",{src:v.GZ+"images/settings-payments/incentives-illustration.svg",alt:(0,i.__)("Incentive illustration","woocommerce")})}),(0,h.jsxs)(_.CardBody,{className:"woocommerce-incentive-banner__body",children:[(0,h.jsx)(k,{status:"has_incentive",message:(0,i.__)("Limited time offer","woocommerce")}),(0,h.jsxs)("div",{className:"woocommerce-incentive-banner__copy",children:[(0,h.jsx)("h2",{children:e.title}),(0,h.jsx)("p",{children:e.description})]}),(0,h.jsx)("div",{className:"woocommerce-incentive-banner__terms",children:(0,c.createInterpolateElement)((0,i.__)("See <termsLink /> for details.","woocommerce"),{termsLink:(0,h.jsx)(w.Link,{href:e.tc_url,target:"_blank",rel:"noreferrer",type:"external",children:(0,i.__)("Terms and Conditions","woocommerce")})})}),(0,h.jsxs)("div",{className:"woocommerce-incentive-banner__actions",children:[(0,h.jsx)(_.Button,{variant:"primary",isBusy:l,disabled:l,onClick:()=>{var n,i;(0,x.TH)("incentive_accept",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(n=t._suggestion_id)&&void 0!==n?n:"",display_context:y}),g(!0),a(e.promo_id),s(e._links.dismiss.href,y,!0),d(!0),r(t,o,"not_installed"===t.plugin.status&&null!==(i=t._links?.attach?.href)&&void 0!==i?i:null),g(!1)},children:e.cta_label}),(0,h.jsx)(_.Button,{variant:"tertiary",isBusy:p,disabled:p,onClick:()=>{g(!0),s(e._links.dismiss.href,y),g(!1),u(!0)},children:(0,i.__)("Dismiss","woocommerce")})]})]})]})})},Q=({incentive:e,provider:t,onboardingUrl:o,onAccept:s,onDismiss:a,setUpPlugin:r})=>{const[l,d]=(0,c.useState)(!1),[m,u]=(0,c.useState)(!0),p="wc_settings_payments__modal",g=(0,x.$8)(e,p);(0,n.useEffect)((()=>{var o;(0,x.TH)("incentive_show",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(o=t._suggestion_id)&&void 0!==o?o:"",display_context:p})}),[e,t]);const y=()=>{u(!1)};return g?null:(0,h.jsx)(h.Fragment,{children:m&&(0,h.jsx)(_.Modal,{title:"",className:"woocommerce-incentive-modal",onRequestClose:()=>{a(e._links.dismiss.href,p),y()},children:(0,h.jsx)(_.Card,{className:"woocommerce-incentive-modal__card",children:(0,h.jsxs)("div",{className:"woocommerce-incentive-modal__content",children:[(0,h.jsx)(_.CardMedia,{className:"woocommerce-incentive-modal__media",children:(0,h.jsx)("img",{src:v.GZ+"images/settings-payments/incentives-illustration.svg",alt:(0,i.__)("Incentive illustration","woocommerce")})}),(0,h.jsxs)(_.CardBody,{className:"woocommerce-incentive-modal__body",children:[(0,h.jsx)("div",{children:(0,h.jsx)(k,{status:"has_incentive",message:(0,i.__)("Limited time offer","woocommerce")})}),(0,h.jsx)("h2",{children:e.title}),(0,h.jsx)("p",{children:e.description}),(0,h.jsx)("p",{className:"woocommerce-incentive-modal__terms",children:(0,c.createInterpolateElement)((0,i.__)("See <termsLink /> for details.","woocommerce"),{termsLink:(0,h.jsx)(w.Link,{href:e.tc_url,target:"_blank",rel:"noreferrer",type:"external",children:(0,i.__)("Terms and Conditions","woocommerce")})})}),(0,h.jsx)("div",{className:"woocommerce-incentive-model__actions",children:(0,h.jsx)(_.Button,{variant:"primary",isBusy:l,disabled:l,onClick:()=>{var n,i;(0,x.TH)("incentive_accept",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(n=t._suggestion_id)&&void 0!==n?n:"",display_context:p}),d(!0),s(e.promo_id),a(e._links.dismiss.href,p,!0),y(),r(t,o,"not_installed"===t.plugin.status&&null!==(i=t._links?.attach?.href)&&void 0!==i?i:null),d(!1)},children:e.cta_label})})]})]})})})})};var ee=o(33068),te=o(93832);function oe({setIsOpen:e,children:t}){return(0,h.jsx)(_.Modal,{className:"settings-payments-onboarding-modal",isFullScreen:!0,__experimentalHideHeader:!0,onRequestClose:()=>e(!1),shouldCloseOnClickOutside:!1,children:t})}const ne=()=>(0,h.jsx)("svg",{className:"stripe-spinner",width:"29",height:"29",viewBox:"0 0 29 29",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M14.3308 28.3333C14.0453 28.3333 13.7714 28.2199 13.5695 28.018C13.3675 27.816 13.2541 27.5422 13.2541 27.2566C13.2541 26.971 13.3675 26.6972 13.5695 26.4952C13.7714 26.2933 14.0453 26.1799 14.3308 26.1799C17.4727 26.1799 20.4859 24.9317 22.7076 22.7101C24.9293 20.4884 26.1774 17.4752 26.1774 14.3333C26.1774 11.1914 24.9293 8.17821 22.7076 5.95655C20.4859 3.73489 17.4727 2.48677 14.3308 2.48677C12.5754 2.48495 10.8416 2.87419 9.25541 3.62623C7.66923 4.37826 6.27049 5.4742 5.16082 6.83441C5.07124 6.94388 4.96097 7.03464 4.83631 7.1015C4.71165 7.16836 4.57504 7.21001 4.43428 7.22407C4.15001 7.25248 3.8661 7.16679 3.645 6.98587C3.42391 6.80494 3.28374 6.54359 3.25534 6.25932C3.22694 5.97505 3.31262 5.69114 3.49355 5.47005C4.80533 3.86303 6.45849 2.56827 8.33301 1.67977C10.2075 0.791267 12.2564 0.331321 14.3308 0.333319C22.0626 0.333319 28.3308 6.6015 28.3308 14.3333C28.3308 22.0651 22.0626 28.3333 14.3308 28.3333Z",fill:"#4F575D"})});function se({label:e,isCompleted:t,isActive:o}){return(0,h.jsxs)("div",{className:(0,C.A)("settings-payments-onboarding-modal__sidebar--list-item",{"is-active":o,"is-completed":t}),children:[(0,h.jsx)("span",{className:"settings-payments-onboarding-modal__sidebar--list-item-icon",children:t?(0,h.jsx)("img",{src:v.GZ+"images/onboarding/icons/complete.svg",alt:(0,i.__)("Step completed","woocommerce")}):(0,h.jsx)("img",{src:v.GZ+"images/onboarding/icons/pending.svg",alt:(0,i.__)("Step active","woocommerce")})}),(0,h.jsx)("span",{className:"settings-payments-onboarding-modal__sidebar--list-item-label",children:e})]})}function ie({active:e,steps:t,justCompletedStepId:o,includeSidebar:n=!1,sidebarTitle:s}){const a=t.find((t=>t.id===e));if(!a)return null;const r=t.findIndex((t=>t.id===e))+1;return(0,h.jsxs)(h.Fragment,{children:[n&&(0,h.jsxs)("div",{className:"settings-payments-onboarding-modal__sidebar",children:[(0,h.jsxs)("div",{className:"settings-payments-onboarding-modal__sidebar--header",children:[(0,h.jsx)("h2",{className:"settings-payments-onboarding-modal__sidebar--header-title",children:s}),(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__sidebar--header-steps",children:(0,i.sprintf)((0,i.__)("Step %1$s of %2$s","woocommerce"),r,t.length)})]}),(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__sidebar--list",children:t.map((n=>(0,h.jsx)(se,{label:n.label,isCompleted:n.id===o||"completed"===n.status||r===t.length,isActive:n.id===e},n.id)))})]}),(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__content",children:(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__step",id:a.id,children:a.content})})]})}var ae=o(47804);function re({onClose:e}){return(0,h.jsxs)("div",{className:"settings-payments-onboarding-modal__header",children:[(0,h.jsx)("img",{src:`${v.GZ}images/woo-logo.svg`,alt:"Woo Logo",className:"settings-payments-onboarding-modal__header--logo"}),(0,h.jsx)(_.Button,{className:"settings-payments-onboarding-modal__header--close",onClick:e,children:(0,h.jsx)(_.Icon,{icon:ae.A})})]})}const ce=()=>{const{currentStep:e,closeModal:t}=tt(),[o,n]=(0,c.useState)(!1);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(re,{onClose:t}),(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,h.jsxs)("div",{className:"settings-payments-onboarding-modal__step--content-jetpack",children:[(0,h.jsx)("h1",{className:"settings-payments-onboarding-modal__step--content-jetpack-title",children:(0,i.__)("Connect to WordPress.com","woocommerce")}),(0,h.jsx)("p",{className:"settings-payments-onboarding-modal__step--content-jetpack-description",children:(0,i.__)("You’ll be briefly redirected to connect your store to your WordPress.com account and unlock the full features and functionality of WooPayments","woocommerce")}),(0,h.jsx)(_.Button,{variant:"primary",className:"settings-payments-onboarding-modal__step--content-jetpack-button",isBusy:o,disabled:o,onClick:()=>{var t;(0,x.W7)("woopayments_onboarding_modal_click",{step:e?.id||"",action:"connect_to_wpcom"}),n(!0),window.location.href=null!==(t=e?.actions?.auth?.href)&&void 0!==t?t:""},children:(0,i.__)("Connect","woocommerce")})]})})]})};var le=o(66087);const de=(e={})=>{const[t,o]=(0,n.useState)(e),[s,i]=(0,n.useState)({}),[a,r]=(0,n.useState)({});return{data:t,setData:e=>o((t=>({...t,...e}))),errors:s,setErrors:e=>i((t=>(0,le.omitBy)({...t,...e},le.isNil))),touched:a,setTouched:e=>r((t=>({...t,...e})))}},me=(0,n.createContext)(null),_e=({children:e,initialData:t})=>(0,h.jsx)(me.Provider,{value:de(t),children:e}),ue=()=>{const e=(0,n.useContext)(me);if(!e)throw new Error("useBusinessVerificationContext() must be used within <BusinessVerificationContextProvider>");return e},pe=(0,n.createContext)(null),ge=({children:e,...t})=>{const o=(e=>e.reduce(((e,t,o)=>{var n;return s().isValidElement(t)&&(e[null!==(n=t.props.name)&&void 0!==n?n:o]=t),e}),{}))(e),i=(({steps:e,initialStep:t,onStepChange:o,onComplete:s,onExit:i})=>{const a=Object.keys(e),{currentStep:r}=tt(),[c,l]=(0,n.useState)(null!=t?t:a[0]);if("completed"===r?.context?.sub_steps[c]?.status){const e=a.indexOf(c),t=a[e+1];l(t)}const d=(a.indexOf(c)+1)/a.length;return{currentStep:c,progress:d,nextStep:()=>{const e=a.indexOf(c),t=a[e+1];t?(l(t),o?.(t)):s?.()},prevStep:()=>{const e=a.indexOf(c),t=a[e-1];t?(l(t),o?.(t)):i?.()},exit:()=>i?.()}})({steps:o,...t}),a=o[i.currentStep];return(0,h.jsx)(pe.Provider,{value:i,children:a})},he=()=>{const e=(0,n.useContext)(pe);if(!e)throw new Error("useStepperContext() must be used within <Stepper>");return e},ye=e=>e?.name||"",we=({selectedItem:e},{type:t,changes:o,props:{items:n}})=>{switch(t){case O.WM.stateChangeTypes.ToggleButtonKeyDownArrowDown:return{selectedItem:n[e?Math.min(n.indexOf(e)+1,n.length-1):0]};case O.WM.stateChangeTypes.ToggleButtonKeyDownArrowUp:return{selectedItem:n[e?Math.max(n.indexOf(e)-1,0):n.length-1]};default:return o}},ve=function({name:e,className:t,label:o,describedBy:n,options:s,onChange:a,value:r,placeholder:l,children:d}){const{getLabelProps:m,getToggleButtonProps:u,getMenuProps:p,getItemProps:g,isOpen:y,highlightedIndex:w,selectedItem:v}=(0,O.WM)({initialSelectedItem:s[0],items:s,itemToString:ye,onSelectedItemChange:a,selectedItem:r||{},stateReducer:we}),x=ye(v),b=p({className:"components-custom-select-control__menu","aria-hidden":!y}),f=(0,c.useCallback)((e=>{e.stopPropagation(),b?.onKeyDown?.(e)}),[b]);return b["aria-activedescendant"]?.startsWith("downshift-null")&&delete b["aria-activedescendant"],(0,h.jsxs)("div",{className:(0,C.A)("woopayments components-custom-select-control",t),children:[(0,h.jsx)("label",{...m({className:"components-custom-select-control__label"}),children:o}),(0,h.jsxs)(_.Button,{...u({"aria-label":o,"aria-labelledby":void 0,"aria-describedby":n||(x?(0,i.sprintf)((0,i.__)("Currently selected: %s","woocommerce"),x):(0,i.__)("No selection","woocommerce")),className:(0,C.A)("components-custom-select-control__button",{placeholder:!x}),name:e}),children:[(0,h.jsx)("span",{className:"components-custom-select-control__button-value",children:x||l}),(0,h.jsx)(j.A,{icon:A.A,className:"components-custom-select-control__button-icon"})]}),(0,h.jsx)("ul",{...b,onKeyDown:f,children:y&&s.map(((e,t)=>(0,h.jsxs)("li",{...g({item:e,index:t,className:(0,C.A)(e.className,"components-custom-select-control__item",{"is-highlighted":t===w}),style:e.style}),children:[d?d(e):e.name,e===v&&(0,h.jsx)(j.A,{icon:E.A,className:"components-custom-select-control__item-icon"})]},e.key)))})]})};var xe=o(56537);const be=({name:e,className:t,label:o,options:s,onChange:a,value:r,placeholder:c,searchable:l})=>{const d=(0,n.useRef)(null),m=(0,n.useRef)(),_=s.filter((e=>e.items?.length)).map((e=>e.key)),[u,p]=(0,n.useState)(new Set([_[0]])),[g,y]=(0,n.useState)(new Set([..._,...s[0]?.items||[]])),[w,v]=(0,n.useState)(""),x=s.filter((e=>g.has(e.key))),{isOpen:b,selectedItem:f,getToggleButtonProps:k,getMenuProps:S,getLabelProps:N,highlightedIndex:P,getItemProps:T}=(0,O.WM)({items:x,itemToString:e=>e?.name||"",selectedItem:r||{},onSelectedItemChange:a,stateReducer:(e,{changes:t,type:o})=>{if(l&&o===O.WM.stateChangeTypes.ToggleButtonKeyDownArrowDown)return e;if(t.selectedItem&&t.selectedItem.items){if(w)return e;const o=t.selectedItem.key;return u.has(o)?(u.delete(o),t.selectedItem.items.forEach((e=>g.delete(e)))):(u.add(o),t.selectedItem.items.forEach((e=>g.add(e)))),p(u),y(g),e}return t}}),I=S({className:"components-grouped-select-control__list","aria-hidden":!b,onFocus:()=>d.current?.focus(),onBlur:e=>{e.relatedTarget===d.current&&(e.nativeEvent.preventDownshiftDefault=!0)},onKeyDown:e=>{"Space"===e.code&&(e.nativeEvent.preventDownshiftDefault=!0)}});return(0,h.jsxs)("div",{className:(0,C.A)("woopayments components-grouped-select-control",t),children:[(0,h.jsx)("label",{...N({className:"components-grouped-select-control__label"}),children:o}),(0,h.jsxs)("button",{...k({type:"button",className:(0,C.A)("components-text-control__input components-grouped-select-control__button",{placeholder:!f?.name}),name:e}),children:[(0,h.jsx)("span",{className:"components-grouped-select-control__button-value",children:f?.name||c}),(0,h.jsx)(j.A,{icon:A.A,className:"components-grouped-select-control__button-icon"})]}),(0,h.jsx)("ul",{...I,children:b&&(0,h.jsxs)(h.Fragment,{children:[l&&(0,h.jsx)("input",{className:"components-grouped-select-control__search",ref:d,type:"text",value:w,onChange:({target:e})=>{if(m.current||(m.current={visibleItems:g}),""===e.value)y(m.current.visibleItems),m.current=void 0;else{const t=s.filter((t=>t?.group&&`${t.name} ${t.context||""}`.toLowerCase().includes(e.value.toLowerCase()))),o=t.map((e=>e?.group||"")),n=new Set([...t.map((e=>e.key)),...o]);y(n)}v(e.value)},tabIndex:-1,placeholder:(0,i.__)("Search…","woocommerce")}),(0,h.jsx)("div",{className:"components-grouped-select-control__list-container",children:x.map(((e,t)=>{const o=!!e.items;return(0,h.jsxs)("li",{...T({item:e,index:t,className:(0,C.A)("components-grouped-select-control__item",e.className,{"is-highlighted":t===P},{"is-group":o})}),children:[(0,h.jsx)("div",{className:"components-grouped-select-control__item-content",children:e.name}),e.key===f?.key&&(0,h.jsx)(j.A,{icon:E.A}),!w&&o&&(0,h.jsx)(j.A,{icon:u.has(e.key)?xe.A:A.A})]},e.key)}))})]})})]})},je=(e,t,o)=>{const{error:n,...s}=t;return n?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(e,{...s,ref:o,className:(0,C.A)(s.className,"has-error")}),(0,h.jsx)("div",{className:"components-form-field__error",children:n})]}):(0,h.jsx)(e,{...s,ref:o})},fe=((0,n.forwardRef)(((e,t)=>je(_.TextControl,e,t))),e=>je(ve,e)),ke=e=>je(be,e),Se={generic:{individual:(0,i.__)("Select if you run your own business as an individual and are self-employed","woocommerce"),company:(0,i.__)("Select if you filed documentation to register your business with a government agency","woocommerce"),non_profit:(0,i.__)("Select if you run a non-business entity","woocommerce"),government_entity:(0,i.__)("Select if your business is classed as a government entity","woocommerce")},US:{individual:(0,i.__)("Select if you run your own business as an individual and are self-employed","woocommerce"),company:(0,i.__)("Select if you filed documentation to register your business with a government agency","woocommerce"),non_profit:(0,i.__)("Select if you have been granted tax-exempt status by the Internal Revenue Service (IRS)","woocommerce"),government_entity:(0,i.__)("Select if your business is classed as a government entity","woocommerce")}},Ne=e=>{const t=window.wcSettings.admin?.onboarding?.profile?.industry?.[0];if(t)return e[t]},Ce=()=>{const{woocommerce_share_key:e,woocommerce_coming_soon:t,woocommerce_private_link:o}=window.wcSettings?.admin?.siteVisibilitySettings||{};return"yes"!==t||"no"===o?"":e?"?woo-share="+e:""},Pe={steps:{activate:{heading:(0,i.__)("Start accepting real payments","woocommerce"),subheading:(0,I.A)({mixedString:(0,i.__)("You are currently testing payments on your store. To activate real payments, you will need to provide some additional details about your business. {{link}}Learn more{{/link}}.","woocommerce"),components:{link:(0,h.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://woocommerce.com/document/woopayments/startup-guide/#sign-up-process"})}}),cta:(0,i.__)("Activate payments","woocommerce")},business:{heading:(0,i.__)("Let’s get your store ready to accept payments","woocommerce"),subheading:(0,i.__)("We’ll use these details to enable payments for your store. This information can’t be changed after your account is created.","woocommerce")},store:{heading:(0,i.__)("Please share a few more details","woocommerce"),subheading:(0,i.__)("This info will help us speed up the set up process.","woocommerce")},loading:{heading:(0,i.__)("One last step! Verify your identity with our partner","woocommerce"),subheading:(0,i.__)("This will take place in a secure environment through our partner. Once your business details are verified, you’ll be redirected back to your store dashboard.","woocommerce"),cta:(0,i.__)("Finish your verification process","woocommerce")},embedded:{heading:(0,i.__)("One last step! Verify your identity with our partner","woocommerce"),subheading:(0,i.__)("This info will verify your account","woocommerce")}},fields:{country:(0,i.__)("Where is your business located?","woocommerce"),business_type:(0,i.__)("What type of legal entity is your business?","woocommerce"),"company.structure":(0,i.__)("What category of legal entity identify your business?","woocommerce"),mcc:(0,i.__)("What type of goods or services does your business sell? ","woocommerce")},errors:{generic:(0,i.__)("Please provide a response","woocommerce"),country:(0,i.__)("Please provide a country","woocommerce"),business_type:(0,i.__)("Please provide a business type","woocommerce"),mcc:(0,i.__)("Please provide a type of goods or services","woocommerce")},placeholders:{generic:(0,i.__)("Select an option","woocommerce"),country:(0,i.__)("Select a country","woocommerce")},tos:(0,I.A)({mixedString:(0,i.sprintf)((0,i.__)("By using %1$s, you agree to be bound by our {{tosLink}}Terms of Service{{/tosLink}} (including {{merchantTermsLink}}%2$s merchant terms{{/merchantTermsLink}}) and acknowledge that you have read our {{privacyPolicyLink}}Privacy Policy{{/privacyPolicyLink}}.","woocommerce"),"WooPayments","WooPay"),components:{tosLink:(0,h.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://wordpress.com/tos/"}),merchantTermsLink:(0,h.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://wordpress.com/tos/#more-woopay-specifically"}),privacyPolicyLink:(0,h.jsx)("a",{rel:"external noopener noreferrer",target:"_blank",href:"https://automattic.com/privacy/"})}}),continue:(0,i.__)("Continue","woocommerce"),back:(0,i.__)("Back","woocommerce"),cancel:(0,i.__)("Cancel","woocommerce")},Te=e=>{const{data:t,errors:o,setErrors:s,touched:i,setTouched:a}=ue(),r=(o=t[e])=>{i[e]||a({[e]:!0});const n=((e,t)=>!!t)(0,o)?void 0:Pe.errors[e]||Pe.errors.generic;s({[e]:n})};return(0,n.useEffect)((()=>(r(),t[e]||a({[e]:!1}),()=>s({[e]:void 0}))),[]),{validate:r,error:()=>i[e]?o[e]:void 0}},Ie=({children:e})=>{const{data:t,errors:o,touched:n,setTouched:s}=ue(),{currentStep:i}=tt(),{nextStep:a}=he();return(0,h.jsxs)("form",{onSubmit:e=>{e.preventDefault(),(()=>{var e,r;if((0,le.isEmpty)(o)&&(e=>["business_type","country","mcc"].every((t=>Boolean(e[t]))))(t))return(0,x.W7)("woopayments_onboarding_modal_kyc_sub_step_completed",{sub_step_id:"business",country:t.country||"",business_type:t.business_type||"",mcc:t.mcc||""}),((e,t,o)=>{t&&d()({url:t,method:"POST",data:{sub_steps:{...o,[e]:{status:"completed"}}}})})("business",null!==(e=i?.actions?.save?.href)&&void 0!==e?e:void 0,null!==(r=i?.context?.sub_steps)&&void 0!==r?r:{}),a();s((0,le.mapValues)(n,(()=>!0)))})()},children:[e,(0,h.jsx)(_.Button,{variant:"primary",type:"submit",className:"stepper__cta",onClick:()=>{var e;(0,x.W7)("woopayments_onboarding_modal_click",{step_id:null!==(e=i?.id)&&void 0!==e?e:"",sub_step_id:"business",action:"business_form_continue"})},children:Pe.continue})]})},Oe=({onChange:e,...t})=>{var o;const{name:n}=t,{data:s,setData:i}=ue(),{validate:a,error:r}=Te(n);return(0,h.jsx)(fe,{label:Pe.fields[n],value:t.options?.find((e=>e.key===s[n])),placeholder:null!==(o=Pe.placeholders[n])&&void 0!==o?o:Pe.placeholders.generic,onChange:({selectedItem:t})=>{e?e?.(n,t):i({[n]:t?.key}),a(t?.key)},options:[],error:r(),...t})},Fe=({onChange:e,...t})=>{var o;const{name:n}=t,{data:s,setData:i}=ue(),{validate:a,error:r}=Te(n);return(0,h.jsx)(ke,{label:Pe.fields[n],value:t.options?.find((e=>e.key===s[n])),placeholder:null!==(o=Pe.placeholders[n])&&void 0!==o?o:Pe.placeholders.generic,onChange:({selectedItem:t})=>{e?e?.(n,t):i({[n]:t?.key}),a(t?.key)},options:[],error:r(),...t})},Ae=()=>{var e;const{data:t,setData:o}=ue(),{currentStep:n}=tt(),s=(e=>Object.entries(e||[]).map((([e,t])=>({key:e,name:t,types:[]}))).sort(((e,t)=>e.name.localeCompare(t.name))))(n?.context?.fields?.available_countries||{}),i=(e=>(e||[]).map((e=>({...e,types:e.types.map((t=>({...t,description:Se[e.key]?Se[e.key][t.key]:Se.generic[t.key]})))}))).sort(((e,t)=>e.name.localeCompare(t.name)))||[])(n?.context?.fields?.business_types||[]),a=((null!==(e=n?.context?.fields?.mccs_display_tree)&&void 0!==e?e:[])||[]).filter((e=>!!e?.items&&(e.items?.filter((e=>!e?.items))||[]).length)).reduce(((e,t)=>{const o=t.items?.map((e=>({key:e.id,name:e.title,group:t.id,context:e?.keywords?e.keywords.join(" "):""})))||[];return[...e,{key:t.id,name:t.title,items:o.map((e=>e.key))},...o]}),[]),r=i.find((e=>"PR"===t.country?"US"===e.key:e.key===t.country)),c=r?.types.sort(((e,t)=>"company"===e.key?-1:"company"===t.key?1:0)),l=c?.find((e=>e.key===t.business_type)),m=0===l?.structures.length||l?.structures.find((e=>e.key===t["company.structure"])),_=e=>{const t=n?.actions?.save?.href;t&&d()({url:t,method:"POST",data:{self_assessment:e}})},u=(e,t)=>{let n={[e]:t?.key};"business_type"===e?n={...n,"company.structure":void 0}:"country"===e&&(n={...n,business_type:void 0}),o(n),_(n)};return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("span",{"data-testid":"country-select",children:(0,h.jsx)(Oe,{name:"country",options:s,onChange:u})}),r&&r.types.length>0&&(0,h.jsx)("span",{"data-testid":"business-type-select",children:(0,h.jsx)(Oe,{name:"business_type",options:r.types,onChange:u,children:e=>(0,h.jsxs)("div",{children:[(0,h.jsx)("div",{children:e.name}),(0,h.jsx)("div",{className:"complete-business-info-task__option-description",children:e.description})]})})}),l&&l.structures.length>0&&(0,h.jsx)("span",{"data-testid":"business-structure-select",children:(0,h.jsx)(Oe,{name:"company.structure",options:l.structures,onChange:u})}),r&&l&&m&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("span",{"data-testid":"mcc-select",children:(0,h.jsx)(Fe,{name:"mcc",options:a,onChange:(e,n)=>{o({[e]:n?.key});const s={...t,[e]:n?.key};_(s)},searchable:!0})}),(0,h.jsx)("span",{className:"woopayments-onboarding__tos",children:Pe.tos})]})]})};var Ee=o(20195),Be=o(94736),De=o(92991),Me=o(8181);E.A,De.A,Be.A,f.A;const Le=({children:e,actions:t=[],className:o,status:n="info",isDismissible:s=!0,onRemove:a})=>{((e,t)=>{const o="string"==typeof t?t:(0,c.renderToString)(t),n="error"===e?"assertive":"polite";(0,c.useEffect)((()=>{o&&(0,Ee.speak)(o,n)}),[o,n])})(n,e);const r=(0,C.A)(o,"woopayments-banner-notice","is-"+n);return(0,h.jsxs)("div",{className:r,children:[(0,h.jsxs)("div",{className:"woopayments-banner-notice__content",children:[e,t.length>0&&(0,h.jsx)("div",{className:"woopayments-banner-notice__actions",children:t.map((({className:e,label:t,variant:o,onClick:n,url:s,urlTarget:i},a)=>{let r=o;return"primary"!==o&&(r=s?"link":"secondary"),(0,h.jsx)(_.Button,{href:s,variant:r,onClick:s?void 0:n,className:e,target:i,children:t},a)}))})]}),s&&(0,h.jsx)(_.Button,{className:"woopayments-banner-notice__dismiss",icon:(0,h.jsx)(Me.A,{}),label:(0,i.__)("Dismiss this notice","woocommerce"),onClick:()=>a?.(),showTooltip:!1})]})};var We=o(2929),He=o(86948);const Re={variables:{colorPrimary:"#873EFF",colorBackground:"#FFFFFF",buttonPrimaryColorBackground:"#3858E9",buttonPrimaryColorBorder:"#3858E9",buttonPrimaryColorText:"#FFFFFF",buttonSecondaryColorBackground:"#FFFFFF",buttonSecondaryColorBorder:"#3858E9",buttonSecondaryColorText:"#3858E9",colorText:"#101517",colorSecondaryText:"#50575E",actionPrimaryColorText:"#3858E9",actionSecondaryColorText:"#101517",colorBorder:"#DDDDDD",formHighlightColorBorder:"#3858E9",formAccentColor:"#3858E9",colorDanger:"#CC1818",offsetBackgroundColor:"#F0F0F0",formBackgroundColor:"#FFFFFF",badgeNeutralColorText:"#2C3338",badgeNeutralColorBackground:"#F6F7F7",badgeNeutralColorBorder:"#F6F7F7",badgeSuccessColorText:"#005C12",badgeSuccessColorBackground:"#EDFAEF",badgeSuccessColorBorder:"#EDFAEF",badgeWarningColorText:"#614200",badgeWarningColorBackground:"#FCF9E8",badgeWarningColorBorder:"#FCF9E8",badgeDangerColorText:"#8A2424",badgeDangerColorBackground:"#FCF0F1",badgeDangerColorBorder:"#FCF0F1",borderRadius:"2px",buttonBorderRadius:"2px",formBorderRadius:"2px",badgeBorderRadius:"2px",overlayBorderRadius:"8px",spacingUnit:"10px",fontFamily:"-apple-system, BlinkMacSystemFont, 'system-ui', 'Segoe UI', 'Helvetica Neue', 'Helvetica', 'Roboto', 'Arial', sans-serif",fontSizeBase:"16px",headingXlFontSize:"32px",headingXlFontWeight:"400",headingLgFontSize:"24px",headingLgFontWeight:"400",headingMdFontSize:"20px",headingMdFontWeight:"400",headingSmFontSize:"13px",headingSmFontWeight:"600",headingXsFontSize:"12px",headingXsFontWeight:"600",bodyMdFontWeight:"400",bodyMdFontSize:"16px",bodySmFontSize:"13px",bodySmFontWeight:"400",labelSmFontSize:"12px",labelSmFontWeight:"200",labelMdFontSize:"13px"}},Ue=({onboardingData:e,onExit:t,onLoaderStart:o,onLoadError:s,onStepChange:a,collectPayoutRequirements:r=!1})=>{const{stripeConnectInstance:c,initializationError:l}=(e=>{const[t,o]=(0,n.useState)(null),{currentStep:s}=tt(),[a,r]=(0,n.useState)(null),[c,l]=(0,n.useState)(!0);return(0,n.useEffect)((()=>{(async()=>{try{var t;const n=await(async(e,t)=>{const o=(n=e,(0,le.toPairs)(n).reduce(((e,[t,o])=>null!==o?(0,le.set)(e,t,o):e),{}));var n;const s={};return Object.keys(o).length>0&&(s.self_assessment=o),await d()({url:t,method:"POST",data:s})})(e,null!==(t=s?.actions?.kyc_session?.href)&&void 0!==t?t:""),{clientSecret:a,publishableKey:r}=n.session;if(!r)throw new Error((0,i.__)("Unable to start onboarding. If this problem persists, please contact support.","woocommerce"));const c=(0,We.e)({publishableKey:r,fetchClientSecret:async()=>a,appearance:{overlays:"drawer",...Re},locale:n.session.locale.replace("_","-")});o(c)}catch(e){r(e instanceof Error?e.message:(0,i.__)("Unable to start onboarding. If this problem persists, please contact support.","woocommerce"))}finally{l(!1)}})()}),[e]),{stripeConnectInstance:t,initializationError:a,loading:c}})(e);return(0,h.jsxs)(h.Fragment,{children:[l&&(0,h.jsx)(Le,{status:"error",children:l}),c&&(0,h.jsx)(He.MT,{connectInstance:c,children:(0,h.jsx)(He.hw,{onLoaderStart:o,onLoadError:s,onExit:t,onStepChange:e=>a?.(e.step),collectionOptions:{fields:r?"eventually_due":"currently_due",futureRequirements:"omit"}})})]})},Ge=({collectPayoutRequirements:e=!1})=>{var t;const{data:o}=ue(),{currentStep:s,navigateToNextStep:a}=tt(),[r,c]=(0,n.useState)(!1),[l,m]=(0,n.useState)(!0),[_,u]=(0,n.useState)(null),p=null!==(t=s?.actions?.kyc_fallback?.href)&&void 0!==t?t:"";return(0,h.jsxs)(h.Fragment,{children:[_&&("invalid_request_error"===_.error.type?(0,h.jsx)(Le,{className:"woopayments-banner-notice--embedded-kyc",status:"warning",isDismissible:!1,actions:[{label:"Learn more",variant:"primary",url:"https://woocommerce.com/document/woopayments/startup-guide/#requirements",urlTarget:"_blank"},{label:"Cancel",variant:"link",url:p}],children:(0,i.__)("Payment activation through our financial partner requires HTTPS and cannot be completed.","woocommerce")}):(0,h.jsx)(Le,{className:"woopayments-banner-notice--embedded-kyc",status:"error",isDismissible:!1,children:_.error.message})),l&&(0,h.jsx)("div",{className:"embedded-kyc-loader-wrapper padded",children:(0,h.jsx)(ne,{})}),r&&(0,h.jsx)("div",{className:"embedded-kyc-loader-wrapper",children:(0,h.jsx)(ne,{})}),(0,h.jsx)(Ue,{onExit:async()=>{c(!0);try{var e;(await(async e=>await d()({url:e,method:"POST",data:{}}))(null!==(e=s?.actions?.kyc_session_finish?.href)&&void 0!==e?e:"")).success?a():window.location.href=p}catch(e){window.location.href=p}},onStepChange:t=>{(0,x.W7)("woopayments_onboarding_modal_kyc_step_change",{kyc_step_id:t,collect_payout_requirements:e})},onLoaderStart:()=>{(0,x.W7)("woopayments_onboarding_modal_kyc_started_loading",{collect_payout_requirements:e}),m(!1)},onLoadError:t=>{(0,x.W7)("woopayments_onboarding_modal_kyc_load_error",{error_type:t.error.type,error_message:t.error.message||"",collect_payout_requirements:e}),u(t)},onboardingData:o,collectPayoutRequirements:e})]})},qe=()=>{const{currentStep:e}=tt(),{nextStep:t}=he(),[o,s]=(0,n.useState)(!1);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("h1",{className:"stepper__heading",children:Pe.steps.activate.heading}),(0,h.jsx)("p",{className:"stepper__subheading",children:Pe.steps.activate.subheading}),(0,h.jsx)("div",{className:"stepper__content",children:(0,h.jsx)(_.Button,{variant:"primary",className:"stepper__cta",onClick:()=>{(0,x.W7)("woopayments_onboarding_modal_click",{step:e?.id||"",sub_step_id:"activate",action:"activate_payments"}),s(!0),(0,x.AC)().then((()=>(s(!1),t()))).catch((()=>{s(!1)}))},isBusy:o,disabled:o,children:Pe.steps.activate.cta})})]})},ze=({name:e,children:t,showHeading:o=!0})=>(0,h.jsx)(h.Fragment,{children:(0,h.jsxs)("div",{className:"stepper__wrapper",children:[o&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("h1",{className:"stepper__heading",children:Pe.steps[e].heading}),(0,h.jsx)("h2",{className:"stepper__subheading",children:Pe.steps[e].subheading})]}),(0,h.jsx)("div",{className:"stepper__content",children:t})]})}),$e=()=>{var e,t,o;const{currentStep:n,closeModal:s}=tt(),i={business_name:window.wcSettings?.siteTitle,mcc:Ne(null!==(e=n?.context?.fields?.mccs_display_tree)&&void 0!==e?e:[]),site:"localhost"===location.hostname?"https://wcpay.test":window.wcSettings?.homeUrl+Ce(),country:n?.context?.fields?.location,...null!==(t=n?.context?.self_assessment)&&void 0!==t?t:{}},a=null!==(o=n?.context?.has_test_account)&&void 0!==o&&o;return(0,h.jsxs)("div",{className:"settings-payments-onboarding-modal__step-business-verification",children:[(0,h.jsx)(re,{onClose:s}),(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__step-business-verification-content",children:(0,h.jsx)(_e,{initialData:i,children:(0,h.jsxs)(ge,{onStepChange:()=>window.scroll(0,0),onExit:()=>{},children:[a&&(0,h.jsx)(ze,{name:"activate",showHeading:!1,children:(0,h.jsx)(qe,{})}),(0,h.jsx)(ze,{name:"business",children:(0,h.jsx)(Ie,{children:(0,h.jsx)(Ae,{})})}),(0,h.jsx)(ze,{name:"embedded",showHeading:!1,children:(0,h.jsx)(Ge,{})})]})})})]})};var Ke=o(83306),Ze=o(1275);function Ve(){const{currentStep:e,navigateToNextStep:t,closeModal:o}=tt(),[n,s]=(0,c.useState)(!1),[a,r]=(0,c.useState)({}),[l,m]=(0,c.useState)(null),[u,p]=(0,c.useState)(!1),g=e?.context?.pms_state,y=e?.context?.recommended_pms,w=(0,c.useMemo)((()=>y?(0,x.js)(y):[]),[y]),v=(0,c.useRef)(null),[b,j]=(0,c.useState)(!1);(0,c.useEffect)((()=>{g&&r(g)}),[g]);const f=(0,c.useMemo)((()=>(0,x.LI)(a)),[a]);(0,c.useEffect)((()=>{if(null===l&&w.length>0&&Object.keys(f).length>0&&w.every((e=>void 0!==f[e.id]))){const e={};w.forEach((t=>{e[t.id]=(0,x.TO)(t,f[t.id])})),m(e)}}),[w,f,l]);const k=(0,c.useMemo)((()=>!l||n?0:w.filter((e=>{var t;return!(null!==(t=l[e.id])&&void 0!==t&&t)})).length),[w,n,l]),S=t=>{r(t);const o=e?.actions?.save?.href;o&&d()({url:o,method:"POST",data:{payment_methods:t}})},N=()=>{const e=v.current;e&&j(e.scrollHeight>e.clientHeight)};return(0,c.useEffect)((()=>{const e=setTimeout((()=>{N()}),0);return window.addEventListener("resize",N),()=>{clearTimeout(e),window.removeEventListener("resize",N)}}),[]),(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,h.jsxs)("div",{className:"woocommerce-layout__header woocommerce-recommended-payment-methods",children:[(0,h.jsxs)("div",{className:"woocommerce-layout__header-wrapper",children:[(0,h.jsxs)("div",{className:"woocommerce-layout__header-title-and-close",children:[(0,h.jsx)("h1",{className:"components-truncate components-text woocommerce-layout__header-heading woocommerce-layout__header-left-align woocommerce-settings-payments-header__title",children:(0,i.__)("Choose your payment methods","woocommerce")}),(0,h.jsx)(_.Button,{className:"settings-payments-onboarding-modal__header--close",onClick:o,children:(0,h.jsx)(_.Icon,{icon:ae.A})})]}),(0,h.jsx)("div",{className:"woocommerce-settings-payments-header__description",children:(0,i.__)("Select which payment methods you'd like to offer to your shoppers. You can update these at any time.","woocommerce")})]}),(0,h.jsx)("div",{className:"woocommerce-recommended-payment-methods__list",children:(0,h.jsxs)("div",{className:"settings-payments-methods__container",ref:v,children:[(0,h.jsx)("div",{className:"woocommerce-list",children:w?.map((e=>{var t;return(0,h.jsx)(Ze.v,{method:e,paymentMethodsState:(0,x.LI)(a),setPaymentMethodsState:e=>{r(e),S(e)},initialVisibilityStatus:l&&null!==(t=l[e.id])&&void 0!==t?t:null,isExpanded:n},e.id)}))}),!n&&k>0&&(0,h.jsx)("div",{className:"settings-payments-methods__show-more--wrapper",children:(0,h.jsx)(_.Button,{className:"settings-payments-methods__show-more",onClick:()=>{(0,x.W7)("woopayments_onboarding_modal_click",{step:e?.id||"",action:"show_more",hidden_count:k}),s(!n),setTimeout((()=>{N()}),0)},tabIndex:0,"aria-expanded":n,children:(0,i.sprintf)((0,i.__)("Show more (%s)","woocommerce"),k)})})]})}),(0,h.jsx)("div",{className:(0,C.A)("woocommerce-recommended-payment-methods__list_footer",{"has-border":b}),children:(0,h.jsx)(_.Button,{className:"components-button is-primary",onClick:()=>{const o=e?.actions?.finish?.href;o&&(S(a),p(!0),d()({url:o,method:"POST"}).then((()=>{var e;(0,Ke.recordEvent)("wcpay_settings_payment_methods_continue",{displayed_payment_methods:Object.keys(a).join(", "),selected_payment_methods:Object.keys(a).filter((e=>a[e])).join(", "),deselected_payment_methods:Object.keys(a).filter((e=>!a[e])).join(", "),business_country:null!==(e=window.wcSettings?.admin?.woocommerce_payments_nox_profile?.business_country_code)&&void 0!==e?e:"unknown"}),p(!1),t()})))},isBusy:u,disabled:u,children:(0,i.__)("Continue","woocommerce")})})]})})}const Ye=({progress:e,message:t})=>(0,h.jsx)(H.Loader,{className:"woocommerce-payments-test-account-step__preloader",children:(0,h.jsxs)(H.Loader.Layout,{className:"woocommerce-payments-test-account-step__preloader-layout",children:[(0,h.jsx)(H.Loader.Illustration,{children:(0,h.jsx)("img",{src:`${v.GZ}images/onboarding/test-account-setup.svg`,alt:"setup",style:{maxWidth:"223px"}})}),(0,h.jsx)(H.Loader.Title,{children:(0,i.__)("Finishing payments setup","woocommerce")}),(0,h.jsx)(H.Loader.ProgressBar,{progress:null!=e?e:0}),(0,h.jsx)(H.Loader.Sequence,{interval:0,children:t||(0,i.__)("In just a few moments, you'll be ready to test payments on your store.","woocommerce")})]})}),Je=()=>{const{currentStep:e,navigateToNextStep:t,closeModal:o,refreshStoreData:s,setJustCompletedStepId:a}=tt(),[r,c]=(0,n.useState)("idle"),[l,u]=(0,n.useState)(20),[p,g]=(0,n.useState)(),[y,b]=(0,n.useState)(0),[j,f]=(0,n.useState)(0),k=(0,n.useRef)(null),S=(0,n.useRef)(null),N=(0,n.useRef)(null),C=()=>{null!==k.current&&(clearTimeout(k.current),k.current=null),null!==N.current&&(clearTimeout(N.current),N.current=null)},[P,T]=(0,n.useState)(!1),O=(0,n.useCallback)((()=>{c("idle"),u(0),g(void 0),b(0),S.current=null,C()}),[c,u,g,b]);return(0,n.useEffect)((()=>{if("idle"===r){if("completed"===e?.status)return c("success"),a(e.id),void u(100);if("blocked"===e?.status)return g(e?.errors?.[0]?.message||(0,i.__)("There are environment or store setup issues which are blocking progress. Please resolve them to proceed.","woocommerce")),void c("blocked");"not_started"===e?.status||"failed"===e?.status?(c("initializing"),u(10),(async()=>{e?.actions?.clean?.href&&(j>0||"failed"===e?.status)&&await d()({url:e?.actions?.clean?.href,method:"POST"})})().then((()=>d()({url:e?.actions?.init?.href,method:"POST"}))).then((e=>{e?.success?c("polling"):(g(e?.message||(0,i.__)("Creating test account failed. Please try again.","woocommerce")),c("error"))})).catch((e=>{g(e.message),c("error")}))):c("polling")}if("polling"===r){const t=()=>{C(),d()({url:e?.actions?.check?.href,method:"POST"}).then((o=>{if("completed"===o?.status)return void(k.current=window.setTimeout((()=>{c("success"),u(100),a(e?.id||"")}),1e3));let n=y,s=3e3,i=0;u((e=>(i=0===y?Math.min(e+5,90):1===y?Math.min(e+1,96):e,i))),0===y&&i>=90?(n=1,s=5e3,S.current=Date.now()):1===y?S.current&&Date.now()-S.current>3e4?(n=2,s=7e3):(n=1,s=5e3):2===y?(n=2,s=7e3):(n=0,s=3e3),b(n),k.current=window.setTimeout(t,s)})).catch((e=>{g(e.message),c("error"),C()}))};t()}return"initializing"===r&&null===N.current&&(N.current=window.setInterval((()=>{u((e=>e<30?Math.min(e+2,30):e))}),1e3)),"initializing"!==r&&null!==N.current&&(clearTimeout(N.current),N.current=null),()=>{C()}}),[r,e,j,y,a]),"success"===r?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(re,{onClose:o}),(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,h.jsx)("div",{className:"woocommerce-payments-test-account-step__success_content_container",children:(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content woocommerce-payments-test-account-step__success_content",children:[(0,h.jsx)("h1",{className:"woocommerce-payments-test-account-step__success_content_title",children:(0,i.__)("You're ready to test payments!","woocommerce")}),(0,h.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,h.jsx)("div",{className:"woocommerce-woopayments-modal__content__item__description",children:(0,h.jsx)("p",{children:(0,I.A)({mixedString:(0,i.__)("We've created a test account for you so that you can begin {{link}}testing payments on your store{{/link}}.","woocommerce"),components:{link:(0,h.jsx)(w.Link,{href:"https://woocommerce.com/document/woopayments/testing-and-troubleshooting/sandbox-mode/",target:"_blank",rel:"noreferrer",type:"external"}),break:(0,h.jsx)("br",{})}})})})}),(0,h.jsxs)("div",{className:"woocommerce-payments-test-account-step__success-whats-next",children:[(0,h.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,h.jsx)("h2",{children:(0,i.__)("What's next:","woocommerce")})}),(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,h.jsx)("img",{src:v.GZ+"images/icons/store.svg",alt:"store icon"}),(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,h.jsx)("h3",{children:(0,i.__)("Continue your store setup","woocommerce")}),(0,h.jsx)("div",{children:(0,i.__)("Finish completing the tasks required to launch your store.","woocommerce")})]})]}),(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,h.jsx)("img",{src:v.GZ+"images/icons/dollar.svg",alt:"dollar icon"}),(0,h.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,h.jsx)("h3",{children:(0,i.__)("Activate payments","woocommerce")}),(0,h.jsx)("div",{children:(0,h.jsx)("p",{children:(0,I.A)({mixedString:(0,i.__)("Provide some additional details about your business so you can being accepting real payments. {{link}}Learn more{{/link}}","woocommerce"),components:{link:(0,h.jsx)(w.Link,{href:"https://woocommerce.com/document/woopayments/startup-guide/#sign-up-process",target:"_blank",rel:"noreferrer",type:"external"})}})})})]})]})]}),(0,h.jsx)(_.Button,{variant:"primary",onClick:()=>{(0,x.W7)("woopayments_onboarding_modal_click",{step:e?.id||"",action:"continue_store_setup"}),(0,m.navigateTo)({url:(0,m.getNewPath)({},"",{page:"wc-admin"})})},children:(0,i.__)("Continue store setup","woocommerce")}),(0,h.jsxs)("div",{className:"woocommerce-payments-test-account-step__success_content_or-divider",children:[(0,h.jsx)("hr",{}),(0,i.__)("OR","woocommerce"),(0,h.jsx)("hr",{})]}),(0,h.jsx)(_.Button,{variant:"secondary",isBusy:P,disabled:P,onClick:()=>{(0,x.W7)("woopayments_onboarding_modal_click",{step:e?.id||"",action:"activate_payments"}),T(!0),(0,x.AC)().then((()=>(T(!1),t(),s()))).catch((()=>{T(!1)}))},children:(0,i.__)("Activate payments","woocommerce")})]})})})]}):(0,h.jsxs)("div",{className:"woocommerce-payments-test-account-step",children:[(0,h.jsx)(re,{onClose:o}),("error"===r||"blocked"===r)&&(0,h.jsx)(_.Notice,{status:"blocked"===r?"error":"warning",isDismissible:!1,actions:"blocked"!==r?[{label:(0,i.__)("Try Again","woocommerce"),variant:"primary",onClick:()=>{(0,x.W7)("woopayments_onboarding_modal_click",{step:e?.id||"",action:"try_again_on_error",retries:j+1}),O(),f((e=>e+1))}},{label:(0,i.__)("Cancel","woocommerce"),variant:"secondary",className:"woocommerce-payments-test-account-step__error-cancel-button",onClick:()=>{(0,x.W7)("woopayments_onboarding_modal_click",{step:e?.id||"",action:"cancel_on_error",retries:j}),o()}}]:[],className:"woocommerce-payments-test-account-step__error",children:(0,h.jsx)("p",{className:"woocommerce-payments-test-account-step__error-message",children:p||(0,i.__)("An error occurred while creating your test account. Please try again.","woocommerce")})}),("initializing"===r||"polling"===r)&&(0,h.jsx)(Ye,{progress:l,message:(F=y,1===F?(0,i.__)("The test account creation is taking a bit longer than expected, but don't worry — we're on it! Please bear with us for a few seconds more as we set everything up for your store.","woocommerce"):2===F?(0,i.__)("Thank you for your patience! Unfortunately, the test account creation is taking a bit longer than we anticipated. But don't worry — we won't give up! Feel free to close this modal and check back later. We appreciate your understanding!","woocommerce"):void 0)})]});var F},Xe=()=>{const{context:e,closeModal:t}=tt();return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(re,{onClose:t}),(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__step--content",children:(0,h.jsxs)("div",{className:"settings-payments-onboarding-modal__step--content-finish",children:[(0,h.jsx)("h1",{className:"settings-payments-onboarding-modal__step--content-finish-title",children:(0,i.__)("You’re ready to accept payments!","woocommerce")}),(0,h.jsx)("p",{className:"settings-payments-onboarding-modal__step--content-finish-description",children:(0,i.__)("Great news — your WooPayments account has been activated. You can now start accepting payments on your store.","woocommerce")}),(0,h.jsx)(_.Button,{variant:"primary",className:"settings-payments-onboarding-modal__step--content-finish-primary-button",onClick:()=>{var t;(0,x.W7)("woopayments_onboarding_modal_click",{step:"finish",action:"go_to_payments_overview"}),window.location.href=null!==(t=e?.urls?.overview_page)&&void 0!==t?t:""},children:(0,i.__)("Go to Payments Overview","woocommerce")}),(0,h.jsxs)("div",{className:"divider",children:[(0,h.jsx)("span",{className:"divider-line"}),(0,h.jsx)("span",{className:"divider-text",children:(0,i.__)("OR","woocommerce")}),(0,h.jsx)("span",{className:"divider-line"})]}),(0,h.jsx)(_.Button,{variant:"secondary",className:"settings-payments-onboarding-modal__step--content-finish-secondary-button",onClick:()=>{(0,x.W7)("woopayments_onboarding_modal_click",{step:"finish",action:"close_window"}),t()},children:(0,i.__)("Close this window","woocommerce")})]})})]})},Qe=[{id:"payment_methods",order:1,type:"backend",label:"Choose your payment methods",content:(0,h.jsx)(Ve,{})},{id:"wpcom_connection",order:2,type:"backend",label:"Connect with WordPress.com",content:(0,h.jsx)(ce,{}),dependencies:["payment_methods"]},{id:"test_account",order:3,type:"backend",label:"Ready to test payments",dependencies:["wpcom_connection"],content:(0,h.jsx)(Je,{})},{id:"business_verification",order:4,type:"backend",label:"Activate Payments",dependencies:["test_account"],content:(0,h.jsx)($e,{})},{id:"finish",order:5,type:"frontend",label:"Submit for verification",dependencies:["business_verification"],content:(0,h.jsx)(Xe,{})}],et=(0,c.createContext)({steps:[],isLoading:!0,currentStep:void 0,context:{},navigateToStep:()=>{},navigateToNextStep:()=>{},getStepByKey:()=>{},refreshStoreData:()=>{},closeModal:()=>{},justCompletedStepId:null,setJustCompletedStepId:()=>{}}),tt=()=>(0,c.useContext)(et),ot=({children:e,closeModal:t})=>{const o=(0,m.getHistory)(),[n,s]=(0,c.useState)([]),[i,l]=(0,c.useState)(!0),[d,_]=(0,c.useState)([]),[u,p]=(0,c.useState)(null),g=(0,c.useCallback)((e=>{p(e)}),[]),{invalidateResolutionForStoreSelector:y}=(0,r.useDispatch)(a.woopaymentsOnboardingStore),{invalidateResolutionForStoreSelector:w}=(0,r.useDispatch)(a.paymentSettingsStore),{storeData:v,isStoreLoading:x}=(0,r.useSelect)((e=>({storeData:e(a.woopaymentsOnboardingStore).getOnboardingData(),isStoreLoading:e(a.woopaymentsOnboardingStore).isOnboardingDataRequestPending()})),[]),b=(0,c.useCallback)((e=>d.find((t=>t.id===e))),[d]),j=(0,c.useCallback)(((e,t)=>!e.dependencies||0===e.dependencies.length||e.dependencies.every((e=>{const o=t.find((t=>t.id===e));return"completed"===o?.status}))),[]),f=(0,c.useCallback)((e=>{const t=b(e);if(t?.path){const e=(0,m.getNewPath)({path:t.path},t.path,{page:"wc-settings",tab:"checkout"});o.push(e)}}),[b,o]),k=d.find((e=>"completed"!==e.status&&j(e,d))),S=(0,c.useCallback)((()=>{const e=d.findIndex((e=>e.id===k?.id));if(-1!==e){"completed"!==k?.status&&_(d.map((e=>e.id===k?.id?{...e,status:"completed"}:e)));const e=d.find((e=>"completed"!==e.status&&j(e,d)));e&&f(e.id)}}),[k,d,f,j]),N=()=>{s([]),l(!0),g(null),_([]),y("getOnboardingData")};return(0,c.useEffect)((()=>{!x&&v.steps.length>0&&(s(v.steps),l(!1))}),[v,x]),(0,c.useEffect)((()=>{const e=Qe.filter((e=>"backend"!==e.type||-1!==n.findIndex((t=>t.id===e.id)))).map((e=>{if("backend"===e.type){const t=n.find((t=>t.id===e.id));return Object.assign({},e,{status:t?.status||"not_started",dependencies:t?.dependencies||[],path:t?.path,context:t?.context,actions:t?.actions,errors:t?.errors})}return Object.assign({},e)})),t=e.map((t=>"frontend"===t.type?{...t,status:j(t,e)?"completed":"not_started"}:t));_(t)}),[n,j]),(0,c.useEffect)((()=>{N()}),[]),(0,h.jsx)(et.Provider,{value:{steps:d,context:v.context,isLoading:i,currentStep:k,navigateToStep:f,navigateToNextStep:S,getStepByKey:b,refreshStoreData:N,closeModal:()=>{t(),w("getPaymentProviders")},justCompletedStepId:u,setJustCompletedStepId:g},children:e})};function nt(){const{steps:e,isLoading:t,currentStep:o,navigateToStep:s,justCompletedStepId:a}=tt(),r=(0,ee.zy)();return(0,n.useEffect)((()=>{var e;o&&r.pathname!==(null!==(e=o?.path)&&void 0!==e?e:"")&&s(o.id)}),[o,s,r.pathname]),t?(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__loading",children:(0,h.jsx)(ne,{})}):e&&e.length>0?(0,h.jsx)(ee.BV,{children:(0,h.jsx)(ee.qh,{path:"/woopayments/onboarding/*",element:(0,h.jsx)("div",{className:"settings-payments-onboarding-modal__wrapper",children:(0,h.jsx)(ie,{steps:e,active:null!==(c=o?.id)&&void 0!==c?c:"",justCompletedStepId:a,includeSidebar:!0,sidebarTitle:(0,i.__)("Set up WooPayments","woocommerce")})})})}):null;var c}function st({isOpen:e,setIsOpen:t,providerData:o}){const n=(0,ee.zy)(),a=(0,m.getHistory)(),c="/woopayments/onboarding",{createErrorNotice:l}=(0,r.dispatch)("core/notices"),d=(0,te.getQueryArg)(window.location.href,"wpcom_connection_return")||!1,_=o?.onboarding?.state?.wpcom_has_working_connection||!1;s().useEffect((()=>{!n.pathname.startsWith(c)||e||!_&&d||((0,x.W7)("woopayments_onboarding_modal_opened"),t(!0)),!_&&d&&((0,x.W7)("woopayments_onboarding_wpcom_connection_cancelled"),l((0,i.__)("Setup was cancelled!","woocommerce"),{type:"snackbar",explicitDismiss:!1}))}),[n,e,t,d,_,l]),s().useEffect((()=>{if(e&&!n.pathname.startsWith(c)){const e=(0,m.getNewPath)({path:c},c,{page:"wc-settings",tab:"checkout"});a.push(e)}}),[e,n.pathname,a]);const u=()=>{(0,x.W7)("woopayments_onboarding_modal_closed");const e=(0,m.getNewPath)({},"/wp-admin/admin.php",{page:"wc-settings",tab:"checkout"});a.push(e),t(!1)};return e?(0,h.jsx)(oe,{setIsOpen:u,children:(0,h.jsx)(ot,{closeModal:u,children:(0,h.jsx)(nt,{})})}):null}const it=()=>{var e,t;const[o,s]=(0,c.useState)(null),[l,p]=(0,c.useState)(null),{installAndActivatePlugins:g}=(0,r.useDispatch)(a.pluginsStore),{updateProviderOrdering:y,attachPaymentExtensionSuggestion:w}=(0,r.useDispatch)(a.paymentSettingsStore),[b,j]=(0,c.useState)(null),[f,k]=(0,c.useState)(!1),[S,C]=(0,c.useState)(window.wcSettings?.admin?.woocommerce_payments_nox_profile?.business_country_code||null),[P,T]=(0,c.useState)(!1),I=(0,v.Qk)("wcAdminAssetUrl");(0,c.useEffect)((()=>{(0,x.TH)("pageview");const e=new URLSearchParams(window.location.search);"true"===e.get("test_drive_error")&&j((0,i.sprintf)((0,i.__)("%s: An error occurred while setting up your sandbox account — please try again.","woocommerce"),"WooPayments")),"1"===e.get("wcpay-connect-jetpack-error")&&j((0,i.sprintf)((0,i.__)("%s: There was a problem connecting your WordPress.com account — please try again.","woocommerce"),"WooPayments")),"true"===e.get("wcpay-sandbox-success")&&k(!0)}),[]);const O=(0,r.useSelect)((e=>e(a.pluginsStore).getInstalledPlugins()),[]),{invalidateResolutionForStoreSelector:F}=(0,r.useDispatch)(a.paymentSettingsStore),{providers:A,offlinePaymentGateways:E,suggestions:B,suggestionCategories:D,isFetching:M}=(0,r.useSelect)((e=>{const t=e(a.paymentSettingsStore);return{providers:t.getPaymentProviders(S),offlinePaymentGateways:t.getOfflinePaymentGateways(),suggestions:t.getSuggestions(),suggestionCategories:t.getSuggestionCategories(),isFetching:t.isFetching()}}),[S]),L=(0,n.useCallback)(((e,t,o=!1)=>{d()({url:e,method:"POST",data:{context:t,do_not_track:o}})}),[]),W=(0,n.useCallback)((e=>{d()({path:`/wc-analytics/admin/notes/experimental-activate-promo/${e}`,method:"POST"})}),[]);(0,c.useEffect)((()=>{p(null)}),[A]);const H=A.find((e=>"_incentive"in e)),R=H?H._incentive:null;let U=!1,G=!1,z=!1;if(H&&R)if((0,x.sq)(R))if((0,x.$8)(R,"wc_settings_payments__modal")){if(!(0,x.$8)(R,"wc_settings_payments__banner")){const e=new Date;e.setDate(e.getDate()-30),(0,x.uH)(R,"wc_settings_payments__modal",e.getTime())?G=!0:z=!0}}else U=!0;else(0,x.uU)(R)&&((0,x.$8)(R,"wc_settings_payments__banner")?z=!0:G=!0);const $=(0,n.useRef)(!1);(0,c.useEffect)((()=>{if(M||!A.length||!B.length||$.current)return;$.current=!0;const e={woocommerce_payments_displayed:A.some((e=>(0,x.j4)(e.id)))};B.forEach((t=>{e[t.id.replace(/-/g,"_")+"_displayed"]=!0})),A.filter((e=>"suggestion"===e._type)).forEach((t=>{t._suggestion_id?e[t._suggestion_id.replace(/-/g,"_")+"_displayed"]=!0:t.plugin&&t.plugin.slug&&(e[t.plugin.slug.replace(/-/g,"_")+"_displayed"]=!0)})),(0,x.TH)("recommendations_pageview",e)}),[B,A,M]);const K=(0,n.useCallback)(((e,t,n)=>{o||(e?.onboarding?._links?.preload?.href&&d()({url:e?.onboarding?._links?.preload.href,method:"POST",data:{location:S}}),!t&&(0,x.j4)(e.id)&&(t=(0,x.ge)()),s(e.id),(0,x.TH)("recommendations_setup",{extension_selected:e.plugin.slug}),g([e.plugin.slug]).then((async o=>{n&&w(n),u(o),F("getPaymentProviders"),(0,x.TH)("provider_installed",{provider_id:e.id});const i=(await(0,r.resolveSelect)(a.paymentSettingsStore).getPaymentProviders(S)).find((t=>t.id===e.id||t?._suggestion_id===e.id||t.plugin.slug===e.plugin.slug));if((0,x.TH)("provider_enable",{provider_id:e.id}),"native_in_context"===i?.onboarding?.type)T(!0),s(null);else{var c;if((null!==(c=i?.onboarding?.recommended_payment_methods)&&void 0!==c?c:[]).length>0)return(0,m.getHistory)().push((0,m.getNewPath)({},"/payment-methods")),void s(null);s(null),t&&(window.location.href=t)}})).catch((e=>{u(e),s(null)})))}),[o,g,F,S]),Z=(0,h.jsxs)(_.Button,{variant:"link",target:"_blank",href:"https://woocommerce.com/product-category/woocommerce-extensions/payment-gateways/",className:"more-payment-options-link",onClick:()=>{const e=A.map((e=>e.plugin&&e.plugin.slug?e.plugin.slug.replace(/-/g,"_"):e._suggestion_id?e._suggestion_id.replace(/-/g,"_"):e.id));E.forEach((t=>{e.push(t.id)})),B.forEach((t=>{t.plugin&&t.plugin.slug?e.push(t.plugin.slug.replace(/-/g,"_")):e.push(t.id.replace(/-/g,"_"))}));const t=[...new Set(e)];(0,x.TH)("recommendations_other_options",{available_payment_methods:t.join(", ")})},children:[(0,h.jsx)("img",{src:I+"/icons/external-link.svg",alt:""}),(0,i.__)("More payment options","woocommerce")]});return(0,h.jsxs)(h.Fragment,{children:[U&&H&&R&&(0,h.jsx)(Q,{incentive:R,provider:H,onboardingUrl:null!==(e=H.onboarding?._links?.onboard?.href)&&void 0!==e?e:null,onDismiss:L,onAccept:W,setUpPlugin:K}),b&&(0,h.jsxs)("div",{className:"notice notice-error is-dismissible wcpay-settings-notice",children:[(0,h.jsx)("p",{children:b}),(0,h.jsx)("button",{type:"button",className:"notice-dismiss",onClick:()=>{j(null)}})]}),G&&H&&R&&(0,h.jsx)(X,{incentive:R,provider:H,onboardingUrl:null!==(t=H.onboarding?._links?.onboard?.href)&&void 0!==t?t:null,onDismiss:L,onAccept:W,setUpPlugin:K}),(0,h.jsxs)("div",{className:"settings-payments-main__container",children:[(0,h.jsx)(J,{providers:l||A,installedPluginSlugs:O,installingPlugin:o,setUpPlugin:K,acceptIncentive:W,shouldHighlightIncentive:z,updateOrdering:function(e){const t=e.map((e=>e._order)).sort(((e,t)=>e-t)),o={};e.forEach(((e,n)=>{o[e.id]=t[n]})),y(o),p(e)},isFetching:M,businessRegistrationCountry:S,setBusinessRegistrationCountry:C,setIsOnboardingModalOpen:T}),!M&&0===B.length&&(0,h.jsx)("div",{className:"more-payment-options",children:Z}),(M||B.length>0)&&(0,h.jsx)(N,{suggestions:B,suggestionCategories:D,installingPlugin:o,setUpPlugin:K,isFetching:M,morePaymentOptionsLink:Z})]}),((0,x.ZT)(A)||(0,x.Pt)(A))&&(0,h.jsx)(st,{isOpen:P,setIsOpen:T,providerData:(0,x.RY)(A)||{}}),(0,h.jsx)(q,{isOpen:f&&(0,x.Pt)(A),devMode:(0,x.MQ)(A),onClose:()=>k(!1)})]})},at=it}}]);