{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-radio-field", "title": "Product radio control", "category": "woocommerce", "description": "The product radio.", "keywords": ["products", "radio", "input"], "textdomain": "default", "attributes": {"title": {"type": "string"}, "description": {"type": "string"}, "property": {"type": "string"}, "options": {"type": "array", "items": {"type": "object"}, "default": [], "role": "content"}, "disabled": {"type": "boolean", "default": false}}, "supports": {"align": false, "html": false, "multiple": true, "reusable": false, "inserter": false, "lock": false, "__experimentalToolbar": false}, "usesContext": ["postType"]}