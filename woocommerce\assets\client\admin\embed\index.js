/*! For license information please see index.js.LICENSE.txt */
(()=>{var e,t,r={36849:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(51609),o=r(51428);function s(e,t){let r,o,i=[];for(let n=0;n<e.length;n++){const s=e[n];if("string"!==s.type){if(void 0===t[s.value])throw new Error(`Invalid interpolation, missing component node: \`${s.value}\``);if("object"!=typeof t[s.value])throw new Error(`Invalid interpolation, component node must be a ReactElement or null: \`${s.value}\``);if("componentClose"===s.type)throw new Error(`Missing opening component token: \`${s.value}\``);if("componentOpen"===s.type){r=t[s.value],o=n;break}i.push(t[s.value])}else i.push(s.value)}if(r){const a=function(e,t){const r=t[e];let n=0;for(let o=e+1;o<t.length;o++){const e=t[o];if(e.value===r.value){if("componentOpen"===e.type){n++;continue}if("componentClose"===e.type){if(0===n)return o;n--}}}throw new Error("Missing closing component token `"+r.value+"`")}(o,e),c=s(e.slice(o+1,a),t),l=(0,n.cloneElement)(r,{},c);if(i.push(l),a<e.length-1){const r=s(e.slice(a+1),t);i=i.concat(r)}}return i=i.filter(Boolean),0===i.length?null:1===i.length?i[0]:(0,n.createElement)(n.Fragment,null,...i)}function i(e){const{mixedString:t,components:r,throwErrors:n}=e;if(!r)return t;if("object"!=typeof r){if(n)throw new Error(`Interpolation Error: unable to process \`${t}\` because components is not an object`);return t}const i=(0,o.A)(t);try{return s(i,r)}catch(e){if(n)throw new Error(`Interpolation Error: unable to process \`${t}\` because of error \`${e.message}\``);return t}}},51428:(e,t,r)=>{"use strict";function n(e){return e.startsWith("{{/")?{type:"componentClose",value:e.replace(/\W/g,"")}:e.endsWith("/}}")?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.startsWith("{{")?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}function o(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(n)}r.d(t,{A:()=>o})},24148:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(86087);const o=(0,n.forwardRef)((function({icon:e,size:t=24,...r},o){return(0,n.cloneElement)(e,{width:t,height:t,...r,ref:o})}))},7833:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),o=r(39793);const s=(0,o.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,o.jsx)(n.Path,{fillRule:"evenodd",d:"M5 5.5h14a.5.5 0 01.5.5v1.5a.5.5 0 01-.5.5H5a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM4 9.232A2 2 0 013 7.5V6a2 2 0 012-2h14a2 2 0 012 2v1.5a2 2 0 01-1 1.732V18a2 2 0 01-2 2H6a2 2 0 01-2-2V9.232zm1.5.268V18a.5.5 0 00.5.5h12a.5.5 0 00.5-.5V9.5h-13z",clipRule:"evenodd"})})},47804:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),o=r(39793);const s=(0,o.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,o.jsx)(n.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"})})},99669:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(5573),o=r(39793);const s=(0,o.jsxs)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,o.jsx)(n.Path,{d:"M15.5 7.5h-7V9h7V7.5Zm-7 3.5h7v1.5h-7V11Zm7 3.5h-7V16h7v-1.5Z"}),(0,o.jsx)(n.Path,{d:"M17 4H7a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2ZM7 5.5h10a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5H7a.5.5 0 0 1-.5-.5V6a.5.5 0 0 1 .5-.5Z"})]})},63861:(e,t,r)=>{"use strict";r.d(t,{q:()=>m,P:()=>y});var n=r(56427),o=r(47143),s=r(27723),i=r(40314),a=r(83306),c=r(39793);const l=()=>(0,c.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)("g",{id:"block-template-part-sidebar",children:(0,c.jsx)("path",{id:"Shape",fillRule:"evenodd",clipRule:"evenodd",d:"M6 4H18C19.1046 4 20 4.89543 20 6V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V6C4 4.89543 4.89543 4 6 4ZM18 5.5H6C5.72386 5.5 5.5 5.72386 5.5 6V9H18.5V6C18.5 5.72386 18.2761 5.5 18 5.5ZM18.5 10.5H10L10 18.5H18C18.2761 18.5 18.5 18.2761 18.5 18V10.5Z",fill:"#1E1E1E"})})}),u=()=>(0,c.jsx)("svg",{className:"woocommerce-layout__activity-panel-tab-icon",width:"12",height:"14",viewBox:"0 0 12 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)("rect",{x:"0.5",y:"0.5",width:"11",height:"13",strokeWidth:"1"})}),d=()=>(0,c.jsxs)("svg",{className:"woocommerce-layout__activity-panel-tab-icon",width:"18",height:"14",viewBox:"0 0 18 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,c.jsx)("rect",{x:"0.5",y:"0.5",width:"7",height:"13",strokeWidth:"1"}),(0,c.jsx)("rect",{x:"9.5",y:"0.5",width:"7",height:"13",strokeWidth:"1"})]});var p=r(99915);const{Fill:m,Slot:f}=(0,n.createSlotFill)("DisplayOptions");m.Slot=f;const h=[{value:"single_column",label:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(u,{}),(0,s.__)("Single column","woocommerce")]})},{value:"two_columns",label:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(d,{}),(0,s.__)("Two columns","woocommerce")]})}],y=()=>{const{defaultHomescreenLayout:e}=(0,o.useSelect)((e=>{const{getOption:t}=e(i.optionsStore);return{defaultHomescreenLayout:t("woocommerce_default_homepage_layout")||"single_column"}})),{updateUserPreferences:t,homepage_layout:r}=(0,i.useUserPreferences)(),u=!(0,p.EM)("setup")||window.wcAdminFeatures.analytics;return(0,c.jsx)(f,{children:o=>0!==o.length||u?(0,c.jsx)(n.DropdownMenu,{icon:(0,c.jsx)(l,{}),label:(0,s.__)("Display options","woocommerce"),toggleProps:{className:"woocommerce-layout__activity-panel-tab display-options",onClick:()=>(0,a.recordEvent)("homescreen_display_click")},popoverProps:{className:"woocommerce-layout__activity-panel-popover"},children:({onClose:i})=>(0,c.jsxs)(c.Fragment,{children:[o,u?(0,c.jsx)(n.MenuGroup,{className:"woocommerce-layout__homescreen-display-options",label:(0,s.__)("Layout","woocommerce"),children:(0,c.jsx)(n.MenuItemsChoice,{choices:h,onSelect:e=>{t({homepage_layout:e}),i(),(0,a.recordEvent)("homescreen_display_option",{display_option:e})},value:r||e})}):null]})}):null})}},57882:(e,t,r)=>{"use strict";r.d(t,{JT:()=>v,sY:()=>b});var n=r(27723),o=r(14908),s=r(83306),i=r(98846),a=r(47143),c=r(99669),l=r(5573),u=r(39793);const d=(0,u.jsx)(l.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,u.jsx)(l.Path,{d:"M18 4H6c-1.1 0-2 .9-2 2v12.9c0 .6.5 1.1 1.1 1.1.3 0 .5-.1.8-.3L8.5 17H18c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm.5 11c0 .3-.2.5-.5.5H7.9l-2.4 2.4V6c0-.3.2-.5.5-.5h12c.3 0 .5.2.5.5v9z"})});var p=r(7833),m=r(56427),f=r(96476),h=r(29332),y=r(42288);const g=()=>(0,u.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,u.jsx)("path",{d:"M0 0h24v24H0z",fill:"none"}),(0,u.jsx)("path",{d:"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.64 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"})]});var w=r(99915);const v="AbbreviatedNotification",b=({thingsToDoNextCount:e})=>{const{ordersToProcessCount:t,reviewsToModerateCount:r,stockNoticesCount:l,isSetupTaskListHidden:b,isExtendedTaskListHidden:_}=(0,a.useSelect)((e=>{const t=(0,h.VJ)(e);return{ordersToProcessCount:(0,h.xC)(e,t),reviewsToModerateCount:(0,y.my)(e),stockNoticesCount:(0,h.G9)(e),isSetupTaskListHidden:!(0,w.Oh)("setup"),isExtendedTaskListHidden:!(0,w.Oh)("extended")}})),x=e=>{(0,s.recordEvent)("activity_panel_click",{task:e})},{Slot:S}=(0,m.createSlotFill)(v),C=(0,f.isWCAdmin)();return(0,u.jsxs)("div",{className:"woocommerce-abbreviated-notifications",children:[e>0&&!_&&(0,u.jsxs)(i.AbbreviatedCard,{className:"woocommerce-abbreviated-notification",icon:(0,u.jsx)(g,{}),href:"admin.php?page=wc-admin#extended_task_list",onClick:()=>x("thingsToDoNext"),type:C?"wc-admin":"wp-admin",children:[(0,u.jsx)(o.Text,{as:"h3",children:(0,n.__)("Things to do next","woocommerce")}),(0,u.jsx)(o.Text,{as:"p",children:(0,n.sprintf)((0,n._n)("You have %d new thing to do","You have %d new things to do",e,"woocommerce"),e)})]}),t>0&&b&&(0,u.jsxs)(i.AbbreviatedCard,{className:"woocommerce-abbreviated-notification",icon:c.A,href:"admin.php?page=wc-admin&opened_panel=orders-panel",onClick:()=>x("ordersToProcess"),type:C?"wc-admin":"wp-admin",children:[(0,u.jsx)(o.Text,{as:"h3",children:(0,n.__)("Orders to fulfill","woocommerce")}),(0,u.jsx)(o.Text,{children:(0,n.sprintf)((0,n._n)("You have %d order to fulfill","You have %d orders to fulfill",t,"woocommerce"),t)})]}),r>0&&b&&(0,u.jsxs)(i.AbbreviatedCard,{className:"woocommerce-abbreviated-notification",icon:d,href:"admin.php?page=wc-admin&opened_panel=reviews-panel",onClick:()=>x("reviewsToModerate"),type:C?"wc-admin":"wp-admin",children:[(0,u.jsx)(o.Text,{as:"h3",children:(0,n.__)("Reviews to moderate","woocommerce")}),(0,u.jsx)(o.Text,{children:(0,n.sprintf)((0,n._n)("You have %d review to moderate","You have %d reviews to moderate",r,"woocommerce"),r)})]}),l>0&&b&&(0,u.jsxs)(i.AbbreviatedCard,{className:"woocommerce-abbreviated-notification",icon:p.A,href:"admin.php?page=wc-admin&opened_panel=stock-panel",onClick:()=>x("stockNotices"),type:C?"wc-admin":"wp-admin",children:[(0,u.jsx)(o.Text,{as:"h3",children:(0,n.__)("Inventory to review","woocommerce")}),(0,u.jsx)(o.Text,{children:(0,n.__)("You have inventory to review and update","woocommerce")})]}),!_&&(0,u.jsx)(S,{})]})}},15838:(e,t,r)=>{"use strict";r.d(t,{gV:()=>d});var n=r(27723),o=r(52619),s=r(36849),i=r(98846),a=r(40314),c=r(77374),l=r(39793);var u=r(56109);const d=["processing","on-hold"],p=["completed","processing","refunded","cancelled","failed","pending","on-hold"],m=Object.keys(u.wm).filter((e=>"refunded"!==e)).map((e=>({value:e,label:u.wm[e],description:(0,n.sprintf)((0,n.__)("Exclude the %s status from reports","woocommerce"),u.wm[e])}))),f=(0,u.Qk)("unregisteredOrderStatuses",{}),h=[{key:"defaultStatuses",options:m.filter((e=>p.includes(e.value)))},{key:"customStatuses",label:(0,n.__)("Custom Statuses","woocommerce"),options:m.filter((e=>!p.includes(e.value)))},{key:"unregisteredStatuses",label:(0,n.__)("Unregistered Statuses","woocommerce"),options:Object.keys(f).map((e=>({value:e,label:e,description:(0,n.sprintf)((0,n.__)("Exclude the %s status from reports","woocommerce"),e)})))}];(0,o.applyFilters)("woocommerce_admin_analytics_settings",{woocommerce_excluded_report_order_statuses:{label:(0,n.__)("Excluded statuses:","woocommerce"),inputType:"checkboxGroup",options:h,helpText:(0,s.A)({mixedString:(0,n.__)("Orders with these statuses are excluded from the totals in your reports. The {{strong}}Refunded{{/strong}} status can not be excluded.","woocommerce"),components:{strong:(0,l.jsx)("strong",{})}}),defaultValue:["pending","cancelled","failed"]},woocommerce_actionable_order_statuses:{label:(0,n.__)("Actionable statuses:","woocommerce"),inputType:"checkboxGroup",options:h,helpText:(0,n.__)("Orders with these statuses require action on behalf of the store admin. These orders will show up in the Home Screen - Orders task.","woocommerce"),defaultValue:d},woocommerce_default_date_range:{name:"woocommerce_default_date_range",label:(0,n.__)("Default date range:","woocommerce"),inputType:"component",component:({value:e,onChange:t})=>{const{wcAdminSettings:r}=(0,a.useSettings)("wc_admin",["wcAdminSettings"]),{woocommerce_default_date_range:n}=r,o=Object.fromEntries(new URLSearchParams(e.replace(/&amp;/g,"&"))),{period:s,compare:u,before:d,after:p}=(0,c.getDateParamsFromQuery)(o,n),{primary:m,secondary:f}=(0,c.getCurrentDates)(o,n),h={period:s,compare:u,before:d,after:p,primaryDate:m,secondaryDate:f};return(0,l.jsx)(i.DateRangeFilterPicker,{query:o,onRangeSelect:e=>{t({target:{name:"woocommerce_default_date_range",value:new URLSearchParams(e).toString()}})},dateQuery:h,isoDateFormat:c.isoDateFormat})},helpText:(0,n.__)("Select a default date range. When no range is selected, reports will be viewed by the default date range.","woocommerce"),defaultValue:"period=month&compare=previous_year"},woocommerce_date_type:{name:"woocommerce_date_type",label:(0,n.__)("Date type:","woocommerce"),inputType:"select",options:[{label:(0,n.__)("Select a date type","woocommerce"),value:"",disabled:!0},{label:(0,n.__)("Date created","woocommerce"),value:"date_created",key:"date_created"},{label:(0,n.__)("Date paid","woocommerce"),value:"date_paid",key:"date_paid"},{label:(0,n.__)("Date completed","woocommerce"),value:"date_completed",key:"date_completed"}],helpText:(0,n.__)("Database date field considered for Revenue and Orders reports","woocommerce")}})},29332:(e,t,r)=>{"use strict";r.d(t,{G9:()=>c,VJ:()=>i,xC:()=>s});var n=r(40314),o=r(15838);function s(e,t){if(!t.length)return 0;const r={page:1,per_page:1,status:t,_fields:["id"]},{getItemsTotalCount:o,getItemsError:s,isResolving:i}=e(n.itemsStore),a=o("orders",r,null),c=Boolean(s("orders",r)),l=i("getItemsTotalCount",["orders",r,null]);return c||l?null:a}function i(e){const{getSetting:t}=e(n.settingsStore),{woocommerce_actionable_order_statuses:r=o.gV}=t("wc_admin","wcAdminSettings",{});return r}const a={status:"publish"};function c(e){const{getItemsTotalCount:t,getItemsError:r,isResolving:o}=e(n.itemsStore),s=null,i=t("products/count-low-in-stock",a,s),c=Boolean(r("products/count-low-in-stock",a)),l=o("getItemsTotalCount",["products/count-low-in-stock",a,s]);return c||l&&i===s?null:i}},42288:(e,t,r)=>{"use strict";r.d(t,{my:()=>s});var n=r(40314);const o={page:1,per_page:1,status:"hold",_embed:1,_fields:["id"]};function s(e){const{getReviewsTotalCount:t,getReviewsError:r,isResolving:s}=e(n.reviewsStore),i=t(o),a=Boolean(r(o)),c=s("getReviewsTotalCount",[o]);return a||c&&void 0===i?null:i}},99915:(e,t,r)=>{"use strict";r.d(t,{EM:()=>c,Oh:()=>i,fK:()=>p});var n=r(47143),o=r(40314),s=r(56109);const i=e=>(0,s.Qk)("visibleTaskListIds",[]).includes(e),a=e=>(0,s.Qk)("completedTaskListIds",[]).includes(e),c=e=>i(e)&&!a(e),l=()=>({requestingTaskListOptions:!1,setupTaskListHidden:!i("setup"),setupTaskListComplete:a("setup"),setupTaskListActive:c("setup"),setupTasksCount:void 0,setupTasksCompleteCount:void 0,thingsToDoNextCount:void 0}),u=e=>{const{getTaskList:t,hasFinishedResolution:r}=e,n=t("setup"),s=(0,o.getVisibleTasks)(n?.tasks||[]),i=!n||n.isHidden,a=n?.isComplete;return{setupTaskListHidden:i,setupTaskListComplete:a,setupTaskListActive:!i&&!a,setupTasksCount:s.length,setupTasksCompleteCount:s.filter((e=>e.isComplete)).length,requestingTaskListOptions:!r("getTaskLists")}},d=e=>{const{getTaskList:t,hasFinishedResolution:r}=e;return{thingsToDoNextCount:(n=t("extended"),n&&n.tasks.length&&!n.isHidden?n.tasks.filter((e=>e.canView&&!e.isComplete&&!e.isDismissed)).length:0),requestingTaskListOptions:!r("getTaskLists")};var n},p=({setupTasklist:e,extendedTaskList:t}={setupTasklist:!0,extendedTaskList:!0})=>{const r=e&&c("setup"),s=t&&c("extended");return(0,n.useSelect)((e=>{if(!r&&!s)return l();const t=e(o.ONBOARDING_STORE_NAME);return r?s?{...l(),...u(t),...d(t)}:{...l(),...u(t)}:{...l(),...d(t)}}),[r,s])}},46591:(e,t,r)=>{"use strict";r.d(t,{e8:()=>a,kT:()=>i,yz:()=>u});var n=r(66087),o=r(30155),s=r.n(o);function i(e,t){return(0,n.filter)(e,(e=>{const{is_deleted:r,date_created_gmt:n,status:o}=e;if(!r)return(!t||!n||new Date(n+"Z").getTime()>t)&&"unactioned"===o})).length}function a(e){return(0,n.filter)(e,(e=>{const{is_deleted:t}=e;return!t})).length>0}const c=(e,t,r=" ")=>{let n=e.slice(0,t);if(e.indexOf(r,t)!==t){const e=n.lastIndexOf(r);e>-1&&(n=n.slice(0,e))}return n.join("")},l=(e,t)=>{const r=document.createElement("div"),n=Array.from(e.childNodes),o=new(s());let i=0;for(let e=0;e<n.length;e++){let s=n[e].cloneNode(!0);const a=o.splitGraphemes(s.textContent);if(i+a.length<=t){r.appendChild(s),i+=a.length;continue}const u=t-i;s.hasChildNodes()?s=l(s,u):s.textContent=c(a,u),r.appendChild(s);break}return r},u=(e,t)=>{const r=document.createElement("div"),n=new(s());return r.innerHTML=e,n.splitGraphemes(r.textContent).length>t?l(r,t).innerHTML+"...":e}},46772:(e,t,r)=>{"use strict";r.d(t,{R:()=>o});var n=r(47143);function o(e){const{createNotice:t}=(0,n.dispatch)("core/notices");e.error_data&&e.errors&&Object.keys(e.errors).length?Object.keys(e.errors).forEach((r=>{t("error",e.errors[r].join(" "))})):e.message&&t(e.code?"error":"success",e.message)}},11846:(e,t,r)=>{"use strict";r.d(t,{O:()=>g});var n=r(27723),o=r(47143),s=r(40314),i=r(56427),a=r(98846),c=r(15703),l=r(47804),u=r(36849),d=r(86087),p=r(96476),m=r(83306),f=r(39793);const h="woocommerce_task_list_reminder_bar_hidden",y=({remainingCount:e,tracksProps:t})=>{const r=1===e?(0,n.__)("🎉 Almost there. Only {{strongText}}%1$d step left{{/strongText}} get your store up and running. {{setupLink}}Finish setup{{/setupLink}}","woocommerce"):(0,n.__)("🚀 You’re doing great! {{strongText}}%1$d steps left{{/strongText}} to get your store up and running. {{setupLink}}Continue setup{{/setupLink}}","woocommerce");return(0,f.jsx)("p",{children:(0,u.A)({mixedString:(0,n.sprintf)(r,e),components:{strongText:(0,f.jsx)("strong",{}),setupLink:(0,f.jsx)(a.Link,{href:(0,c.getAdminLink)("admin.php?page=wc-admin"),onClick:()=>(0,m.recordEvent)("tasklist_reminder_bar_continue",t),type:"wp-admin",children:(0,f.jsx)(f.Fragment,{})})}})})},g=({taskListId:e,updateBodyMargin:t})=>{const{updateOptions:r}=(0,o.useDispatch)(s.optionsStore),{remainingCount:n,loading:a,taskListHidden:c,taskListComplete:u,reminderBarHidden:g,completedTasksCount:w}=(0,o.useSelect)((t=>{const{getTaskList:r,hasFinishedResolution:n}=t(s.onboardingStore),{getOption:o,hasFinishedResolution:i}=t(s.optionsStore),a=o(h),c=r(e),l=n("getTaskList",[e]),u=i("getOption",[h]),d=(0,s.getVisibleTasks)(c?.tasks||[]),p=d.filter((e=>e.isComplete))||[],m=l&&u;return{reminderBarHidden:"yes"===a,taskListHidden:!!m&&c?.isHidden,taskListComplete:!!m&&c?.isComplete,loading:!m,completedTasksCount:p.length,remainingCount:m?d?.length-p.length:null}}),[e]),v=(0,p.getQuery)(),b=v.page&&"wc-admin"===v.page&&!v.path,_=Boolean(v.wc_onboarding_active_task),x=a||c||u||g||0===w||b||_;(0,d.useEffect)((()=>{t()}),[x,t]);const S={completed:w,is_homescreen:!!b,is_active_task_page:_};return(0,d.useEffect)((()=>{a||x||(0,m.recordEvent)("tasklist_reminder_bar_view",S)}),[x,a]),x?null:(0,f.jsxs)("div",{className:"woocommerce-layout__header-tasks-reminder-bar",children:[(0,f.jsx)(y,{remainingCount:n,tracksProps:S}),(0,f.jsx)(i.Button,{isSmall:!0,onClick:()=>{r({[h]:"yes"}),(0,m.recordEvent)("tasklist_reminder_bar_close",S)},icon:l.A})]})}},56109:(e,t,r)=>{"use strict";r.d(t,{GZ:()=>d,Qk:()=>l,kY:()=>u,wm:()=>p});var n=r(27723),o=r(15703);r(24060);const s=["wcAdminSettings","preloadSettings"],i=(0,o.getSetting)("admin",{}),a=Object.keys(i).reduce(((e,t)=>(s.includes(t)||(e[t]=i[t]),e)),{}),c={onboarding:{profile:"Deprecated: wcSettings.admin.onboarding.profile is deprecated. It is planned to be released in WooCommerce 10.0.0. Please use `getProfileItems` from the onboarding store. See https://github.com/woocommerce/woocommerce/tree/trunk/packages/js/data/src/onboarding for more information.",euCountries:"Deprecated: wcSettings.admin.onboarding.euCountries is deprecated. Please use `/wc/v3/data/continents/eu` from the REST API. See https://woocommerce.github.io/woocommerce-rest-api-docs/#list-all-continents for more information.",localInfo:'Deprecated: wcSettings.admin.onboarding.localInfo is deprecated. Please use `include WC()->plugin_path() . "/i18n/locale-info.php"` instead.',currencySymbols:'"Deprecated: wcSettings.admin.onboarding.currencySymbols is deprecated. Please use get_woocommerce_currency_symbols() function instead.'}};function l(e,t=!1,r=e=>e,o=c){if(s.includes(e))throw new Error((0,n.__)("Mutable settings should be accessed via data store.","woocommerce"));return r(a.hasOwnProperty(e)?a[e]:t,t)}const u=(0,o.getSetting)("adminUrl"),d=((0,o.getSetting)("countries"),(0,o.getSetting)("currency"),(0,o.getSetting)("locale"),(0,o.getSetting)("siteTitle"),(0,o.getSetting)("wcAssetUrl")),p=l("orderStatuses")},24060:(e,t,r)=>{"use strict";r.d(t,{CZ:()=>n.C,D8:()=>a,al:()=>o,s9:()=>s,vK:()=>n.v}),r(86087);var n=r(30642);function o(e){return e?e.substr(1).split("&").reduce(((e,t)=>{const r=t.split("="),n=r[0];let o=decodeURIComponent(r[1]);return o=isNaN(Number(o))?o:Number(o),e[n]=o,e}),{}):{}}function s(){let e="";const{page:t,path:r,post_type:n}=o(window.location.search);if(t){const n="wc-admin"===t?"home_screen":t;e=r?r.replace(/\//g,"_").substring(1):n}else n&&(e=n);return e}const i=[{name:"0-2s",max:2},{name:"2-5s",max:5},{name:"5-10s",max:10},{name:"10-15s",max:15},{name:"15-20s",max:20},{name:"20-30s",max:30},{name:"30-60s",max:60},{name:">60s"}],a=e=>{for(const t of i){if(!t.max)return t.name;if(e<1e3*t.max)return t.name}}},30642:(e,t,r)=>{"use strict";function n(e){return(e||"").split(":",1)[0]}function o(e){const t=n(e);return/^woocommerce(-|_)payments$/.test(t)?"wcpay":`${t.replace(/-/g,"_")}`.split(":",1)[0]}r.d(t,{C:()=>o,v:()=>n})},40368:(e,t,r)=>{"use strict";var n=r(40885),o=r(11548),s=o(n("String.prototype.indexOf"));e.exports=function(e,t){var r=n(e,!!t);return"function"==typeof r&&s(e,".prototype.")>-1?o(r):r}},11548:(e,t,r)=>{"use strict";var n=r(72418),o=r(40885),s=r(73745),i=o("%TypeError%"),a=o("%Function.prototype.apply%"),c=o("%Function.prototype.call%"),l=o("%Reflect.apply%",!0)||n.call(c,a),u=o("%Object.defineProperty%",!0),d=o("%Math.max%");if(u)try{u({},"a",{value:1})}catch(e){u=null}e.exports=function(e){if("function"!=typeof e)throw new i("a function is required");var t=l(n,c,arguments);return s(t,1+d(0,e.length-(arguments.length-1)),!0)};var p=function(){return l(n,a,arguments)};u?u(e.exports,"apply",{value:p}):e.exports.apply=p},91244:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,o=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(n++,"%c"===e&&(o=n))})),t.splice(o,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(44099)(t);const{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},44099:(e,t,r)=>{e.exports=function(e){function t(e){let r,o,s,i=null;function a(...e){if(!a.enabled)return;const n=a,o=Number(new Date),s=o-(r||o);n.diff=s,n.prev=r,n.curr=o,r=o,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((r,o)=>{if("%%"===r)return"%";i++;const s=t.formatters[o];if("function"==typeof s){const t=e[i];r=s.call(n,t),e.splice(i,1),i--}return r})),t.formatArgs.call(n,e),(n.log||t.log).apply(n,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=n,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(o!==t.namespaces&&(o=t.namespaces,s=t.enabled(e)),s),set:e=>{i=e}}),"function"==typeof t.init&&t.init(a),a}function n(e,r){const n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function o(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){const e=[...t.names.map(o),...t.skips.map(o).map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];const n=("string"==typeof e?e:"").split(/[\s,]+/),o=n.length;for(r=0;r<o;r++)n[r]&&("-"===(e=n[r].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.slice(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){if("*"===e[e.length-1])return!0;let r,n;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r(59295),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((r=>{t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},91555:(e,t,r)=>{"use strict";var n=r(87612)(),o=r(40885),s=n&&o("%Object.defineProperty%",!0);if(s)try{s({},"a",{value:1})}catch(e){s=!1}var i=o("%SyntaxError%"),a=o("%TypeError%"),c=r(8632);e.exports=function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new a("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new a("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new a("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new a("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new a("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new a("`loose`, if provided, must be a boolean");var n=arguments.length>3?arguments[3]:null,o=arguments.length>4?arguments[4]:null,l=arguments.length>5?arguments[5]:null,u=arguments.length>6&&arguments[6],d=!!c&&c(e,t);if(s)s(e,t,{configurable:null===l&&d?d.configurable:!l,enumerable:null===n&&d?d.enumerable:!n,value:r,writable:null===o&&d?d.writable:!o});else{if(!u&&(n||o||l))throw new i("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");e[t]=r}}},65786:e=>{"use strict";var t=Object.prototype.toString,r=Math.max,n=function(e,t){for(var r=[],n=0;n<e.length;n+=1)r[n]=e[n];for(var o=0;o<t.length;o+=1)r[o+e.length]=t[o];return r};e.exports=function(e){var o=this;if("function"!=typeof o||"[object Function]"!==t.apply(o))throw new TypeError("Function.prototype.bind called on incompatible "+o);for(var s,i=function(e){for(var t=[],r=1,n=0;r<e.length;r+=1,n+=1)t[n]=e[r];return t}(arguments),a=r(0,o.length-i.length),c=[],l=0;l<a;l++)c[l]="$"+l;if(s=Function("binder","return function ("+function(e){for(var t="",r=0;r<e.length;r+=1)t+=e[r],r+1<e.length&&(t+=",");return t}(c)+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof s){var t=o.apply(this,n(i,arguments));return Object(t)===t?t:this}return o.apply(e,n(i,arguments))})),o.prototype){var u=function(){};u.prototype=o.prototype,s.prototype=new u,u.prototype=null}return s}},72418:(e,t,r)=>{"use strict";var n=r(65786);e.exports=Function.prototype.bind||n},40885:(e,t,r)=>{"use strict";var n,o=SyntaxError,s=Function,i=TypeError,a=function(e){try{return s('"use strict"; return ('+e+").constructor;")()}catch(e){}},c=Object.getOwnPropertyDescriptor;if(c)try{c({},"")}catch(e){c=null}var l=function(){throw new i},u=c?function(){try{return l}catch(e){try{return c(arguments,"callee").get}catch(e){return l}}}():l,d=r(13518)(),p=r(64310)(),m=Object.getPrototypeOf||(p?function(e){return e.__proto__}:null),f={},h="undefined"!=typeof Uint8Array&&m?m(Uint8Array):n,y={"%AggregateError%":"undefined"==typeof AggregateError?n:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?n:ArrayBuffer,"%ArrayIteratorPrototype%":d&&m?m([][Symbol.iterator]()):n,"%AsyncFromSyncIteratorPrototype%":n,"%AsyncFunction%":f,"%AsyncGenerator%":f,"%AsyncGeneratorFunction%":f,"%AsyncIteratorPrototype%":f,"%Atomics%":"undefined"==typeof Atomics?n:Atomics,"%BigInt%":"undefined"==typeof BigInt?n:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?n:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?n:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?n:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?n:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?n:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?n:FinalizationRegistry,"%Function%":s,"%GeneratorFunction%":f,"%Int8Array%":"undefined"==typeof Int8Array?n:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?n:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?n:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":d&&m?m(m([][Symbol.iterator]())):n,"%JSON%":"object"==typeof JSON?JSON:n,"%Map%":"undefined"==typeof Map?n:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&d&&m?m((new Map)[Symbol.iterator]()):n,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?n:Promise,"%Proxy%":"undefined"==typeof Proxy?n:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?n:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?n:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&d&&m?m((new Set)[Symbol.iterator]()):n,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?n:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":d&&m?m(""[Symbol.iterator]()):n,"%Symbol%":d?Symbol:n,"%SyntaxError%":o,"%ThrowTypeError%":u,"%TypedArray%":h,"%TypeError%":i,"%Uint8Array%":"undefined"==typeof Uint8Array?n:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?n:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?n:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?n:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?n:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?n:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?n:WeakSet};if(m)try{null.error}catch(e){var g=m(m(e));y["%Error.prototype%"]=g}var w=function e(t){var r;if("%AsyncFunction%"===t)r=a("async function () {}");else if("%GeneratorFunction%"===t)r=a("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=a("async function* () {}");else if("%AsyncGenerator%"===t){var n=e("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&m&&(r=m(o.prototype))}return y[t]=r,r},v={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},b=r(72418),_=r(63206),x=b.call(Function.call,Array.prototype.concat),S=b.call(Function.apply,Array.prototype.splice),C=b.call(Function.call,String.prototype.replace),k=b.call(Function.call,String.prototype.slice),j=b.call(Function.call,RegExp.prototype.exec),E=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,A=/\\(\\)?/g,P=function(e,t){var r,n=e;if(_(v,n)&&(n="%"+(r=v[n])[0]+"%"),_(y,n)){var s=y[n];if(s===f&&(s=w(n)),void 0===s&&!t)throw new i("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:n,value:s}}throw new o("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new i("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new i('"allowMissing" argument must be a boolean');if(null===j(/^%?[^%]*%?$/,e))throw new o("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=k(e,0,1),r=k(e,-1);if("%"===t&&"%"!==r)throw new o("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new o("invalid intrinsic syntax, expected opening `%`");var n=[];return C(e,E,(function(e,t,r,o){n[n.length]=r?C(o,A,"$1"):t||e})),n}(e),n=r.length>0?r[0]:"",s=P("%"+n+"%",t),a=s.name,l=s.value,u=!1,d=s.alias;d&&(n=d[0],S(r,x([0,1],d)));for(var p=1,m=!0;p<r.length;p+=1){var f=r[p],h=k(f,0,1),g=k(f,-1);if(('"'===h||"'"===h||"`"===h||'"'===g||"'"===g||"`"===g)&&h!==g)throw new o("property names with quotes must have matching quotes");if("constructor"!==f&&m||(u=!0),_(y,a="%"+(n+="."+f)+"%"))l=y[a];else if(null!=l){if(!(f in l)){if(!t)throw new i("base intrinsic for "+e+" exists, but the property is not available.");return}if(c&&p+1>=r.length){var w=c(l,f);l=(m=!!w)&&"get"in w&&!("originalValue"in w.get)?w.get:l[f]}else m=_(l,f),l=l[f];m&&!u&&(y[a]=l)}}return l}},8632:(e,t,r)=>{"use strict";var n=r(40885)("%Object.getOwnPropertyDescriptor%",!0);if(n)try{n([],"length")}catch(e){n=null}e.exports=n},30155:e=>{e.exports&&(e.exports=function(){var e=3,t=4,r=12,n=13,o=16,s=17;function i(e,t){void 0===t&&(t=0);var r=e.charCodeAt(t);if(55296<=r&&r<=56319&&t<e.length-1){var n=r;return 56320<=(o=e.charCodeAt(t+1))&&o<=57343?1024*(n-55296)+(o-56320)+65536:n}if(56320<=r&&r<=57343&&t>=1){var o=r;return 55296<=(n=e.charCodeAt(t-1))&&n<=56319?1024*(n-55296)+(o-56320)+65536:o}return r}function a(i,a,c){var l=[i].concat(a).concat([c]),u=l[l.length-2],d=c,p=l.lastIndexOf(14);if(p>1&&l.slice(1,p).every((function(t){return t==e}))&&-1==[e,n,s].indexOf(i))return 2;var m=l.lastIndexOf(t);if(m>0&&l.slice(1,m).every((function(e){return e==t}))&&-1==[r,t].indexOf(u))return l.filter((function(e){return e==t})).length%2==1?3:4;if(0==u&&1==d)return 0;if(2==u||0==u||1==u)return 14==d&&a.every((function(t){return t==e}))?2:1;if(2==d||0==d||1==d)return 1;if(6==u&&(6==d||7==d||9==d||10==d))return 0;if(!(9!=u&&7!=u||7!=d&&8!=d))return 0;if((10==u||8==u)&&8==d)return 0;if(d==e||15==d)return 0;if(5==d)return 0;if(u==r)return 0;var f=-1!=l.indexOf(e)?l.lastIndexOf(e)-1:l.length-2;return-1!=[n,s].indexOf(l[f])&&l.slice(f+1,-1).every((function(t){return t==e}))&&14==d||15==u&&-1!=[o,s].indexOf(d)?0:-1!=a.indexOf(t)?2:u==t&&d==t?0:1}function c(i){return 1536<=i&&i<=1541||1757==i||1807==i||2274==i||3406==i||69821==i||70082<=i&&i<=70083||72250==i||72326<=i&&i<=72329||73030==i?r:13==i?0:10==i?1:0<=i&&i<=9||11<=i&&i<=12||14<=i&&i<=31||127<=i&&i<=159||173==i||1564==i||6158==i||8203==i||8206<=i&&i<=8207||8232==i||8233==i||8234<=i&&i<=8238||8288<=i&&i<=8292||8293==i||8294<=i&&i<=8303||55296<=i&&i<=57343||65279==i||65520<=i&&i<=65528||65529<=i&&i<=65531||113824<=i&&i<=113827||119155<=i&&i<=119162||917504==i||917505==i||917506<=i&&i<=917535||917632<=i&&i<=917759||918e3<=i&&i<=921599?2:768<=i&&i<=879||1155<=i&&i<=1159||1160<=i&&i<=1161||1425<=i&&i<=1469||1471==i||1473<=i&&i<=1474||1476<=i&&i<=1477||1479==i||1552<=i&&i<=1562||1611<=i&&i<=1631||1648==i||1750<=i&&i<=1756||1759<=i&&i<=1764||1767<=i&&i<=1768||1770<=i&&i<=1773||1809==i||1840<=i&&i<=1866||1958<=i&&i<=1968||2027<=i&&i<=2035||2070<=i&&i<=2073||2075<=i&&i<=2083||2085<=i&&i<=2087||2089<=i&&i<=2093||2137<=i&&i<=2139||2260<=i&&i<=2273||2275<=i&&i<=2306||2362==i||2364==i||2369<=i&&i<=2376||2381==i||2385<=i&&i<=2391||2402<=i&&i<=2403||2433==i||2492==i||2494==i||2497<=i&&i<=2500||2509==i||2519==i||2530<=i&&i<=2531||2561<=i&&i<=2562||2620==i||2625<=i&&i<=2626||2631<=i&&i<=2632||2635<=i&&i<=2637||2641==i||2672<=i&&i<=2673||2677==i||2689<=i&&i<=2690||2748==i||2753<=i&&i<=2757||2759<=i&&i<=2760||2765==i||2786<=i&&i<=2787||2810<=i&&i<=2815||2817==i||2876==i||2878==i||2879==i||2881<=i&&i<=2884||2893==i||2902==i||2903==i||2914<=i&&i<=2915||2946==i||3006==i||3008==i||3021==i||3031==i||3072==i||3134<=i&&i<=3136||3142<=i&&i<=3144||3146<=i&&i<=3149||3157<=i&&i<=3158||3170<=i&&i<=3171||3201==i||3260==i||3263==i||3266==i||3270==i||3276<=i&&i<=3277||3285<=i&&i<=3286||3298<=i&&i<=3299||3328<=i&&i<=3329||3387<=i&&i<=3388||3390==i||3393<=i&&i<=3396||3405==i||3415==i||3426<=i&&i<=3427||3530==i||3535==i||3538<=i&&i<=3540||3542==i||3551==i||3633==i||3636<=i&&i<=3642||3655<=i&&i<=3662||3761==i||3764<=i&&i<=3769||3771<=i&&i<=3772||3784<=i&&i<=3789||3864<=i&&i<=3865||3893==i||3895==i||3897==i||3953<=i&&i<=3966||3968<=i&&i<=3972||3974<=i&&i<=3975||3981<=i&&i<=3991||3993<=i&&i<=4028||4038==i||4141<=i&&i<=4144||4146<=i&&i<=4151||4153<=i&&i<=4154||4157<=i&&i<=4158||4184<=i&&i<=4185||4190<=i&&i<=4192||4209<=i&&i<=4212||4226==i||4229<=i&&i<=4230||4237==i||4253==i||4957<=i&&i<=4959||5906<=i&&i<=5908||5938<=i&&i<=5940||5970<=i&&i<=5971||6002<=i&&i<=6003||6068<=i&&i<=6069||6071<=i&&i<=6077||6086==i||6089<=i&&i<=6099||6109==i||6155<=i&&i<=6157||6277<=i&&i<=6278||6313==i||6432<=i&&i<=6434||6439<=i&&i<=6440||6450==i||6457<=i&&i<=6459||6679<=i&&i<=6680||6683==i||6742==i||6744<=i&&i<=6750||6752==i||6754==i||6757<=i&&i<=6764||6771<=i&&i<=6780||6783==i||6832<=i&&i<=6845||6846==i||6912<=i&&i<=6915||6964==i||6966<=i&&i<=6970||6972==i||6978==i||7019<=i&&i<=7027||7040<=i&&i<=7041||7074<=i&&i<=7077||7080<=i&&i<=7081||7083<=i&&i<=7085||7142==i||7144<=i&&i<=7145||7149==i||7151<=i&&i<=7153||7212<=i&&i<=7219||7222<=i&&i<=7223||7376<=i&&i<=7378||7380<=i&&i<=7392||7394<=i&&i<=7400||7405==i||7412==i||7416<=i&&i<=7417||7616<=i&&i<=7673||7675<=i&&i<=7679||8204==i||8400<=i&&i<=8412||8413<=i&&i<=8416||8417==i||8418<=i&&i<=8420||8421<=i&&i<=8432||11503<=i&&i<=11505||11647==i||11744<=i&&i<=11775||12330<=i&&i<=12333||12334<=i&&i<=12335||12441<=i&&i<=12442||42607==i||42608<=i&&i<=42610||42612<=i&&i<=42621||42654<=i&&i<=42655||42736<=i&&i<=42737||43010==i||43014==i||43019==i||43045<=i&&i<=43046||43204<=i&&i<=43205||43232<=i&&i<=43249||43302<=i&&i<=43309||43335<=i&&i<=43345||43392<=i&&i<=43394||43443==i||43446<=i&&i<=43449||43452==i||43493==i||43561<=i&&i<=43566||43569<=i&&i<=43570||43573<=i&&i<=43574||43587==i||43596==i||43644==i||43696==i||43698<=i&&i<=43700||43703<=i&&i<=43704||43710<=i&&i<=43711||43713==i||43756<=i&&i<=43757||43766==i||44005==i||44008==i||44013==i||64286==i||65024<=i&&i<=65039||65056<=i&&i<=65071||65438<=i&&i<=65439||66045==i||66272==i||66422<=i&&i<=66426||68097<=i&&i<=68099||68101<=i&&i<=68102||68108<=i&&i<=68111||68152<=i&&i<=68154||68159==i||68325<=i&&i<=68326||69633==i||69688<=i&&i<=69702||69759<=i&&i<=69761||69811<=i&&i<=69814||69817<=i&&i<=69818||69888<=i&&i<=69890||69927<=i&&i<=69931||69933<=i&&i<=69940||70003==i||70016<=i&&i<=70017||70070<=i&&i<=70078||70090<=i&&i<=70092||70191<=i&&i<=70193||70196==i||70198<=i&&i<=70199||70206==i||70367==i||70371<=i&&i<=70378||70400<=i&&i<=70401||70460==i||70462==i||70464==i||70487==i||70502<=i&&i<=70508||70512<=i&&i<=70516||70712<=i&&i<=70719||70722<=i&&i<=70724||70726==i||70832==i||70835<=i&&i<=70840||70842==i||70845==i||70847<=i&&i<=70848||70850<=i&&i<=70851||71087==i||71090<=i&&i<=71093||71100<=i&&i<=71101||71103<=i&&i<=71104||71132<=i&&i<=71133||71219<=i&&i<=71226||71229==i||71231<=i&&i<=71232||71339==i||71341==i||71344<=i&&i<=71349||71351==i||71453<=i&&i<=71455||71458<=i&&i<=71461||71463<=i&&i<=71467||72193<=i&&i<=72198||72201<=i&&i<=72202||72243<=i&&i<=72248||72251<=i&&i<=72254||72263==i||72273<=i&&i<=72278||72281<=i&&i<=72283||72330<=i&&i<=72342||72344<=i&&i<=72345||72752<=i&&i<=72758||72760<=i&&i<=72765||72767==i||72850<=i&&i<=72871||72874<=i&&i<=72880||72882<=i&&i<=72883||72885<=i&&i<=72886||73009<=i&&i<=73014||73018==i||73020<=i&&i<=73021||73023<=i&&i<=73029||73031==i||92912<=i&&i<=92916||92976<=i&&i<=92982||94095<=i&&i<=94098||113821<=i&&i<=113822||119141==i||119143<=i&&i<=119145||119150<=i&&i<=119154||119163<=i&&i<=119170||119173<=i&&i<=119179||119210<=i&&i<=119213||119362<=i&&i<=119364||121344<=i&&i<=121398||121403<=i&&i<=121452||121461==i||121476==i||121499<=i&&i<=121503||121505<=i&&i<=121519||122880<=i&&i<=122886||122888<=i&&i<=122904||122907<=i&&i<=122913||122915<=i&&i<=122916||122918<=i&&i<=122922||125136<=i&&i<=125142||125252<=i&&i<=125258||917536<=i&&i<=917631||917760<=i&&i<=917999?e:127462<=i&&i<=127487?t:2307==i||2363==i||2366<=i&&i<=2368||2377<=i&&i<=2380||2382<=i&&i<=2383||2434<=i&&i<=2435||2495<=i&&i<=2496||2503<=i&&i<=2504||2507<=i&&i<=2508||2563==i||2622<=i&&i<=2624||2691==i||2750<=i&&i<=2752||2761==i||2763<=i&&i<=2764||2818<=i&&i<=2819||2880==i||2887<=i&&i<=2888||2891<=i&&i<=2892||3007==i||3009<=i&&i<=3010||3014<=i&&i<=3016||3018<=i&&i<=3020||3073<=i&&i<=3075||3137<=i&&i<=3140||3202<=i&&i<=3203||3262==i||3264<=i&&i<=3265||3267<=i&&i<=3268||3271<=i&&i<=3272||3274<=i&&i<=3275||3330<=i&&i<=3331||3391<=i&&i<=3392||3398<=i&&i<=3400||3402<=i&&i<=3404||3458<=i&&i<=3459||3536<=i&&i<=3537||3544<=i&&i<=3550||3570<=i&&i<=3571||3635==i||3763==i||3902<=i&&i<=3903||3967==i||4145==i||4155<=i&&i<=4156||4182<=i&&i<=4183||4228==i||6070==i||6078<=i&&i<=6085||6087<=i&&i<=6088||6435<=i&&i<=6438||6441<=i&&i<=6443||6448<=i&&i<=6449||6451<=i&&i<=6456||6681<=i&&i<=6682||6741==i||6743==i||6765<=i&&i<=6770||6916==i||6965==i||6971==i||6973<=i&&i<=6977||6979<=i&&i<=6980||7042==i||7073==i||7078<=i&&i<=7079||7082==i||7143==i||7146<=i&&i<=7148||7150==i||7154<=i&&i<=7155||7204<=i&&i<=7211||7220<=i&&i<=7221||7393==i||7410<=i&&i<=7411||7415==i||43043<=i&&i<=43044||43047==i||43136<=i&&i<=43137||43188<=i&&i<=43203||43346<=i&&i<=43347||43395==i||43444<=i&&i<=43445||43450<=i&&i<=43451||43453<=i&&i<=43456||43567<=i&&i<=43568||43571<=i&&i<=43572||43597==i||43755==i||43758<=i&&i<=43759||43765==i||44003<=i&&i<=44004||44006<=i&&i<=44007||44009<=i&&i<=44010||44012==i||69632==i||69634==i||69762==i||69808<=i&&i<=69810||69815<=i&&i<=69816||69932==i||70018==i||70067<=i&&i<=70069||70079<=i&&i<=70080||70188<=i&&i<=70190||70194<=i&&i<=70195||70197==i||70368<=i&&i<=70370||70402<=i&&i<=70403||70463==i||70465<=i&&i<=70468||70471<=i&&i<=70472||70475<=i&&i<=70477||70498<=i&&i<=70499||70709<=i&&i<=70711||70720<=i&&i<=70721||70725==i||70833<=i&&i<=70834||70841==i||70843<=i&&i<=70844||70846==i||70849==i||71088<=i&&i<=71089||71096<=i&&i<=71099||71102==i||71216<=i&&i<=71218||71227<=i&&i<=71228||71230==i||71340==i||71342<=i&&i<=71343||71350==i||71456<=i&&i<=71457||71462==i||72199<=i&&i<=72200||72249==i||72279<=i&&i<=72280||72343==i||72751==i||72766==i||72873==i||72881==i||72884==i||94033<=i&&i<=94078||119142==i||119149==i?5:4352<=i&&i<=4447||43360<=i&&i<=43388?6:4448<=i&&i<=4519||55216<=i&&i<=55238?7:4520<=i&&i<=4607||55243<=i&&i<=55291?8:44032==i||44060==i||44088==i||44116==i||44144==i||44172==i||44200==i||44228==i||44256==i||44284==i||44312==i||44340==i||44368==i||44396==i||44424==i||44452==i||44480==i||44508==i||44536==i||44564==i||44592==i||44620==i||44648==i||44676==i||44704==i||44732==i||44760==i||44788==i||44816==i||44844==i||44872==i||44900==i||44928==i||44956==i||44984==i||45012==i||45040==i||45068==i||45096==i||45124==i||45152==i||45180==i||45208==i||45236==i||45264==i||45292==i||45320==i||45348==i||45376==i||45404==i||45432==i||45460==i||45488==i||45516==i||45544==i||45572==i||45600==i||45628==i||45656==i||45684==i||45712==i||45740==i||45768==i||45796==i||45824==i||45852==i||45880==i||45908==i||45936==i||45964==i||45992==i||46020==i||46048==i||46076==i||46104==i||46132==i||46160==i||46188==i||46216==i||46244==i||46272==i||46300==i||46328==i||46356==i||46384==i||46412==i||46440==i||46468==i||46496==i||46524==i||46552==i||46580==i||46608==i||46636==i||46664==i||46692==i||46720==i||46748==i||46776==i||46804==i||46832==i||46860==i||46888==i||46916==i||46944==i||46972==i||47e3==i||47028==i||47056==i||47084==i||47112==i||47140==i||47168==i||47196==i||47224==i||47252==i||47280==i||47308==i||47336==i||47364==i||47392==i||47420==i||47448==i||47476==i||47504==i||47532==i||47560==i||47588==i||47616==i||47644==i||47672==i||47700==i||47728==i||47756==i||47784==i||47812==i||47840==i||47868==i||47896==i||47924==i||47952==i||47980==i||48008==i||48036==i||48064==i||48092==i||48120==i||48148==i||48176==i||48204==i||48232==i||48260==i||48288==i||48316==i||48344==i||48372==i||48400==i||48428==i||48456==i||48484==i||48512==i||48540==i||48568==i||48596==i||48624==i||48652==i||48680==i||48708==i||48736==i||48764==i||48792==i||48820==i||48848==i||48876==i||48904==i||48932==i||48960==i||48988==i||49016==i||49044==i||49072==i||49100==i||49128==i||49156==i||49184==i||49212==i||49240==i||49268==i||49296==i||49324==i||49352==i||49380==i||49408==i||49436==i||49464==i||49492==i||49520==i||49548==i||49576==i||49604==i||49632==i||49660==i||49688==i||49716==i||49744==i||49772==i||49800==i||49828==i||49856==i||49884==i||49912==i||49940==i||49968==i||49996==i||50024==i||50052==i||50080==i||50108==i||50136==i||50164==i||50192==i||50220==i||50248==i||50276==i||50304==i||50332==i||50360==i||50388==i||50416==i||50444==i||50472==i||50500==i||50528==i||50556==i||50584==i||50612==i||50640==i||50668==i||50696==i||50724==i||50752==i||50780==i||50808==i||50836==i||50864==i||50892==i||50920==i||50948==i||50976==i||51004==i||51032==i||51060==i||51088==i||51116==i||51144==i||51172==i||51200==i||51228==i||51256==i||51284==i||51312==i||51340==i||51368==i||51396==i||51424==i||51452==i||51480==i||51508==i||51536==i||51564==i||51592==i||51620==i||51648==i||51676==i||51704==i||51732==i||51760==i||51788==i||51816==i||51844==i||51872==i||51900==i||51928==i||51956==i||51984==i||52012==i||52040==i||52068==i||52096==i||52124==i||52152==i||52180==i||52208==i||52236==i||52264==i||52292==i||52320==i||52348==i||52376==i||52404==i||52432==i||52460==i||52488==i||52516==i||52544==i||52572==i||52600==i||52628==i||52656==i||52684==i||52712==i||52740==i||52768==i||52796==i||52824==i||52852==i||52880==i||52908==i||52936==i||52964==i||52992==i||53020==i||53048==i||53076==i||53104==i||53132==i||53160==i||53188==i||53216==i||53244==i||53272==i||53300==i||53328==i||53356==i||53384==i||53412==i||53440==i||53468==i||53496==i||53524==i||53552==i||53580==i||53608==i||53636==i||53664==i||53692==i||53720==i||53748==i||53776==i||53804==i||53832==i||53860==i||53888==i||53916==i||53944==i||53972==i||54e3==i||54028==i||54056==i||54084==i||54112==i||54140==i||54168==i||54196==i||54224==i||54252==i||54280==i||54308==i||54336==i||54364==i||54392==i||54420==i||54448==i||54476==i||54504==i||54532==i||54560==i||54588==i||54616==i||54644==i||54672==i||54700==i||54728==i||54756==i||54784==i||54812==i||54840==i||54868==i||54896==i||54924==i||54952==i||54980==i||55008==i||55036==i||55064==i||55092==i||55120==i||55148==i||55176==i?9:44033<=i&&i<=44059||44061<=i&&i<=44087||44089<=i&&i<=44115||44117<=i&&i<=44143||44145<=i&&i<=44171||44173<=i&&i<=44199||44201<=i&&i<=44227||44229<=i&&i<=44255||44257<=i&&i<=44283||44285<=i&&i<=44311||44313<=i&&i<=44339||44341<=i&&i<=44367||44369<=i&&i<=44395||44397<=i&&i<=44423||44425<=i&&i<=44451||44453<=i&&i<=44479||44481<=i&&i<=44507||44509<=i&&i<=44535||44537<=i&&i<=44563||44565<=i&&i<=44591||44593<=i&&i<=44619||44621<=i&&i<=44647||44649<=i&&i<=44675||44677<=i&&i<=44703||44705<=i&&i<=44731||44733<=i&&i<=44759||44761<=i&&i<=44787||44789<=i&&i<=44815||44817<=i&&i<=44843||44845<=i&&i<=44871||44873<=i&&i<=44899||44901<=i&&i<=44927||44929<=i&&i<=44955||44957<=i&&i<=44983||44985<=i&&i<=45011||45013<=i&&i<=45039||45041<=i&&i<=45067||45069<=i&&i<=45095||45097<=i&&i<=45123||45125<=i&&i<=45151||45153<=i&&i<=45179||45181<=i&&i<=45207||45209<=i&&i<=45235||45237<=i&&i<=45263||45265<=i&&i<=45291||45293<=i&&i<=45319||45321<=i&&i<=45347||45349<=i&&i<=45375||45377<=i&&i<=45403||45405<=i&&i<=45431||45433<=i&&i<=45459||45461<=i&&i<=45487||45489<=i&&i<=45515||45517<=i&&i<=45543||45545<=i&&i<=45571||45573<=i&&i<=45599||45601<=i&&i<=45627||45629<=i&&i<=45655||45657<=i&&i<=45683||45685<=i&&i<=45711||45713<=i&&i<=45739||45741<=i&&i<=45767||45769<=i&&i<=45795||45797<=i&&i<=45823||45825<=i&&i<=45851||45853<=i&&i<=45879||45881<=i&&i<=45907||45909<=i&&i<=45935||45937<=i&&i<=45963||45965<=i&&i<=45991||45993<=i&&i<=46019||46021<=i&&i<=46047||46049<=i&&i<=46075||46077<=i&&i<=46103||46105<=i&&i<=46131||46133<=i&&i<=46159||46161<=i&&i<=46187||46189<=i&&i<=46215||46217<=i&&i<=46243||46245<=i&&i<=46271||46273<=i&&i<=46299||46301<=i&&i<=46327||46329<=i&&i<=46355||46357<=i&&i<=46383||46385<=i&&i<=46411||46413<=i&&i<=46439||46441<=i&&i<=46467||46469<=i&&i<=46495||46497<=i&&i<=46523||46525<=i&&i<=46551||46553<=i&&i<=46579||46581<=i&&i<=46607||46609<=i&&i<=46635||46637<=i&&i<=46663||46665<=i&&i<=46691||46693<=i&&i<=46719||46721<=i&&i<=46747||46749<=i&&i<=46775||46777<=i&&i<=46803||46805<=i&&i<=46831||46833<=i&&i<=46859||46861<=i&&i<=46887||46889<=i&&i<=46915||46917<=i&&i<=46943||46945<=i&&i<=46971||46973<=i&&i<=46999||47001<=i&&i<=47027||47029<=i&&i<=47055||47057<=i&&i<=47083||47085<=i&&i<=47111||47113<=i&&i<=47139||47141<=i&&i<=47167||47169<=i&&i<=47195||47197<=i&&i<=47223||47225<=i&&i<=47251||47253<=i&&i<=47279||47281<=i&&i<=47307||47309<=i&&i<=47335||47337<=i&&i<=47363||47365<=i&&i<=47391||47393<=i&&i<=47419||47421<=i&&i<=47447||47449<=i&&i<=47475||47477<=i&&i<=47503||47505<=i&&i<=47531||47533<=i&&i<=47559||47561<=i&&i<=47587||47589<=i&&i<=47615||47617<=i&&i<=47643||47645<=i&&i<=47671||47673<=i&&i<=47699||47701<=i&&i<=47727||47729<=i&&i<=47755||47757<=i&&i<=47783||47785<=i&&i<=47811||47813<=i&&i<=47839||47841<=i&&i<=47867||47869<=i&&i<=47895||47897<=i&&i<=47923||47925<=i&&i<=47951||47953<=i&&i<=47979||47981<=i&&i<=48007||48009<=i&&i<=48035||48037<=i&&i<=48063||48065<=i&&i<=48091||48093<=i&&i<=48119||48121<=i&&i<=48147||48149<=i&&i<=48175||48177<=i&&i<=48203||48205<=i&&i<=48231||48233<=i&&i<=48259||48261<=i&&i<=48287||48289<=i&&i<=48315||48317<=i&&i<=48343||48345<=i&&i<=48371||48373<=i&&i<=48399||48401<=i&&i<=48427||48429<=i&&i<=48455||48457<=i&&i<=48483||48485<=i&&i<=48511||48513<=i&&i<=48539||48541<=i&&i<=48567||48569<=i&&i<=48595||48597<=i&&i<=48623||48625<=i&&i<=48651||48653<=i&&i<=48679||48681<=i&&i<=48707||48709<=i&&i<=48735||48737<=i&&i<=48763||48765<=i&&i<=48791||48793<=i&&i<=48819||48821<=i&&i<=48847||48849<=i&&i<=48875||48877<=i&&i<=48903||48905<=i&&i<=48931||48933<=i&&i<=48959||48961<=i&&i<=48987||48989<=i&&i<=49015||49017<=i&&i<=49043||49045<=i&&i<=49071||49073<=i&&i<=49099||49101<=i&&i<=49127||49129<=i&&i<=49155||49157<=i&&i<=49183||49185<=i&&i<=49211||49213<=i&&i<=49239||49241<=i&&i<=49267||49269<=i&&i<=49295||49297<=i&&i<=49323||49325<=i&&i<=49351||49353<=i&&i<=49379||49381<=i&&i<=49407||49409<=i&&i<=49435||49437<=i&&i<=49463||49465<=i&&i<=49491||49493<=i&&i<=49519||49521<=i&&i<=49547||49549<=i&&i<=49575||49577<=i&&i<=49603||49605<=i&&i<=49631||49633<=i&&i<=49659||49661<=i&&i<=49687||49689<=i&&i<=49715||49717<=i&&i<=49743||49745<=i&&i<=49771||49773<=i&&i<=49799||49801<=i&&i<=49827||49829<=i&&i<=49855||49857<=i&&i<=49883||49885<=i&&i<=49911||49913<=i&&i<=49939||49941<=i&&i<=49967||49969<=i&&i<=49995||49997<=i&&i<=50023||50025<=i&&i<=50051||50053<=i&&i<=50079||50081<=i&&i<=50107||50109<=i&&i<=50135||50137<=i&&i<=50163||50165<=i&&i<=50191||50193<=i&&i<=50219||50221<=i&&i<=50247||50249<=i&&i<=50275||50277<=i&&i<=50303||50305<=i&&i<=50331||50333<=i&&i<=50359||50361<=i&&i<=50387||50389<=i&&i<=50415||50417<=i&&i<=50443||50445<=i&&i<=50471||50473<=i&&i<=50499||50501<=i&&i<=50527||50529<=i&&i<=50555||50557<=i&&i<=50583||50585<=i&&i<=50611||50613<=i&&i<=50639||50641<=i&&i<=50667||50669<=i&&i<=50695||50697<=i&&i<=50723||50725<=i&&i<=50751||50753<=i&&i<=50779||50781<=i&&i<=50807||50809<=i&&i<=50835||50837<=i&&i<=50863||50865<=i&&i<=50891||50893<=i&&i<=50919||50921<=i&&i<=50947||50949<=i&&i<=50975||50977<=i&&i<=51003||51005<=i&&i<=51031||51033<=i&&i<=51059||51061<=i&&i<=51087||51089<=i&&i<=51115||51117<=i&&i<=51143||51145<=i&&i<=51171||51173<=i&&i<=51199||51201<=i&&i<=51227||51229<=i&&i<=51255||51257<=i&&i<=51283||51285<=i&&i<=51311||51313<=i&&i<=51339||51341<=i&&i<=51367||51369<=i&&i<=51395||51397<=i&&i<=51423||51425<=i&&i<=51451||51453<=i&&i<=51479||51481<=i&&i<=51507||51509<=i&&i<=51535||51537<=i&&i<=51563||51565<=i&&i<=51591||51593<=i&&i<=51619||51621<=i&&i<=51647||51649<=i&&i<=51675||51677<=i&&i<=51703||51705<=i&&i<=51731||51733<=i&&i<=51759||51761<=i&&i<=51787||51789<=i&&i<=51815||51817<=i&&i<=51843||51845<=i&&i<=51871||51873<=i&&i<=51899||51901<=i&&i<=51927||51929<=i&&i<=51955||51957<=i&&i<=51983||51985<=i&&i<=52011||52013<=i&&i<=52039||52041<=i&&i<=52067||52069<=i&&i<=52095||52097<=i&&i<=52123||52125<=i&&i<=52151||52153<=i&&i<=52179||52181<=i&&i<=52207||52209<=i&&i<=52235||52237<=i&&i<=52263||52265<=i&&i<=52291||52293<=i&&i<=52319||52321<=i&&i<=52347||52349<=i&&i<=52375||52377<=i&&i<=52403||52405<=i&&i<=52431||52433<=i&&i<=52459||52461<=i&&i<=52487||52489<=i&&i<=52515||52517<=i&&i<=52543||52545<=i&&i<=52571||52573<=i&&i<=52599||52601<=i&&i<=52627||52629<=i&&i<=52655||52657<=i&&i<=52683||52685<=i&&i<=52711||52713<=i&&i<=52739||52741<=i&&i<=52767||52769<=i&&i<=52795||52797<=i&&i<=52823||52825<=i&&i<=52851||52853<=i&&i<=52879||52881<=i&&i<=52907||52909<=i&&i<=52935||52937<=i&&i<=52963||52965<=i&&i<=52991||52993<=i&&i<=53019||53021<=i&&i<=53047||53049<=i&&i<=53075||53077<=i&&i<=53103||53105<=i&&i<=53131||53133<=i&&i<=53159||53161<=i&&i<=53187||53189<=i&&i<=53215||53217<=i&&i<=53243||53245<=i&&i<=53271||53273<=i&&i<=53299||53301<=i&&i<=53327||53329<=i&&i<=53355||53357<=i&&i<=53383||53385<=i&&i<=53411||53413<=i&&i<=53439||53441<=i&&i<=53467||53469<=i&&i<=53495||53497<=i&&i<=53523||53525<=i&&i<=53551||53553<=i&&i<=53579||53581<=i&&i<=53607||53609<=i&&i<=53635||53637<=i&&i<=53663||53665<=i&&i<=53691||53693<=i&&i<=53719||53721<=i&&i<=53747||53749<=i&&i<=53775||53777<=i&&i<=53803||53805<=i&&i<=53831||53833<=i&&i<=53859||53861<=i&&i<=53887||53889<=i&&i<=53915||53917<=i&&i<=53943||53945<=i&&i<=53971||53973<=i&&i<=53999||54001<=i&&i<=54027||54029<=i&&i<=54055||54057<=i&&i<=54083||54085<=i&&i<=54111||54113<=i&&i<=54139||54141<=i&&i<=54167||54169<=i&&i<=54195||54197<=i&&i<=54223||54225<=i&&i<=54251||54253<=i&&i<=54279||54281<=i&&i<=54307||54309<=i&&i<=54335||54337<=i&&i<=54363||54365<=i&&i<=54391||54393<=i&&i<=54419||54421<=i&&i<=54447||54449<=i&&i<=54475||54477<=i&&i<=54503||54505<=i&&i<=54531||54533<=i&&i<=54559||54561<=i&&i<=54587||54589<=i&&i<=54615||54617<=i&&i<=54643||54645<=i&&i<=54671||54673<=i&&i<=54699||54701<=i&&i<=54727||54729<=i&&i<=54755||54757<=i&&i<=54783||54785<=i&&i<=54811||54813<=i&&i<=54839||54841<=i&&i<=54867||54869<=i&&i<=54895||54897<=i&&i<=54923||54925<=i&&i<=54951||54953<=i&&i<=54979||54981<=i&&i<=55007||55009<=i&&i<=55035||55037<=i&&i<=55063||55065<=i&&i<=55091||55093<=i&&i<=55119||55121<=i&&i<=55147||55149<=i&&i<=55175||55177<=i&&i<=55203?10:9757==i||9977==i||9994<=i&&i<=9997||127877==i||127938<=i&&i<=127940||127943==i||127946<=i&&i<=127948||128066<=i&&i<=128067||128070<=i&&i<=128080||128110==i||128112<=i&&i<=128120||128124==i||128129<=i&&i<=128131||128133<=i&&i<=128135||128170==i||128372<=i&&i<=128373||128378==i||128400==i||128405<=i&&i<=128406||128581<=i&&i<=128583||128587<=i&&i<=128591||128675==i||128692<=i&&i<=128694||128704==i||128716==i||129304<=i&&i<=129308||129310<=i&&i<=129311||129318==i||129328<=i&&i<=129337||129341<=i&&i<=129342||129489<=i&&i<=129501?n:127995<=i&&i<=127999?14:8205==i?15:9792==i||9794==i||9877<=i&&i<=9878||9992==i||10084==i||127752==i||127806==i||127859==i||127891==i||127908==i||127912==i||127979==i||127981==i||128139==i||128187<=i&&i<=128188||128295==i||128300==i||128488==i||128640==i||128658==i?o:128102<=i&&i<=128105?s:11}return this.nextBreak=function(e,t){if(void 0===t&&(t=0),t<0)return 0;if(t>=e.length-1)return e.length;for(var r,n,o=c(i(e,t)),s=[],l=t+1;l<e.length;l++)if(n=l-1,!(55296<=(r=e).charCodeAt(n)&&r.charCodeAt(n)<=56319&&56320<=r.charCodeAt(n+1)&&r.charCodeAt(n+1)<=57343)){var u=c(i(e,l));if(a(o,s,u))return l;s.push(u)}return e.length},this.splitGraphemes=function(e){for(var t,r=[],n=0;(t=this.nextBreak(e,n))<e.length;)r.push(e.slice(n,t)),n=t;return n<e.length&&r.push(e.slice(n)),r},this.iterateGraphemes=function(e){var t=0,r={next:function(){var r,n;return(n=this.nextBreak(e,t))<e.length?(r=e.slice(t,n),t=n,{value:r,done:!1}):t<e.length?(r=e.slice(t),t=e.length,{value:r,done:!1}):{value:void 0,done:!0}}.bind(this)};return"undefined"!=typeof Symbol&&Symbol.iterator&&(r[Symbol.iterator]=function(){return r}),r},this.countGraphemes=function(e){for(var t,r=0,n=0;(t=this.nextBreak(e,n))<e.length;)n=t,r++;return n<e.length&&r++,r},this})},87612:(e,t,r)=>{"use strict";var n=r(40885)("%Object.defineProperty%",!0),o=function(){if(n)try{return n({},"a",{value:1}),!0}catch(e){return!1}return!1};o.hasArrayLengthDefineBug=function(){if(!o())return null;try{return 1!==n([],"length",{value:1}).length}catch(e){return!0}},e.exports=o},64310:e=>{"use strict";var t={foo:{}},r=Object;e.exports=function(){return{__proto__:t}.foo===t.foo&&!({__proto__:null}instanceof r)}},13518:(e,t,r)=>{"use strict";var n="undefined"!=typeof Symbol&&Symbol,o=r(71108);e.exports=function(){return"function"==typeof n&&"function"==typeof Symbol&&"symbol"==typeof n("foo")&&"symbol"==typeof Symbol("bar")&&o()}},71108:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,t);if(42!==o.value||!0!==o.enumerable)return!1}return!0}},63206:(e,t,r)=>{"use strict";var n=Function.prototype.call,o=Object.prototype.hasOwnProperty,s=r(72418);e.exports=s.call(n,o)},59295:e=>{var t=1e3,r=60*t,n=60*r,o=24*n,s=7*o;function i(e,t,r,n){var o=t>=1.5*r;return Math.round(e/r)+" "+n+(o?"s":"")}e.exports=function(e,a){a=a||{};var c,l,u=typeof e;if("string"===u&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var i=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(i){var a=parseFloat(i[1]);switch((i[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*a;case"weeks":case"week":case"w":return a*s;case"days":case"day":case"d":return a*o;case"hours":case"hour":case"hrs":case"hr":case"h":return a*n;case"minutes":case"minute":case"mins":case"min":case"m":return a*r;case"seconds":case"second":case"secs":case"sec":case"s":return a*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}}}(e);if("number"===u&&isFinite(e))return a.long?(c=e,(l=Math.abs(c))>=o?i(c,l,o,"day"):l>=n?i(c,l,n,"hour"):l>=r?i(c,l,r,"minute"):l>=t?i(c,l,t,"second"):c+" ms"):function(e){var s=Math.abs(e);return s>=o?Math.round(e/o)+"d":s>=n?Math.round(e/n)+"h":s>=r?Math.round(e/r)+"m":s>=t?Math.round(e/t)+"s":e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},83282:(e,t,r)=>{var n="function"==typeof Map&&Map.prototype,o=Object.getOwnPropertyDescriptor&&n?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,s=n&&o&&"function"==typeof o.get?o.get:null,i=n&&Map.prototype.forEach,a="function"==typeof Set&&Set.prototype,c=Object.getOwnPropertyDescriptor&&a?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,l=a&&c&&"function"==typeof c.get?c.get:null,u=a&&Set.prototype.forEach,d="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,p="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,m="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,f=Boolean.prototype.valueOf,h=Object.prototype.toString,y=Function.prototype.toString,g=String.prototype.match,w=String.prototype.slice,v=String.prototype.replace,b=String.prototype.toUpperCase,_=String.prototype.toLowerCase,x=RegExp.prototype.test,S=Array.prototype.concat,C=Array.prototype.join,k=Array.prototype.slice,j=Math.floor,E="function"==typeof BigInt?BigInt.prototype.valueOf:null,A=Object.getOwnPropertySymbols,P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,O="function"==typeof Symbol&&"object"==typeof Symbol.iterator,T="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol.toStringTag,1)?Symbol.toStringTag:null,N=Object.prototype.propertyIsEnumerable,I=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function R(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||x.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var n=e<0?-j(-e):j(e);if(n!==e){var o=String(n),s=w.call(t,o.length+1);return v.call(o,r,"$&_")+"."+v.call(v.call(s,/([0-9]{3})/g,"$&_"),/_$/,"")}}return v.call(t,r,"$&_")}var M=r(70123),F=M.custom,L=H(F)?F:null;function D(e,t,r){var n="double"===(r.quoteStyle||t)?'"':"'";return n+e+n}function V(e){return v.call(String(e),/"/g,"&quot;")}function B(e){return!("[object Array]"!==$(e)||T&&"object"==typeof e&&T in e)}function U(e){return!("[object RegExp]"!==$(e)||T&&"object"==typeof e&&T in e)}function H(e){if(O)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!P)return!1;try{return P.call(e),!0}catch(e){}return!1}e.exports=function e(t,n,o,a){var c=n||{};if(G(c,"quoteStyle")&&"single"!==c.quoteStyle&&"double"!==c.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(G(c,"maxStringLength")&&("number"==typeof c.maxStringLength?c.maxStringLength<0&&c.maxStringLength!==1/0:null!==c.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var h=!G(c,"customInspect")||c.customInspect;if("boolean"!=typeof h&&"symbol"!==h)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(G(c,"indent")&&null!==c.indent&&"\t"!==c.indent&&!(parseInt(c.indent,10)===c.indent&&c.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(G(c,"numericSeparator")&&"boolean"!=typeof c.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var b=c.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return q(t,c);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var x=String(t);return b?R(t,x):x}if("bigint"==typeof t){var j=String(t)+"n";return b?R(t,j):j}var A=void 0===c.depth?5:c.depth;if(void 0===o&&(o=0),o>=A&&A>0&&"object"==typeof t)return B(t)?"[Array]":"[Object]";var F,z=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=C.call(Array(e.indent+1)," ")}return{base:r,prev:C.call(Array(t+1),r)}}(c,o);if(void 0===a)a=[];else if(W(a,t)>=0)return"[Circular]";function Q(t,r,n){if(r&&(a=k.call(a)).push(r),n){var s={depth:c.depth};return G(c,"quoteStyle")&&(s.quoteStyle=c.quoteStyle),e(t,s,o+1,a)}return e(t,c,o+1,a)}if("function"==typeof t&&!U(t)){var ee=function(e){if(e.name)return e.name;var t=g.call(y.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),te=X(t,Q);return"[Function"+(ee?": "+ee:" (anonymous)")+"]"+(te.length>0?" { "+C.call(te,", ")+" }":"")}if(H(t)){var re=O?v.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):P.call(t);return"object"!=typeof t||O?re:Z(re)}if((F=t)&&"object"==typeof F&&("undefined"!=typeof HTMLElement&&F instanceof HTMLElement||"string"==typeof F.nodeName&&"function"==typeof F.getAttribute)){for(var ne="<"+_.call(String(t.nodeName)),oe=t.attributes||[],se=0;se<oe.length;se++)ne+=" "+oe[se].name+"="+D(V(oe[se].value),"double",c);return ne+=">",t.childNodes&&t.childNodes.length&&(ne+="..."),ne+"</"+_.call(String(t.nodeName))+">"}if(B(t)){if(0===t.length)return"[]";var ie=X(t,Q);return z&&!function(e){for(var t=0;t<e.length;t++)if(W(e[t],"\n")>=0)return!1;return!0}(ie)?"["+J(ie,z)+"]":"[ "+C.call(ie,", ")+" ]"}if(function(e){return!("[object Error]"!==$(e)||T&&"object"==typeof e&&T in e)}(t)){var ae=X(t,Q);return"cause"in Error.prototype||!("cause"in t)||N.call(t,"cause")?0===ae.length?"["+String(t)+"]":"{ ["+String(t)+"] "+C.call(ae,", ")+" }":"{ ["+String(t)+"] "+C.call(S.call("[cause]: "+Q(t.cause),ae),", ")+" }"}if("object"==typeof t&&h){if(L&&"function"==typeof t[L]&&M)return M(t,{depth:A-o});if("symbol"!==h&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!s||!e||"object"!=typeof e)return!1;try{s.call(e);try{l.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var ce=[];return i&&i.call(t,(function(e,r){ce.push(Q(r,t,!0)+" => "+Q(e,t))})),K("Map",s.call(t),ce,z)}if(function(e){if(!l||!e||"object"!=typeof e)return!1;try{l.call(e);try{s.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var le=[];return u&&u.call(t,(function(e){le.push(Q(e,t))})),K("Set",l.call(t),le,z)}if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{p.call(e,p)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return Y("WeakMap");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{p.call(e,p);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return Y("WeakSet");if(function(e){if(!m||!e||"object"!=typeof e)return!1;try{return m.call(e),!0}catch(e){}return!1}(t))return Y("WeakRef");if(function(e){return!("[object Number]"!==$(e)||T&&"object"==typeof e&&T in e)}(t))return Z(Q(Number(t)));if(function(e){if(!e||"object"!=typeof e||!E)return!1;try{return E.call(e),!0}catch(e){}return!1}(t))return Z(Q(E.call(t)));if(function(e){return!("[object Boolean]"!==$(e)||T&&"object"==typeof e&&T in e)}(t))return Z(f.call(t));if(function(e){return!("[object String]"!==$(e)||T&&"object"==typeof e&&T in e)}(t))return Z(Q(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if(t===r.g)return"{ [object globalThis] }";if(!function(e){return!("[object Date]"!==$(e)||T&&"object"==typeof e&&T in e)}(t)&&!U(t)){var ue=X(t,Q),de=I?I(t)===Object.prototype:t instanceof Object||t.constructor===Object,pe=t instanceof Object?"":"null prototype",me=!de&&T&&Object(t)===t&&T in t?w.call($(t),8,-1):pe?"Object":"",fe=(de||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(me||pe?"["+C.call(S.call([],me||[],pe||[]),": ")+"] ":"");return 0===ue.length?fe+"{}":z?fe+"{"+J(ue,z)+"}":fe+"{ "+C.call(ue,", ")+" }"}return String(t)};var z=Object.prototype.hasOwnProperty||function(e){return e in this};function G(e,t){return z.call(e,t)}function $(e){return h.call(e)}function W(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}function q(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return q(w.call(e,0,t.maxStringLength),t)+n}return D(v.call(v.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,Q),"single",t)}function Q(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+b.call(t.toString(16))}function Z(e){return"Object("+e+")"}function Y(e){return e+" { ? }"}function K(e,t,r,n){return e+" ("+t+") {"+(n?J(r,n):C.call(r,", "))+"}"}function J(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+C.call(e,","+r)+"\n"+t.prev}function X(e,t){var r=B(e),n=[];if(r){n.length=e.length;for(var o=0;o<e.length;o++)n[o]=G(e,o)?t(e[o],e):""}var s,i="function"==typeof A?A(e):[];if(O){s={};for(var a=0;a<i.length;a++)s["$"+i[a]]=i[a]}for(var c in e)G(e,c)&&(r&&String(Number(c))===c&&c<e.length||O&&s["$"+c]instanceof Symbol||(x.call(/[^\w$]/,c)?n.push(t(c,e)+": "+t(e[c],e)):n.push(c+": "+t(e[c],e))));if("function"==typeof A)for(var l=0;l<i.length;l++)N.call(e,i[l])&&n.push("["+t(i[l])+"]: "+t(e[i[l]],e));return n}},24294:e=>{"use strict";var t=String.prototype.replace,r=/%20/g,n="RFC3986";e.exports={default:n,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:n}},4594:(e,t,r)=>{"use strict";var n=r(61007),o=r(84977),s=r(24294);e.exports={formats:s,parse:o,stringify:n}},84977:(e,t,r)=>{"use strict";var n=r(50323),o=Object.prototype.hasOwnProperty,s=Array.isArray,i={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:n.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},a=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},c=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},l=function(e,t,r,n){if(e){var s=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,i=/(\[[^[\]]*])/g,a=r.depth>0&&/(\[[^[\]]*])/.exec(s),l=a?s.slice(0,a.index):s,u=[];if(l){if(!r.plainObjects&&o.call(Object.prototype,l)&&!r.allowPrototypes)return;u.push(l)}for(var d=0;r.depth>0&&null!==(a=i.exec(s))&&d<r.depth;){if(d+=1,!r.plainObjects&&o.call(Object.prototype,a[1].slice(1,-1))&&!r.allowPrototypes)return;u.push(a[1])}return a&&u.push("["+s.slice(a.index)+"]"),function(e,t,r,n){for(var o=n?t:c(t,r),s=e.length-1;s>=0;--s){var i,a=e[s];if("[]"===a&&r.parseArrays)i=[].concat(o);else{i=r.plainObjects?Object.create(null):{};var l="["===a.charAt(0)&&"]"===a.charAt(a.length-1)?a.slice(1,-1):a,u=parseInt(l,10);r.parseArrays||""!==l?!isNaN(u)&&a!==l&&String(u)===l&&u>=0&&r.parseArrays&&u<=r.arrayLimit?(i=[])[u]=o:"__proto__"!==l&&(i[l]=o):i={0:o}}o=i}return o}(u,t,r,n)}};e.exports=function(e,t){var r=function(e){if(!e)return i;if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?i.charset:e.charset;return{allowDots:void 0===e.allowDots?i.allowDots:!!e.allowDots,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:i.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:i.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:i.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:i.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:i.comma,decoder:"function"==typeof e.decoder?e.decoder:i.decoder,delimiter:"string"==typeof e.delimiter||n.isRegExp(e.delimiter)?e.delimiter:i.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:i.depth,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:i.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:i.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:i.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:i.strictNullHandling}}(t);if(""===e||null==e)return r.plainObjects?Object.create(null):{};for(var u="string"==typeof e?function(e,t){var r,l={__proto__:null},u=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,d=t.parameterLimit===1/0?void 0:t.parameterLimit,p=u.split(t.delimiter,d),m=-1,f=t.charset;if(t.charsetSentinel)for(r=0;r<p.length;++r)0===p[r].indexOf("utf8=")&&("utf8=%E2%9C%93"===p[r]?f="utf-8":"utf8=%26%2310003%3B"===p[r]&&(f="iso-8859-1"),m=r,r=p.length);for(r=0;r<p.length;++r)if(r!==m){var h,y,g=p[r],w=g.indexOf("]="),v=-1===w?g.indexOf("="):w+1;-1===v?(h=t.decoder(g,i.decoder,f,"key"),y=t.strictNullHandling?null:""):(h=t.decoder(g.slice(0,v),i.decoder,f,"key"),y=n.maybeMap(c(g.slice(v+1),t),(function(e){return t.decoder(e,i.decoder,f,"value")}))),y&&t.interpretNumericEntities&&"iso-8859-1"===f&&(y=a(y)),g.indexOf("[]=")>-1&&(y=s(y)?[y]:y),o.call(l,h)?l[h]=n.combine(l[h],y):l[h]=y}return l}(e,r):e,d=r.plainObjects?Object.create(null):{},p=Object.keys(u),m=0;m<p.length;++m){var f=p[m],h=l(f,u[f],r,"string"==typeof e);d=n.merge(d,h,r)}return!0===r.allowSparse?d:n.compact(d)}},61007:(e,t,r)=>{"use strict";var n=r(2435),o=r(50323),s=r(24294),i=Object.prototype.hasOwnProperty,a={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},c=Array.isArray,l=Array.prototype.push,u=function(e,t){l.apply(e,c(t)?t:[t])},d=Date.prototype.toISOString,p=s.default,m={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:o.encode,encodeValuesOnly:!1,format:p,formatter:s.formatters[p],indices:!1,serializeDate:function(e){return d.call(e)},skipNulls:!1,strictNullHandling:!1},f={},h=function e(t,r,s,i,a,l,d,p,h,y,g,w,v,b,_,x){for(var S,C=t,k=x,j=0,E=!1;void 0!==(k=k.get(f))&&!E;){var A=k.get(t);if(j+=1,void 0!==A){if(A===j)throw new RangeError("Cyclic object value");E=!0}void 0===k.get(f)&&(j=0)}if("function"==typeof p?C=p(r,C):C instanceof Date?C=g(C):"comma"===s&&c(C)&&(C=o.maybeMap(C,(function(e){return e instanceof Date?g(e):e}))),null===C){if(a)return d&&!b?d(r,m.encoder,_,"key",w):r;C=""}if("string"==typeof(S=C)||"number"==typeof S||"boolean"==typeof S||"symbol"==typeof S||"bigint"==typeof S||o.isBuffer(C))return d?[v(b?r:d(r,m.encoder,_,"key",w))+"="+v(d(C,m.encoder,_,"value",w))]:[v(r)+"="+v(String(C))];var P,O=[];if(void 0===C)return O;if("comma"===s&&c(C))b&&d&&(C=o.maybeMap(C,d)),P=[{value:C.length>0?C.join(",")||null:void 0}];else if(c(p))P=p;else{var T=Object.keys(C);P=h?T.sort(h):T}for(var N=i&&c(C)&&1===C.length?r+"[]":r,I=0;I<P.length;++I){var R=P[I],M="object"==typeof R&&void 0!==R.value?R.value:C[R];if(!l||null!==M){var F=c(C)?"function"==typeof s?s(N,R):N:N+(y?"."+R:"["+R+"]");x.set(t,j);var L=n();L.set(f,x),u(O,e(M,F,s,i,a,l,"comma"===s&&b&&c(C)?null:d,p,h,y,g,w,v,b,_,L))}}return O};e.exports=function(e,t){var r,o=e,l=function(e){if(!e)return m;if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||m.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=s.default;if(void 0!==e.format){if(!i.call(s.formatters,e.format))throw new TypeError("Unknown format option provided.");r=e.format}var n=s.formatters[r],o=m.filter;return("function"==typeof e.filter||c(e.filter))&&(o=e.filter),{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:m.addQueryPrefix,allowDots:void 0===e.allowDots?m.allowDots:!!e.allowDots,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:m.charsetSentinel,delimiter:void 0===e.delimiter?m.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:m.encode,encoder:"function"==typeof e.encoder?e.encoder:m.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:m.encodeValuesOnly,filter:o,format:r,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:m.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:m.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:m.strictNullHandling}}(t);"function"==typeof l.filter?o=(0,l.filter)("",o):c(l.filter)&&(r=l.filter);var d,p=[];if("object"!=typeof o||null===o)return"";d=t&&t.arrayFormat in a?t.arrayFormat:t&&"indices"in t?t.indices?"indices":"repeat":"indices";var f=a[d];if(t&&"commaRoundTrip"in t&&"boolean"!=typeof t.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var y="comma"===f&&t&&t.commaRoundTrip;r||(r=Object.keys(o)),l.sort&&r.sort(l.sort);for(var g=n(),w=0;w<r.length;++w){var v=r[w];l.skipNulls&&null===o[v]||u(p,h(o[v],v,f,y,l.strictNullHandling,l.skipNulls,l.encode?l.encoder:null,l.filter,l.sort,l.allowDots,l.serializeDate,l.format,l.formatter,l.encodeValuesOnly,l.charset,g))}var b=p.join(l.delimiter),_=!0===l.addQueryPrefix?"?":"";return l.charsetSentinel&&("iso-8859-1"===l.charset?_+="utf8=%26%2310003%3B&":_+="utf8=%E2%9C%93&"),b.length>0?_+b:""}},50323:(e,t,r)=>{"use strict";var n=r(24294),o=Object.prototype.hasOwnProperty,s=Array.isArray,i=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),a=function(e,t){for(var r=t&&t.plainObjects?Object.create(null):{},n=0;n<e.length;++n)void 0!==e[n]&&(r[n]=e[n]);return r};e.exports={arrayToObject:a,assign:function(e,t){return Object.keys(t).reduce((function(e,r){return e[r]=t[r],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],n=0;n<t.length;++n)for(var o=t[n],i=o.obj[o.prop],a=Object.keys(i),c=0;c<a.length;++c){var l=a[c],u=i[l];"object"==typeof u&&null!==u&&-1===r.indexOf(u)&&(t.push({obj:i,prop:l}),r.push(u))}return function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(s(r)){for(var n=[],o=0;o<r.length;++o)void 0!==r[o]&&n.push(r[o]);t.obj[t.prop]=n}}}(t),e},decode:function(e,t,r){var n=e.replace(/\+/g," ");if("iso-8859-1"===r)return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch(e){return n}},encode:function(e,t,r,o,s){if(0===e.length)return e;var a=e;if("symbol"==typeof e?a=Symbol.prototype.toString.call(e):"string"!=typeof e&&(a=String(e)),"iso-8859-1"===r)return escape(a).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var c="",l=0;l<a.length;++l){var u=a.charCodeAt(l);45===u||46===u||95===u||126===u||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122||s===n.RFC1738&&(40===u||41===u)?c+=a.charAt(l):u<128?c+=i[u]:u<2048?c+=i[192|u>>6]+i[128|63&u]:u<55296||u>=57344?c+=i[224|u>>12]+i[128|u>>6&63]+i[128|63&u]:(l+=1,u=65536+((1023&u)<<10|1023&a.charCodeAt(l)),c+=i[240|u>>18]+i[128|u>>12&63]+i[128|u>>6&63]+i[128|63&u])}return c},isBuffer:function(e){return!(!e||"object"!=typeof e||!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e)))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(s(e)){for(var r=[],n=0;n<e.length;n+=1)r.push(t(e[n]));return r}return t(e)},merge:function e(t,r,n){if(!r)return t;if("object"!=typeof r){if(s(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(n&&(n.plainObjects||n.allowPrototypes)||!o.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var i=t;return s(t)&&!s(r)&&(i=a(t,n)),s(t)&&s(r)?(r.forEach((function(r,s){if(o.call(t,s)){var i=t[s];i&&"object"==typeof i&&r&&"object"==typeof r?t[s]=e(i,r,n):t.push(r)}else t[s]=r})),t):Object.keys(r).reduce((function(t,s){var i=r[s];return o.call(t,s)?t[s]=e(t[s],i,n):t[s]=i,t}),i)}}},94931:(e,t,r)=>{"use strict";var n=r(51609),o=Symbol.for("react.element"),s=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,a=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function l(e,t,r){var n,s={},l=null,u=null;for(n in void 0!==r&&(l=""+r),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,n)&&!c.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===s[n]&&(s[n]=t[n]);return{$$typeof:o,type:e,key:l,ref:u,props:s,_owner:a.current}}t.Fragment=s,t.jsx=l,t.jsxs=l},39793:(e,t,r)=>{"use strict";e.exports=r(94931)},73745:(e,t,r)=>{"use strict";var n=r(40885),o=r(91555),s=r(87612)(),i=r(8632),a=n("%TypeError%"),c=n("%Math.floor%");e.exports=function(e,t){if("function"!=typeof e)throw new a("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||c(t)!==t)throw new a("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],n=!0,l=!0;if("length"in e&&i){var u=i(e,"length");u&&!u.configurable&&(n=!1),u&&!u.writable&&(l=!1)}return(n||l||!r)&&(s?o(e,"length",t,!0,!0):o(e,"length",t)),e}},2435:(e,t,r)=>{"use strict";var n=r(40885),o=r(40368),s=r(83282),i=n("%TypeError%"),a=n("%WeakMap%",!0),c=n("%Map%",!0),l=o("WeakMap.prototype.get",!0),u=o("WeakMap.prototype.set",!0),d=o("WeakMap.prototype.has",!0),p=o("Map.prototype.get",!0),m=o("Map.prototype.set",!0),f=o("Map.prototype.has",!0),h=function(e,t){for(var r,n=e;null!==(r=n.next);n=r)if(r.key===t)return n.next=r.next,r.next=e.next,e.next=r,r};e.exports=function(){var e,t,r,n={assert:function(e){if(!n.has(e))throw new i("Side channel does not contain "+s(e))},get:function(n){if(a&&n&&("object"==typeof n||"function"==typeof n)){if(e)return l(e,n)}else if(c){if(t)return p(t,n)}else if(r)return function(e,t){var r=h(e,t);return r&&r.value}(r,n)},has:function(n){if(a&&n&&("object"==typeof n||"function"==typeof n)){if(e)return d(e,n)}else if(c){if(t)return f(t,n)}else if(r)return function(e,t){return!!h(e,t)}(r,n);return!1},set:function(n,o){a&&n&&("object"==typeof n||"function"==typeof n)?(e||(e=new a),u(e,n,o)):c?(t||(t=new c),m(t,n,o)):(r||(r={key:{},next:null}),function(e,t,r){var n=h(e,t);n?n.value=r:e.next={key:t,next:e.next,value:r}}(r,n,o))}};return n}},51609:e=>{"use strict";e.exports=window.React},75795:e=>{"use strict";e.exports=window.ReactDOM},66087:e=>{"use strict";e.exports=window.lodash},76154:e=>{"use strict";e.exports=window.moment},45155:e=>{"use strict";e.exports=window.wc.adminLayout},98846:e=>{"use strict";e.exports=window.wc.components},94111:e=>{"use strict";e.exports=window.wc.currency},27752:e=>{"use strict";e.exports=window.wc.customerEffortScore},40314:e=>{"use strict";e.exports=window.wc.data},77374:e=>{"use strict";e.exports=window.wc.date},14908:e=>{"use strict";e.exports=window.wc.experimental},750:e=>{"use strict";e.exports=window.wc.explat},96476:e=>{"use strict";e.exports=window.wc.navigation},83306:e=>{"use strict";e.exports=window.wc.tracks},15703:e=>{"use strict";e.exports=window.wc.wcSettings},1455:e=>{"use strict";e.exports=window.wp.apiFetch},56427:e=>{"use strict";e.exports=window.wp.components},29491:e=>{"use strict";e.exports=window.wp.compose},47143:e=>{"use strict";e.exports=window.wp.data},86087:e=>{"use strict";e.exports=window.wp.element},52619:e=>{"use strict";e.exports=window.wp.hooks},18537:e=>{"use strict";e.exports=window.wp.htmlEntities},27723:e=>{"use strict";e.exports=window.wp.i18n},48558:e=>{"use strict";e.exports=window.wp.keycodes},92279:e=>{"use strict";e.exports=window.wp.plugins},5573:e=>{"use strict";e.exports=window.wp.primitives},93832:e=>{"use strict";e.exports=window.wp.url},70123:()=>{},4921:(e,t,r)=>{"use strict";function n(e){var t,r,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(r=n(e[t]))&&(o&&(o+=" "),o+=r)}else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}r.d(t,{A:()=>o});const o=function(){for(var e,t,r=0,o="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=n(e))&&(o&&(o+=" "),o+=t);return o}}},n={};function o(e){var t=n[e];if(void 0!==t)return t.exports;var s=n[e]={exports:{}};return r[e].call(s.exports,s,s.exports,o),s.exports}o.m=r,o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.f={},o.e=e=>Promise.all(Object.keys(o.f).reduce(((t,r)=>(o.f[r](e,t),t)),[])),o.u=e=>"chunks/"+({1438:"store-alerts",2464:"activity-panels-setup",3534:"shipping-recommendations",7956:"activity-panels-help",8039:"activity-panels-inbox"}[e]||e)+".js?ver="+{1438:"e14a1b76936d2b76ae49",2464:"ab6810e8fb033ee97eb5",3240:"e875968cbf383cfdb9fe",3534:"58c0d161ca97054c205a",3699:"cf8da90de2c7e71a9c40",4317:"8fd0c82633d94dff5dbd",5841:"b10e966eeff222363542",6568:"0c69d708d17267963866",7956:"5553b400608236f1316d",8039:"7b8d213a62e02fd43e72",8276:"119ad8582a9f1b379900",9670:"d592638b01a089e98868",9719:"1d8b6a12ef3178de7990",9994:"2bfe57964e976d1cbdd2"}[e],o.miniCssF=e=>"chunks/"+e+".style.css?ver="+{1438:"1a0ef7160a89435a3a20",3534:"a3cd233a85f546b463eb",3699:"4cf32095f267ef8be347",4317:"baabe1753a0027ffd2dd",5841:"88a79bf0b361b4f9a077",7956:"08abeb3d8724447da769",8039:"7ac669c717c4eb7f1a0d"}[e],o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="__wcAdmin_webpackJsonp:",o.l=(r,n,s,i)=>{if(e[r])e[r].push(n);else{var a,c;if(void 0!==s)for(var l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var d=l[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+s){a=d;break}}a||(c=!0,(a=document.createElement("script")).charset="utf-8",a.timeout=120,o.nc&&a.setAttribute("nonce",o.nc),a.setAttribute("data-webpack",t+s),a.src=r),e[r]=[n];var p=(t,n)=>{a.onerror=a.onload=null,clearTimeout(m);var o=e[r];if(delete e[r],a.parentNode&&a.parentNode.removeChild(a),o&&o.forEach((e=>e(n))),t)return t(n)},m=setTimeout(p.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=p.bind(null,a.onerror),a.onload=p.bind(null,a.onload),c&&document.head.appendChild(a)}},o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.j=1722,(()=>{var e;o.g.importScripts&&(e=o.g.location+"");var t=o.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var n=r.length-1;n>-1&&(!e||!/^http(s?):/.test(e));)e=r[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),o.p=e+"../"})(),(()=>{if("undefined"!=typeof document){var e={1722:0};o.f.miniCss=(t,r)=>{e[t]?r.push(e[t]):0!==e[t]&&{1438:1,3534:1,3699:1,4317:1,5841:1,7956:1,8039:1}[t]&&r.push(e[t]=(e=>new Promise(((t,r)=>{var n=o.miniCssF(e),s=o.p+n;if(((e,t)=>{for(var r=document.getElementsByTagName("link"),n=0;n<r.length;n++){var o=(i=r[n]).getAttribute("data-href")||i.getAttribute("href");if("stylesheet"===i.rel&&(o===e||o===t))return i}var s=document.getElementsByTagName("style");for(n=0;n<s.length;n++){var i;if((o=(i=s[n]).getAttribute("data-href"))===e||o===t)return i}})(n,s))return t();((e,t,r,n,s)=>{var i=document.createElement("link");i.rel="stylesheet",i.type="text/css",o.nc&&(i.nonce=o.nc),i.onerror=i.onload=r=>{if(i.onerror=i.onload=null,"load"===r.type)n();else{var o=r&&r.type,a=r&&r.target&&r.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+o+": "+a+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=o,c.request=a,i.parentNode&&i.parentNode.removeChild(i),s(c)}},i.href=t,document.head.appendChild(i)})(e,s,0,t,r)})))(t).then((()=>{e[t]=0}),(r=>{throw delete e[t],r})))}}})(),(()=>{var e={1722:0};o.f.j=(t,r)=>{var n=o.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var s=new Promise(((r,o)=>n=e[t]=[r,o]));r.push(n[2]=s);var i=o.p+o.u(t),a=new Error;o.l(i,(r=>{if(o.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var s=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;a.message="Loading chunk "+t+" failed.\n("+s+": "+i+")",a.name="ChunkLoadError",a.type=s,a.request=i,n[1](a)}}),"chunk-"+t,t)}};var t=(t,r)=>{var n,s,[i,a,c]=r,l=0;if(i.some((t=>0!==e[t]))){for(n in a)o.o(a,n)&&(o.m[n]=a[n]);c&&c(o)}for(t&&t(r);l<i.length;l++)s=i[l],o.o(e,s)&&e[s]&&e[s][0](),e[s]=0},r=globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";var e={};o.r(e),o.d(e,{activateInstalledPlugin:()=>Po,handleFetchError:()=>jo,installAndActivateRecommendedPlugin:()=>Oo,loadInstalledPluginsAfterActivation:()=>Ao,receiveActivatingPlugin:()=>_o,receiveBlogPosts:()=>ko,receiveInstalledPlugins:()=>bo,receiveMiscRecommendations:()=>Co,receiveRecommendedPlugins:()=>So,removeActivatingPlugin:()=>xo,setError:()=>Eo});var t={};o.r(t),o.d(t,{getActivatingPlugins:()=>No,getBlogPosts:()=>Mo,getBlogPostsError:()=>Fo,getInstalledPlugins:()=>To,getMiscRecommendations:()=>Ro,getRecommendedPlugins:()=>Io});var r={};o.r(r),o.d(r,{getBlogPosts:()=>Vo,getMiscRecommendations:()=>Do,getRecommendedPlugins:()=>Lo});const n=window.wc.remoteLogging;var s=o(27752),i=o(86087),a=o(91244),c=o.n(a),l=o(39793);const u=c()("wc-admin:client");var d=o(56109),p=o(52619),m=o(45155),f=o(4594),h=o(47143),y=o(40314);const g="woocommerce_show_marketplace_suggestions",w=({children:e})=>{const{currentUserCan:t}=(0,y.useUser)(),r=(0,h.useSelect)((e=>{const{getOption:t,hasFinishedResolution:r}=e(y.optionsStore),n=r("getOption",[g]),o="no"!==t(g);return n&&o}),[]);return t("install_plugins")&&r?(0,l.jsx)(l.Fragment,{children:e}):null},v=(0,i.lazy)((()=>window.wcAdminFeatures["shipping-smart-defaults"]?o.e(3534).then(o.bind(o,97607)):o.e(3534).then(o.bind(o,17336)))),b=[({})=>null,({page:e,tab:t,section:r,zone_id:n})=>"wc-settings"!==e||"shipping"!==t||Boolean(r)||Boolean(n)?null:(0,l.jsx)(w,{children:(0,l.jsx)(i.Suspense,{fallback:null,children:(0,l.jsx)(v,{})})})],_=()=>{(0,i.useEffect)((()=>{(0,s.triggerExitPageCesSurvey)()}),[]);const e=(0,f.parse)(location.search.substring(1));let t={page:"",tab:""};void 0!==e.page&&(t=e);const r=(0,p.applyFilters)("woocommerce_admin_embedded_layout_components",b,t);return(0,l.jsx)(m.LayoutContextProvider,{value:(0,m.getLayoutContextValue)(["page"]),children:(0,l.jsx)("div",{className:"woocommerce-embedded-layout__primary",id:"woocommerce-embedded-layout__primary",children:r.map(((e,r)=>(0,l.jsx)(e,{...t},r)))})})};window.wc.notices;class x extends i.Component{render(){return(0,l.jsx)("div",{id:"woocommerce-layout__notice-list",className:"woocommerce-layout__notice-list"})}}const S=x,C=(0,i.lazy)((()=>Promise.all([o.e(3240),o.e(1438)]).then(o.bind(o,46189)))),k=({children:e,showStoreAlerts:t=!0,showNotices:r=!0})=>(0,l.jsxs)("div",{className:"woocommerce-layout__primary",id:"woocommerce-layout__primary",children:[window.wcAdminFeatures["store-alerts"]&&t&&(0,l.jsx)(i.Suspense,{fallback:null,children:(0,l.jsx)(C,{})}),r&&(0,l.jsx)(S,{}),e]});var j=o(29491),E=o(66087),A=o(56427),P=o(96476),O=o(83306),T=o(92279),N=o(27723),I=o(24148),R=o(5573);const M=(0,l.jsx)(R.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(R.Path,{d:"M12 4.75a7.25 7.25 0 100 14.5 7.25 7.25 0 000-14.5zM3.25 12a8.75 8.75 0 1117.5 0 8.75 8.75 0 01-17.5 0zM12 8.75a1.5 1.5 0 01.167 2.99c-.465.052-.917.44-.917 1.01V14h1.5v-.845A3 3 0 109 10.25h1.5a1.5 1.5 0 011.5-1.5zM11.25 15v1.5h1.5V15h-1.5z"})}),F=(0,l.jsx)(R.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(R.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})});var L=o(98846),D=o(14908);const V=()=>(0,l.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,l.jsx)("mask",{id:"mask0_2915:6733",maskUnits:"userSpaceOnUse",x:"4",y:"3",width:"16",height:"18",children:(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.5 3.5H13.5L13.9 5.5H19.5V15.5H12.5L12.1 13.5H6.5V20.5H4.5V3.5ZM12.26 7.5L11.86 5.5H6.5V11.5H13.74L14.14 13.5H17.5V7.5H12.26Z",fill:"white"})}),(0,l.jsx)("g",{mask:"url(#mask0_2915:6733)",children:(0,l.jsx)("rect",{width:"24",height:"24",fill:"#50575E"})})]});var B=o(46591);const U={page:1,per_page:y.QUERY_DEFAULTS.pageSize,status:"unactioned",type:y.QUERY_DEFAULTS.noteTypes,orderby:"date",order:"desc"};function H(e){const{getNotes:t,getNotesError:r,isResolving:n}=e(y.notesStore),{getCurrentUser:o}=e(y.userStore),s=o(),i=parseInt(s&&s.woocommerce_meta&&s.woocommerce_meta.activity_panel_inbox_last_read,10);if(!i)return null;t(U);const a=Boolean(r("getNotes",[U])),c=n("getNotes",[U]);if(a||c)return null;const l=t(U);return(0,B.kT)(l,i)>0}var z=o(4921);const G=({icon:e,title:t,name:r,unread:n,selected:o,isPanelOpen:s,onTabClick:i})=>{const a=(0,z.A)("woocommerce-layout__activity-panel-tab",{"is-active":s&&o,"has-unread":n}),c=`activity-panel-tab-${r}`;return(0,l.jsxs)(A.Button,{role:"tab",className:a,"aria-selected":o,"aria-controls":`activity-panel-${r}`,id:c,"data-testid":c,onClick:()=>{i(r)},children:[e,t," ",n&&(0,l.jsx)("span",{className:"screen-reader-text",children:(0,N.__)("unread activity","woocommerce")})]},c)},$=({tabs:e,onTabClick:t,selectedTab:r,tabOpen:n=!1})=>{const[{tabOpen:o,currentTab:s},a]=(0,i.useState)({tabOpen:n,currentTab:r});return(0,i.useEffect)((()=>{a({tabOpen:n,currentTab:r})}),[n,r]),(0,l.jsx)(A.NavigableMenu,{role:"tablist",orientation:"horizontal",className:"woocommerce-layout__activity-panel-tabs",children:e&&e.map(((e,r)=>{if(e.component){const{component:t,options:n}=e;return(0,l.jsx)(t,{...n},r)}return(0,l.jsx)(G,{index:r,isPanelOpen:o,selected:s===e.name,...e,onTabClick:()=>{const r=s!==e.name&&""!==s||!o;r&&s===e.name||(0,O.recordEvent)("activity_panel_open",{tab:e.name}),a({tabOpen:r,currentTab:e.name}),t(e,r)}},r)}))})},W=({setupTasksComplete:e,setupCompletePercent:t})=>(0,l.jsxs)("svg",{className:"woocommerce-layout__activity-panel-tab-icon setup-progress",viewBox:"0 0 25 25",children:[(0,l.jsx)("path",{className:"setup-progress-ring",d:"M 12.476 23.237 C 18.369 23.237 23.146 18.414 23.146 12.464 C 23.146 6.512 18.369 1.687 12.476 1.687 C 6.581 1.687 1.803 6.512 1.803 12.464 C 1.803 18.414 6.581 23.237 12.476 23.237 Z"}),(0,l.jsx)("path",{className:"setup-progress-slice",transform:"matrix(-0.034188, 0, 0, 0.034134, 38.373184, -8.278505)",d:"M 522 607 A 237 237 0 0 1 759 370 L 759 607 Z",fill:e>0?"currentColor":"white"}),(0,l.jsx)("path",{className:"setup-progress-slice",transform:"matrix(-0.034188, 0, 0, -0.034134, 38.368454, 33.13131)",d:"M 522 607 A 237 237 0 0 1 759 370 L 759 607 Z",fill:t>=50?"currentColor":"white"}),(0,l.jsx)("path",{className:"setup-progress-slice",transform:"matrix(0.034188, 0, 0, -0.034134, -13.500516, 33.133827)",d:"M 522 607 A 237 237 0 0 1 759 370 L 759 607 Z",fill:t>=75?"currentColor":"white"}),(0,l.jsx)("path",{className:"setup-progress-slice",transform:"matrix(0.034188, 0, 0, 0.034134, -13.495783, -8.281025)",d:"M 522 607 A 237 237 0 0 1 759 370 L 759 607 Z",fill:"white"})]});var q=o(63861);const Q=window.wp.dom,Z=["button","submit"];const Y=({content:e,isPanelOpen:t,isPanelSwitching:r,currentTab:n,tab:o,closePanel:s,clearPanel:a})=>{const c="woocommerce-layout__activity-panel-wrapper",u=function(e="firstElement"){const t=(0,i.useRef)(e);return(0,i.useEffect)((()=>{t.current=e}),[e]),(0,i.useCallback)((e=>{if(!e||!1===t.current)return;if(e.contains(e.ownerDocument.activeElement))return;let r=e;if("firstElement"===t.current){const t=Q.focus.tabbable.find(e)[0];t&&(r=t)}r.focus()}),[])}(),d=(0,i.useRef)(null),p=function(e){const t=(0,i.useRef)(e);(0,i.useEffect)((()=>{t.current=e}),[e]);const r=(0,i.useRef)(!1),n=(0,i.useRef)(),o=(0,i.useCallback)((()=>{clearTimeout(n.current)}),[]);(0,i.useEffect)((()=>()=>o()),[]),(0,i.useEffect)((()=>{e||o()}),[e,o]);const s=(0,i.useCallback)((e=>{const{type:t,target:n}=e;(0,E.includes)(["mouseup","touchend"],t)?r.current=!1:function(e){if(!(e instanceof window.HTMLElement))return!1;switch(e.nodeName){case"A":case"BUTTON":return!0;case"INPUT":return(0,E.includes)(Z,e.type)}return!1}(n)&&(r.current=!0)}),[]),a=(0,i.useCallback)((e=>{e.persist(),r.current||(n.current=setTimeout((()=>{document.hasFocus()?"function"==typeof t.current&&t.current(e):e.preventDefault()}),0))}),[]);return{onFocus:o,onMouseDown:s,onMouseUp:s,onTouchStart:s,onTouchEnd:s,onBlur:a}}((e=>{const r=e.relatedTarget&&(e.relatedTarget.closest(".woocommerce-inbox-dismiss-confirmation_modal")||e.relatedTarget.closest(".components-snackbar__action"));t&&!r&&s()})),m=(0,i.useCallback)((e=>{d.current=e,u(e)}),[]);if(!o)return(0,l.jsx)("div",{className:c});if(!e)return null;const f=(0,z.A)(c,{"is-open":t,"is-switching":r});return(0,l.jsx)("div",{className:f,tabIndex:0,role:"tabpanel","aria-label":o.title,onTransitionEnd:e=>{e&&"transform"===e.propertyName&&(a(),d.current&&t&&o&&u(d.current))},...p,ref:m,children:(0,l.jsx)("div",{className:"woocommerce-layout__activity-panel-content",id:"activity-panel-"+n,children:(0,l.jsx)(i.Suspense,{fallback:(0,l.jsx)(L.Spinner,{}),children:e})},"activity-panel-"+n)})};var K=o(29332),J=o(42288),X=o(57882),ee=o(24060);const te=()=>(0,l.jsx)("svg",{width:"16",height:"17",viewBox:"0 0 16 17",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.68822 12.625L1.5 13.8145L1.5 1.5L14.5 1.5L14.5 12.625L2.68822 12.625ZM3.31 14.125L15 14.125C15.5523 14.125 16 13.6773 16 13.125L16 1C16 0.447717 15.5523 0 15 0H1C0.447717 0 0 0.447716 0 1V15.5247C0 15.8173 0.161234 16.086 0.419354 16.2237C0.727111 16.3878 1.10601 16.3313 1.35252 16.0845L3.31 14.125ZM12 5.99997H4V4.49997H12V5.99997ZM4 9.99997H9V8.49997H4V9.99997Z",fill:"currentColor"})});var re=o(51609),ne=o(15703);window.wp.coreData;const oe="__EXPERIMENTAL__WcAdminSettingsSlots",{Slot:se}=(0,A.createSlotFill)(oe),{Fill:ie}=(0,A.createSlotFill)(oe);var ae=o(99915);const ce=(0,l.jsx)(R.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(R.Path,{fillRule:"evenodd",d:"M7.25 16.437a6.5 6.5 0 1 1 9.5 0V16A2.75 2.75 0 0 0 14 13.25h-4A2.75 2.75 0 0 0 7.25 16v.437Zm1.5 1.193a6.47 6.47 0 0 0 3.25.87 6.47 6.47 0 0 0 3.25-.87V16c0-.69-.56-1.25-1.25-1.25h-4c-.69 0-1.25.56-1.25 1.25v1.63ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm10-2a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z",clipRule:"evenodd"})}),le=(0,l.jsx)(R.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(R.Path,{d:"M17.031 4.703 15.576 4l-1.56 3H14v.03l-2.324 4.47H9.5V13h1.396l-1.502 2.889h-.95a3.694 3.694 0 0 1 0-7.389H10V7H8.444a5.194 5.194 0 1 0 0 10.389h.17L7.5 19.53l1.416.719L15.049 8.5h.507a3.694 3.694 0 0 1 0 7.39H14v1.5h1.556a5.194 5.194 0 0 0 .273-10.383l1.202-2.304Z"})});function ue(e){const{setIsModalOpen:t,disconnectURL:r}=e,[n,o]=(0,i.useState)(!1),s=()=>t(!1);return(0,l.jsxs)(A.Modal,{title:(0,N.__)("Are you sure you want to disconnect?","woocommerce"),onRequestClose:s,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},size:"medium",overlayClassName:"woocommerce-marketplace__header-account-modal-overlay",children:[(0,l.jsx)("p",{className:"woocommerce-marketplace__header-account-modal-text",children:(0,N.__)("Keep your store connected to WooCommerce.com to get updates, manage your subscriptions, and receive streamlined support for your extensions and themes.","woocommerce")}),(0,l.jsxs)(A.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group",children:[(0,l.jsx)(A.Button,{variant:"tertiary",href:r,onClick:()=>o(!n),isBusy:n,isDestructive:!0,className:"woocommerce-marketplace__header-account-modal-button",children:(0,N.__)("Disconnect","woocommerce")}),(0,l.jsx)(A.Button,{variant:"primary",onClick:s,className:"woocommerce-marketplace__header-account-modal-button",children:(0,N.__)("Keep connected","woocommerce")})]})]})}d.kY,o(1455),window.wp.notices;const de={notices:{}},pe=(0,h.createReduxStore)("woocommerce-admin/subscription-notices",{reducer(e=de,t){switch(t.type){case"ADD_NOTICE":return{...e,notices:{...e.notices,[t.productKey]:{productKey:t.productKey,message:t.message,status:t.status,options:t.options}}};case"REMOVE_NOTICE":const r={...e.notices};return r[t.productKey]&&delete r[t.productKey],{...e,notices:r}}return e},actions:{addNotice:(e,t,r,n)=>({type:"ADD_NOTICE",productKey:e,message:t,status:r,options:n}),removeNotice:e=>({type:"REMOVE_NOTICE",productKey:e})},selectors:{notices:e=>e?Object.values(e.notices):[],getNotice(e,t){if(e)return e.notices[t]}}});(0,h.register)(pe),new Map;const me=e=>(0,l.jsx)(A.MenuItem,{...e});function fe({page:e="wc-admin"}){var t,r;const[n,o]=(0,i.useState)(!1),[s,a]=(0,i.useState)(!1),c=(0,d.Qk)("wccomHelper",{}),u=null!==(t=c?.isConnected)&&void 0!==t&&t,p=((e="wc-admin",t=!1)=>{const r=(0,d.Qk)("wccomHelper",{});if(!t&&!r.connectURL)return"";if(t&&!r.reConnectURL)return"";const n=t?r.reConnectURL:r.connectURL,o=new URL(window.location.href);return o.searchParams.set("page",e),((e,t)=>{if(!e)return e;const r=new URL(e);return r?(t.forEach((([e,t])=>{r.searchParams.set(e,t)})),r.toString()):e})(n,[["redirect_admin_url",encodeURIComponent(o.toString())],["page",e]])})(e),m=c?.userEmail,f=null!==(r=c?.userAvatar)&&void 0!==r?r:ce,h="https://woocommerce.com/my-dashboard/",y=u?h:p,g=u?(0,N.__)("Connected to WooCommerce.com","woocommerce"):(0,N.__)("Connect to WooCommerce.com","woocommerce");return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(A.DropdownMenu,{className:"woocommerce-layout__activity-panel-tab woocommerce-marketplace__user-menu",icon:!u||s?ce:(0,l.jsx)("img",{src:f,alt:"",className:"woocommerce-marketplace__menu-avatar-image",onError:()=>a(!0)}),label:(0,N.__)("User options","woocommerce"),toggleProps:{className:"woocommerce-layout__activity-panel-tab",onClick:()=>(0,O.recordEvent)("header_account_click",{page:e})},popoverProps:{className:"woocommerce-layout__activity-panel-popover"},children:()=>(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(A.MenuGroup,{className:"woocommerce-layout__homescreen-display-options",label:g,children:[(0,l.jsx)(me,{className:"woocommerce-marketplace__menu-item",href:y,onClick:()=>{u?(0,O.recordEvent)("header_account_view_click",{page:e}):(0,O.recordEvent)("header_account_connect_click",{page:e})},children:u?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(I.A,{icon:ce,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,l.jsx)("span",{className:"woocommerce-marketplace__main-text",children:m})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(I.A,{icon:ce,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,l.jsxs)("div",{className:"woocommerce-marketplace__menu-text",children:[(0,N.__)("Connect account","woocommerce"),(0,l.jsx)("span",{className:"woocommerce-marketplace__sub-text",children:(0,N.__)("Get product updates, manage your subscriptions from your store admin, and get streamlined support.","woocommerce")})]})]})}),"wc-addons"===e&&!u&&(0,l.jsxs)(me,{href:h,onClick:()=>(0,O.recordEvent)("header_account_view_click",{page:e}),children:[(0,l.jsx)(I.A,{icon:F,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,N.__)("WooCommerce.com account","woocommerce")]})]}),u&&(0,l.jsx)(A.MenuGroup,{className:"woocommerce-layout__homescreen-display-options",children:(0,l.jsxs)(me,{onClick:()=>{(0,O.recordEvent)("header_account_disconnect_click",{page:e}),o(!0)},children:[(0,l.jsx)(I.A,{icon:le,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,N.__)("Disconnect account","woocommerce")]})})]})}),n&&(0,l.jsx)(ue,{setIsModalOpen:o,disconnectURL:p})]})}const he=(0,i.lazy)((()=>Promise.all([o.e(9719),o.e(7956)]).then(o.bind(o,85810)))),ye=(0,i.lazy)((()=>Promise.all([o.e(8276),o.e(8039)]).then(o.bind(o,16421)))),ge=(0,i.lazy)((()=>Promise.all([o.e(3240),o.e(9994),o.e(4317),o.e(2464)]).then(o.bind(o,16081)))),we=({isEmbedded:e,query:t})=>{const r="wc-admin"===t.page&&!t.path,[n,o]=(0,i.useState)(""),[a,c]=(0,i.useState)(!1),[u,p]=(0,i.useState)(!1),[f,g]=(0,i.useState)(!1),{fills:w}=(0,D.useSlot)(X.JT),v=Boolean(w?.length),{comingSoon:b}=(({enabled:e}={enabled:!0})=>{const{isLoading:t,launchYourStoreEnabled:r,comingSoon:n,storePagesOnly:o,privateLink:s,shareKey:i}=(0,h.useSelect)((t=>{if(!e)return{isLoading:!1,comingSoon:null,storePagesOnly:null,privateLink:null,shareKey:null,launchYourStoreEnabled:null};const{hasFinishedResolution:r,getOption:n}=t(y.optionsStore);return{isLoading:!(r("getOption",["woocommerce_coming_soon"])||r("getOption",["woocommerce_store_pages_only"])||r("getOption",["woocommerce_private_link"])||r("getOption",["woocommerce_share_key"])),comingSoon:n("woocommerce_coming_soon"),storePagesOnly:n("woocommerce_store_pages_only"),privateLink:n("woocommerce_private_link"),shareKey:n("woocommerce_share_key"),launchYourStoreEnabled:window.wcAdminFeatures["launch-your-store"]}}),[e]);return{isLoading:t,comingSoon:n,storePagesOnly:o,privateLink:s,shareKey:i,launchYourStoreEnabled:r}})({enabled:r}),_=()=>{c(!0),p(!1)},x=()=>{u||(c(!1),g(!1),o(""))};(0,i.useEffect)((()=>(0,P.addHistoryListener)((()=>{_(),x()}))),[]);const S=(0,m.useExtendLayout)("activity-panel"),C=(0,i.useCallback)(((e,r)=>{let n={};if("wc-admin"===t.page&&"appearance"===t.task){const{getTaskLists:t}=e(y.onboardingStore),o=t().reduce(((e,t)=>[...e,...t.tasks]),[]).find((e=>"appearance"===e.id));n={set_notice:r("woocommerce_demo_store_notice")?"Y":"N",create_homepage:!0===o?.additionalData?.hasHomepage?"Y":"N",upload_logo:o?.additionalData?.themeMods?.custom_logo?"Y":"N"}}return n}),[t.page,t.task]),k=(0,i.useCallback)(((e,t,r)=>{const n=(0,K.VJ)(e),o=!!t&&(0,K.xC)(e,n)>0,s=!!t&&(0,J.my)(e),i=!!t&&(0,K.G9)(e);return r>0||o||s||i||v}),[v]),{requestingTaskListOptions:j,setupTaskListComplete:A,setupTaskListHidden:T,setupTasksCount:R,setupTasksCompleteCount:B,thingsToDoNextCount:U}=(0,ae.fK)(),{hasUnreadNotes:z,hasAbbreviatedNotifications:G,previewSiteBtnTrackData:Q}=(0,h.useSelect)((e=>{const{getOption:t}=e(y.optionsStore);return{hasUnreadNotes:H(e),hasAbbreviatedNotifications:k(e,T,U),previewSiteBtnTrackData:C(e,t)}}),[k,U,T,C]),{showCesModal:Z}=(0,h.useDispatch)(s.STORE_KEY),{currentUserCan:re}=(0,y.useUser)(),ne=()=>{const[e]=function(e){var t;const r=e?.startsWith("/")?1:0,n=e?.endsWith("/")?-1:void 0;return null!==(t=e?.slice(r,n)?.split("/"))&&void 0!==t?t:[]}(t.path);return"add-product"===e||"product"===e},oe=()=>{const t=(0,ee.al)(window.location.search);return e&&/post-new\.php$/.test(window.location.pathname)&&"product"===t?.post_type},se=()=>t.task&&!t.path&&(!0===j||!1===T&&!1===A),ie=()=>{const n={name:"activity",title:(0,N.__)("Activity","woocommerce"),icon:(0,l.jsx)(V,{}),unread:z||G,visible:(e||!r)&&!se()&&!ne()&&re("manage_woocommerce")},s={name:"feedback",title:(0,N.__)("Feedback","woocommerce"),icon:(0,l.jsx)(te,{}),onClick:()=>{o("feedback"),p(!0),Z({action:"product_feedback",title:(0,N.__)("How's your experience with the product editor?","woocommerce"),firstQuestion:(0,N.__)("The product editing screen is easy to use","woocommerce"),secondQuestion:(0,N.__)("The product editing screen's functionality meets my needs","woocommerce")},{onRecordScore:()=>{o(""),p(!1)},onCloseModal:()=>{o(""),p(!1)}},{type:"snackbar",icon:(0,l.jsx)("span",{children:"🌟"})})},visible:oe()},i={name:"setup",title:(0,N.__)("Finish setup","woocommerce"),icon:(0,l.jsx)(W,{setupTasksComplete:B,setupCompletePercent:Math.ceil(B/R*100)}),visible:re("manage_woocommerce")&&!j&&!T&&!A&&!r&&!ne()},a={name:"help",icon:(0,l.jsx)(I.A,{icon:M}),visible:re("manage_woocommerce")&&(r&&!e||se())},c={component:q.P,visible:re("manage_woocommerce")&&!e&&r&&!se()},u={component:()=>(0,l.jsx)(fe,{page:"wc-admin"}),visible:r};return[n,s,i,{name:"previewSite",title:(0,N.__)("Preview site","woocommerce"),icon:(0,l.jsx)(I.A,{icon:F}),visible:r&&"appearance"===t.task,onClick:()=>(window.open((0,d.Qk)("siteUrl")),(0,O.recordEvent)("wcadmin_tasklist_previewsite",Q),null)},{name:"previewStore",title:"yes"===b&&(0,N.__)("Preview store","woocommerce")||(0,N.__)("View store","woocommerce"),visible:r&&"appearance"!==t.task,onClick:()=>(window.open((0,d.Qk)("shopUrl")),(0,O.recordEvent)("wcadmin_previewstore_click"),null)},c,u,a].filter((e=>e.visible))},ce=ie(),le=(0,E.uniqueId)("activity-panel-header_");return(0,l.jsx)(m.LayoutContextProvider,{value:S,children:(0,l.jsxs)("div",{children:[(0,l.jsx)(L.H,{id:le,className:"screen-reader-text",children:(0,N.__)("Store Activity","woocommerce")}),(0,l.jsxs)(L.Section,{component:"aside",id:"woocommerce-activity-panel",className:"woocommerce-layout__activity-panel","aria-labelledby":le,children:[(0,l.jsx)($,{tabs:ce,tabOpen:u,selectedTab:n,onTabClick:(e,t)=>{e.onClick?e.onClick():(({name:e},t)=>{const r=e!==n&&""!==n&&t&&u;a||(o(e),p(t),g(r))})(e,t)}}),(0,l.jsx)(Y,{currentTab:!0,isPanelOpen:u,isPanelSwitching:f,tab:(0,E.find)(ie(),{name:n}),content:(e=>{const{task:r}=t;switch(e){case"activity":return(0,l.jsx)(ye,{hasAbbreviatedNotifications:G,thingsToDoNextCount:U});case"help":return(0,l.jsx)(he,{taskName:r});case"setup":return(0,l.jsx)(ge,{query:t});default:return null}})(n),closePanel:()=>_(),clearPanel:()=>x()})]})]})})},ve=["wc-settings"];(0,T.registerPlugin)("activity-panel-header-item",{render:()=>(0,l.jsx)(m.WooHeaderItem,{order:20,children:({isEmbedded:e,query:t})=>!window.wcAdminFeatures["activity-panels"]||ve.includes(t.page)?null:(0,l.jsx)(we,{isEmbedded:e,query:t})}),scope:"woocommerce-admin"});var be=o(18537),_e=o(11846);function xe(){return window.innerHeight+window.scrollY>=document.body.scrollHeight}function Se(){const[e,t]=(0,i.useState)(!1),[r,n]=(0,i.useState)(xe()),o=(0,i.useRef)(null);return(0,i.useEffect)((()=>{const e=()=>{t(window.pageYOffset>20),n(xe())},r=()=>{o.current=window.requestAnimationFrame(e)};return window.addEventListener("scroll",r),window.addEventListener("resize",r),()=>{window.removeEventListener("scroll",r),window.removeEventListener("resize",r),window.cancelAnimationFrame(o.current)}}),[]),{isScrolled:e,atBottom:r,atTop:!e}}const Ce=e=>{let t;return t=e.length>2&&Array.isArray(e[1])&&["admin.php?page=wc-settings","admin.php?page=wc-reports","admin.php?page=wc-status"].includes(e[1][0])?e[1][1]:e[e.length-1],t},ke=({isEmbedded:e,query:t,showReminderBar:r,sections:n,children:o,leftAlign:s=!0})=>{const{isScrolled:a}=Se(),c=(0,i.useRef)(null),u=(0,D.useSlot)(m.WC_HEADER_PAGE_TITLE_SLOT_NAME),d=Boolean(u?.fills?.length),p=(({headerElement:e,headerItemSlot:t})=>{const r=(0,i.useRef)(null),n=(0,i.useCallback)((()=>{r.current&&clearTimeout(r.current),r.current=setTimeout((function(){const t=document.querySelector("#wpbody");t&&e.current&&(t.style.marginTop=`${e.current.clientHeight}px`)}),200)}),[e]);return(0,i.useLayoutEffect)((()=>(n(),window.addEventListener("resize",n),()=>{window.removeEventListener("resize",n);const e=document.querySelector("#wpbody");e&&(e.style.marginTop="")})),[t?.fills,n]),n})({headerElement:c,headerItemSlot:(0,D.useSlot)(m.WC_HEADER_SLOT_NAME)});return(0,l.jsxs)("div",{className:(0,z.A)("woocommerce-layout__header",{"is-scrolled":a}),ref:c,children:[r&&(0,l.jsx)(_e.O,{updateBodyMargin:p,taskListId:"setup"}),(0,l.jsxs)("div",{className:"woocommerce-layout__header-wrapper",children:[(0,l.jsx)(m.WooHeaderNavigationItem.Slot,{fillProps:{isEmbedded:e,query:t}}),(0,l.jsx)(D.Text,{className:(0,z.A)("woocommerce-layout__header-heading",{"woocommerce-layout__header-left-align":s}),as:"h1",children:(0,be.decodeEntities)(d?(0,l.jsx)(m.WooHeaderPageTitle.Slot,{fillProps:{isEmbedded:e,query:t}}):Ce(n))}),o,(0,l.jsx)(m.WooHeaderItem.Slot,{fillProps:{isEmbedded:e,query:t}})]})]})},je=({sections:e,query:t})=>{const r=Boolean("wc-settings"===t?.page&&"checkout"===t?.tab),n=Boolean((0,ae.EM)("setup")&&!r);return(0,l.jsx)(ke,{isEmbedded:!0,query:t,sections:e,showReminderBar:n})};var Ee=Object.defineProperty,Ae={};((e,t)=>{for(var r in t)Ee(e,r,{get:t[r],enumerable:!0})})(Ae,{assign:()=>at,colors:()=>ot,createStringInterpolator:()=>et,skipAnimation:()=>st,to:()=>tt,willAdvance:()=>it});var Pe=$e(),Oe=e=>Ue(e,Pe),Te=$e();Oe.write=e=>Ue(e,Te);var Ne=$e();Oe.onStart=e=>Ue(e,Ne);var Ie=$e();Oe.onFrame=e=>Ue(e,Ie);var Re=$e();Oe.onFinish=e=>Ue(e,Re);var Me=[];Oe.setTimeout=(e,t)=>{const r=Oe.now()+t,n=()=>{const e=Me.findIndex((e=>e.cancel==n));~e&&Me.splice(e,1),Ve-=~e?1:0},o={time:r,handler:e,cancel:n};return Me.splice(Fe(r),0,o),Ve+=1,He(),o};var Fe=e=>~(~Me.findIndex((t=>t.time>e))||~Me.length);Oe.cancel=e=>{Ne.delete(e),Ie.delete(e),Re.delete(e),Pe.delete(e),Te.delete(e)},Oe.sync=e=>{Be=!0,Oe.batchedUpdates(e),Be=!1},Oe.throttle=e=>{let t;function r(){try{e(...t)}finally{t=null}}function n(...e){t=e,Oe.onStart(r)}return n.handler=e,n.cancel=()=>{Ne.delete(r),t=null},n};var Le="undefined"!=typeof window?window.requestAnimationFrame:()=>{};Oe.use=e=>Le=e,Oe.now="undefined"!=typeof performance?()=>performance.now():Date.now,Oe.batchedUpdates=e=>e(),Oe.catch=console.error,Oe.frameLoop="always",Oe.advance=()=>{"demand"!==Oe.frameLoop?console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"):Ge()};var De=-1,Ve=0,Be=!1;function Ue(e,t){Be?(t.delete(e),e(0)):(t.add(e),He())}function He(){De<0&&(De=0,"demand"!==Oe.frameLoop&&Le(ze))}function ze(){~De&&(Le(ze),Oe.batchedUpdates(Ge))}function Ge(){const e=De;De=Oe.now();const t=Fe(De);t&&(We(Me.splice(0,t),(e=>e.handler())),Ve-=t),Ve?(Ne.flush(),Pe.flush(e?Math.min(64,De-e):16.667),Ie.flush(),Te.flush(),Re.flush()):De=-1}function $e(){let e=new Set,t=e;return{add(r){Ve+=t!=e||e.has(r)?0:1,e.add(r)},delete:r=>(Ve-=t==e&&e.has(r)?1:0,e.delete(r)),flush(r){t.size&&(e=new Set,Ve-=t.size,We(t,(t=>t(r)&&e.add(t))),Ve+=e.size,t=e)}}}function We(e,t){e.forEach((e=>{try{t(e)}catch(e){Oe.catch(e)}}))}function qe(){}var Qe={arr:Array.isArray,obj:e=>!!e&&"Object"===e.constructor.name,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,und:e=>void 0===e};function Ze(e,t){if(Qe.arr(e)){if(!Qe.arr(t)||e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}return e===t}var Ye=(e,t)=>e.forEach(t);function Ke(e,t,r){if(Qe.arr(e))for(let n=0;n<e.length;n++)t.call(r,e[n],`${n}`);else for(const n in e)e.hasOwnProperty(n)&&t.call(r,e[n],n)}var Je=e=>Qe.und(e)?[]:Qe.arr(e)?e:[e];function Xe(e,t){if(e.size){const r=Array.from(e);e.clear(),Ye(r,t)}}var et,tt,rt=(e,...t)=>Xe(e,(e=>e(...t))),nt=()=>"undefined"==typeof window||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent),ot=null,st=!1,it=qe,at=e=>{e.to&&(tt=e.to),e.now&&(Oe.now=e.now),void 0!==e.colors&&(ot=e.colors),null!=e.skipAnimation&&(st=e.skipAnimation),e.createStringInterpolator&&(et=e.createStringInterpolator),e.requestAnimationFrame&&Oe.use(e.requestAnimationFrame),e.batchedUpdates&&(Oe.batchedUpdates=e.batchedUpdates),e.willAdvance&&(it=e.willAdvance),e.frameLoop&&(Oe.frameLoop=e.frameLoop)},ct=new Set,lt=[],ut=[],dt=0,pt={get idle(){return!ct.size&&!lt.length},start(e){dt>e.priority?(ct.add(e),Oe.onStart(mt)):(ft(e),Oe(yt))},advance:yt,sort(e){if(dt)Oe.onFrame((()=>pt.sort(e)));else{const t=lt.indexOf(e);~t&&(lt.splice(t,1),ht(e))}},clear(){lt=[],ct.clear()}};function mt(){ct.forEach(ft),ct.clear(),Oe(yt)}function ft(e){lt.includes(e)||ht(e)}function ht(e){lt.splice(function(t){const r=t.findIndex((t=>t.priority>e.priority));return r<0?t.length:r}(lt),0,e)}function yt(e){const t=ut;for(let r=0;r<lt.length;r++){const n=lt[r];dt=n.priority,n.idle||(it(n),n.advance(e),n.idle||t.push(n))}return dt=0,(ut=lt).length=0,(lt=t).length>0}var gt="[-+]?\\d*\\.?\\d+",wt=gt+"%";function vt(...e){return"\\(\\s*("+e.join(")\\s*,\\s*(")+")\\s*\\)"}var bt=new RegExp("rgb"+vt(gt,gt,gt)),_t=new RegExp("rgba"+vt(gt,gt,gt,gt)),xt=new RegExp("hsl"+vt(gt,wt,wt)),St=new RegExp("hsla"+vt(gt,wt,wt,gt)),Ct=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,kt=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,jt=/^#([0-9a-fA-F]{6})$/,Et=/^#([0-9a-fA-F]{8})$/;function At(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+6*(t-e)*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function Pt(e,t,r){const n=r<.5?r*(1+t):r+t-r*t,o=2*r-n,s=At(o,n,e+1/3),i=At(o,n,e),a=At(o,n,e-1/3);return Math.round(255*s)<<24|Math.round(255*i)<<16|Math.round(255*a)<<8}function Ot(e){const t=parseInt(e,10);return t<0?0:t>255?255:t}function Tt(e){return(parseFloat(e)%360+360)%360/360}function Nt(e){const t=parseFloat(e);return t<0?0:t>1?255:Math.round(255*t)}function It(e){const t=parseFloat(e);return t<0?0:t>100?1:t/100}function Rt(e){let t=function(e){let t;return"number"==typeof e?e>>>0===e&&e>=0&&e<=4294967295?e:null:(t=jt.exec(e))?parseInt(t[1]+"ff",16)>>>0:ot&&void 0!==ot[e]?ot[e]:(t=bt.exec(e))?(Ot(t[1])<<24|Ot(t[2])<<16|Ot(t[3])<<8|255)>>>0:(t=_t.exec(e))?(Ot(t[1])<<24|Ot(t[2])<<16|Ot(t[3])<<8|Nt(t[4]))>>>0:(t=Ct.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+"ff",16)>>>0:(t=Et.exec(e))?parseInt(t[1],16)>>>0:(t=kt.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+t[4]+t[4],16)>>>0:(t=xt.exec(e))?(255|Pt(Tt(t[1]),It(t[2]),It(t[3])))>>>0:(t=St.exec(e))?(Pt(Tt(t[1]),It(t[2]),It(t[3]))|Nt(t[4]))>>>0:null}(e);return null===t?e:(t=t||0,`rgba(${(4278190080&t)>>>24}, ${(16711680&t)>>>16}, ${(65280&t)>>>8}, ${(255&t)/255})`)}var Mt=(e,t,r)=>{if(Qe.fun(e))return e;if(Qe.arr(e))return Mt({range:e,output:t,extrapolate:r});if(Qe.str(e.output[0]))return et(e);const n=e,o=n.output,s=n.range||[0,1],i=n.extrapolateLeft||n.extrapolate||"extend",a=n.extrapolateRight||n.extrapolate||"extend",c=n.easing||(e=>e);return e=>{const t=function(e,t){for(var r=1;r<t.length-1&&!(t[r]>=e);++r);return r-1}(e,s);return function(e,t,r,n,o,s,i,a,c){let l=c?c(e):e;if(l<t){if("identity"===i)return l;"clamp"===i&&(l=t)}if(l>r){if("identity"===a)return l;"clamp"===a&&(l=r)}return n===o?n:t===r?e<=t?n:o:(t===-1/0?l=-l:r===1/0?l-=t:l=(l-t)/(r-t),l=s(l),n===-1/0?l=-l:o===1/0?l+=n:l=l*(o-n)+n,l)}(e,s[t],s[t+1],o[t],o[t+1],c,i,a,n.map)}},Ft=1.70158,Lt=1.525*Ft,Dt=Ft+1,Vt=2*Math.PI/3,Bt=2*Math.PI/4.5,Ut=e=>{const t=7.5625,r=2.75;return e<1/r?t*e*e:e<2/r?t*(e-=1.5/r)*e+.75:e<2.5/r?t*(e-=2.25/r)*e+.9375:t*(e-=2.625/r)*e+.984375},Ht={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>1-(1-e)*(1-e),easeInOutQuad:e=>e<.5?2*e*e:1-Math.pow(-2*e+2,2)/2,easeInCubic:e=>e*e*e,easeOutCubic:e=>1-Math.pow(1-e,3),easeInOutCubic:e=>e<.5?4*e*e*e:1-Math.pow(-2*e+2,3)/2,easeInQuart:e=>e*e*e*e,easeOutQuart:e=>1-Math.pow(1-e,4),easeInOutQuart:e=>e<.5?8*e*e*e*e:1-Math.pow(-2*e+2,4)/2,easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>1-Math.pow(1-e,5),easeInOutQuint:e=>e<.5?16*e*e*e*e*e:1-Math.pow(-2*e+2,5)/2,easeInSine:e=>1-Math.cos(e*Math.PI/2),easeOutSine:e=>Math.sin(e*Math.PI/2),easeInOutSine:e=>-(Math.cos(Math.PI*e)-1)/2,easeInExpo:e=>0===e?0:Math.pow(2,10*e-10),easeOutExpo:e=>1===e?1:1-Math.pow(2,-10*e),easeInOutExpo:e=>0===e?0:1===e?1:e<.5?Math.pow(2,20*e-10)/2:(2-Math.pow(2,-20*e+10))/2,easeInCirc:e=>1-Math.sqrt(1-Math.pow(e,2)),easeOutCirc:e=>Math.sqrt(1-Math.pow(e-1,2)),easeInOutCirc:e=>e<.5?(1-Math.sqrt(1-Math.pow(2*e,2)))/2:(Math.sqrt(1-Math.pow(-2*e+2,2))+1)/2,easeInBack:e=>Dt*e*e*e-Ft*e*e,easeOutBack:e=>1+Dt*Math.pow(e-1,3)+Ft*Math.pow(e-1,2),easeInOutBack:e=>e<.5?Math.pow(2*e,2)*(7.189819*e-Lt)/2:(Math.pow(2*e-2,2)*((Lt+1)*(2*e-2)+Lt)+2)/2,easeInElastic:e=>0===e?0:1===e?1:-Math.pow(2,10*e-10)*Math.sin((10*e-10.75)*Vt),easeOutElastic:e=>0===e?0:1===e?1:Math.pow(2,-10*e)*Math.sin((10*e-.75)*Vt)+1,easeInOutElastic:e=>0===e?0:1===e?1:e<.5?-Math.pow(2,20*e-10)*Math.sin((20*e-11.125)*Bt)/2:Math.pow(2,-20*e+10)*Math.sin((20*e-11.125)*Bt)/2+1,easeInBounce:e=>1-Ut(1-e),easeOutBounce:Ut,easeInOutBounce:e=>e<.5?(1-Ut(1-2*e))/2:(1+Ut(2*e-1))/2,steps:(e,t="end")=>r=>{const n=(r="end"===t?Math.min(r,.999):Math.max(r,.001))*e;return o=("end"===t?Math.floor(n):Math.ceil(n))/e,Math.min(Math.max(o,0),1);var o}},zt=Symbol.for("FluidValue.get"),Gt=Symbol.for("FluidValue.observers"),$t=e=>Boolean(e&&e[zt]),Wt=e=>e&&e[zt]?e[zt]():e,qt=e=>e[Gt]||null;function Qt(e,t){const r=e[Gt];r&&r.forEach((e=>{!function(e,t){e.eventObserved?e.eventObserved(t):e(t)}(e,t)}))}var Zt=class{constructor(e){if(!e&&!(e=this.get))throw Error("Unknown getter");Yt(this,e)}},Yt=(e,t)=>er(e,zt,t);function Kt(e,t){if(e[zt]){let r=e[Gt];r||er(e,Gt,r=new Set),r.has(t)||(r.add(t),e.observerAdded&&e.observerAdded(r.size,t))}return t}function Jt(e,t){const r=e[Gt];if(r&&r.has(t)){const n=r.size-1;n?r.delete(t):e[Gt]=null,e.observerRemoved&&e.observerRemoved(n,t)}}var Xt,er=(e,t,r)=>Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0}),tr=/[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,rr=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi,nr=new RegExp(`(${tr.source})(%|[a-z]+)`,"i"),or=/rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi,sr=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/,ir=e=>{const[t,r]=ar(e);if(!t||nt())return e;const n=window.getComputedStyle(document.documentElement).getPropertyValue(t);if(n)return n.trim();if(r&&r.startsWith("--")){return window.getComputedStyle(document.documentElement).getPropertyValue(r)||e}return r&&sr.test(r)?ir(r):r||e},ar=e=>{const t=sr.exec(e);if(!t)return[,];const[,r,n]=t;return[r,n]},cr=(e,t,r,n,o)=>`rgba(${Math.round(t)}, ${Math.round(r)}, ${Math.round(n)}, ${o})`,lr=e=>{Xt||(Xt=ot?new RegExp(`(${Object.keys(ot).join("|")})(?!\\w)`,"g"):/^\b$/);const t=e.output.map((e=>Wt(e).replace(sr,ir).replace(rr,Rt).replace(Xt,Rt))),r=t.map((e=>e.match(tr).map(Number))),n=r[0].map(((e,t)=>r.map((e=>{if(!(t in e))throw Error('The arity of each "output" value must be equal');return e[t]})))).map((t=>Mt({...e,output:t})));return e=>{const r=!nr.test(t[0])&&t.find((e=>nr.test(e)))?.replace(tr,"");let o=0;return t[0].replace(tr,(()=>`${n[o++](e)}${r||""}`)).replace(or,cr)}},ur="react-spring: ",dr=e=>{const t=e;let r=!1;if("function"!=typeof t)throw new TypeError(`${ur}once requires a function parameter`);return(...e)=>{r||(t(...e),r=!0)}},pr=dr(console.warn),mr=dr(console.warn);function fr(e){return Qe.str(e)&&("#"==e[0]||/\d/.test(e)||!nt()&&sr.test(e)||e in(ot||{}))}var hr=nt()?re.useEffect:re.useLayoutEffect;function yr(){const e=(0,re.useState)()[1],t=(()=>{const e=(0,re.useRef)(!1);return hr((()=>(e.current=!0,()=>{e.current=!1})),[]),e})();return()=>{t.current&&e(Math.random())}}var gr=e=>(0,re.useEffect)(e,wr),wr=[],vr=Symbol.for("Animated:node"),br=e=>e&&e[vr],_r=(e,t)=>{return r=e,n=vr,o=t,Object.defineProperty(r,n,{value:o,writable:!0,configurable:!0});var r,n,o},xr=e=>e&&e[vr]&&e[vr].getPayload(),Sr=class{constructor(){_r(this,this)}getPayload(){return this.payload||[]}},Cr=class extends Sr{constructor(e){super(),this._value=e,this.done=!0,this.durationProgress=0,Qe.num(this._value)&&(this.lastPosition=this._value)}static create(e){return new Cr(e)}getPayload(){return[this]}getValue(){return this._value}setValue(e,t){return Qe.num(e)&&(this.lastPosition=e,t&&(e=Math.round(e/t)*t,this.done&&(this.lastPosition=e))),this._value!==e&&(this._value=e,!0)}reset(){const{done:e}=this;this.done=!1,Qe.num(this._value)&&(this.elapsedTime=0,this.durationProgress=0,this.lastPosition=this._value,e&&(this.lastVelocity=null),this.v0=null)}},kr=class extends Cr{constructor(e){super(0),this._string=null,this._toString=Mt({output:[e,e]})}static create(e){return new kr(e)}getValue(){const e=this._string;return null==e?this._string=this._toString(this._value):e}setValue(e){if(Qe.str(e)){if(e==this._string)return!1;this._string=e,this._value=1}else{if(!super.setValue(e))return!1;this._string=null}return!0}reset(e){e&&(this._toString=Mt({output:[this.getValue(),e]})),this._value=0,super.reset()}},jr={dependencies:null},Er=class extends Sr{constructor(e){super(),this.source=e,this.setValue(e)}getValue(e){const t={};return Ke(this.source,((r,n)=>{var o;(o=r)&&o[vr]===o?t[n]=r.getValue(e):$t(r)?t[n]=Wt(r):e||(t[n]=r)})),t}setValue(e){this.source=e,this.payload=this._makePayload(e)}reset(){this.payload&&Ye(this.payload,(e=>e.reset()))}_makePayload(e){if(e){const t=new Set;return Ke(e,this._addToPayload,t),Array.from(t)}}_addToPayload(e){jr.dependencies&&$t(e)&&jr.dependencies.add(e);const t=xr(e);t&&Ye(t,(e=>this.add(e)))}},Ar=class extends Er{constructor(e){super(e)}static create(e){return new Ar(e)}getValue(){return this.source.map((e=>e.getValue()))}setValue(e){const t=this.getPayload();return e.length==t.length?t.map(((t,r)=>t.setValue(e[r]))).some(Boolean):(super.setValue(e.map(Pr)),!0)}};function Pr(e){return(fr(e)?kr:Cr).create(e)}function Or(e){const t=br(e);return t?t.constructor:Qe.arr(e)?Ar:fr(e)?kr:Cr}var Tr=(e,t)=>{const r=!Qe.fun(e)||e.prototype&&e.prototype.isReactComponent;return(0,re.forwardRef)(((n,o)=>{const s=(0,re.useRef)(null),i=r&&(0,re.useCallback)((e=>{s.current=function(e,t){return e&&(Qe.fun(e)?e(t):e.current=t),t}(o,e)}),[o]),[a,c]=function(e,t){const r=new Set;return jr.dependencies=r,e.style&&(e={...e,style:t.createAnimatedStyle(e.style)}),e=new Er(e),jr.dependencies=null,[e,r]}(n,t),l=yr(),u=()=>{const e=s.current;r&&!e||!1===(!!e&&t.applyAnimatedValues(e,a.getValue(!0)))&&l()},d=new Nr(u,c),p=(0,re.useRef)();hr((()=>(p.current=d,Ye(c,(e=>Kt(e,d))),()=>{p.current&&(Ye(p.current.deps,(e=>Jt(e,p.current))),Oe.cancel(p.current.update))}))),(0,re.useEffect)(u,[]),gr((()=>()=>{const e=p.current;Ye(e.deps,(t=>Jt(t,e)))}));const m=t.getComponentProps(a.getValue());return re.createElement(e,{...m,ref:i})}))},Nr=class{constructor(e,t){this.update=e,this.deps=t}eventObserved(e){"change"==e.type&&Oe.write(this.update)}},Ir=Symbol.for("AnimatedComponent"),Rr=e=>Qe.str(e)?e:e&&Qe.str(e.displayName)?e.displayName:Qe.fun(e)&&e.name||null;function Mr(e,...t){return Qe.fun(e)?e(...t):e}var Fr=(e,t)=>!0===e||!!(t&&e&&(Qe.fun(e)?e(t):Je(e).includes(t))),Lr=(e,t)=>Qe.obj(e)?t&&e[t]:e,Dr=(e,t)=>!0===e.default?e[t]:e.default?e.default[t]:void 0,Vr=e=>e,Br=(e,t=Vr)=>{let r=Ur;e.default&&!0!==e.default&&(e=e.default,r=Object.keys(e));const n={};for(const o of r){const r=t(e[o],o);Qe.und(r)||(n[o]=r)}return n},Ur=["config","onProps","onStart","onChange","onPause","onResume","onRest"],Hr={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function zr(e){const t=function(e){const t={};let r=0;if(Ke(e,((e,n)=>{Hr[n]||(t[n]=e,r++)})),r)return t}(e);if(t){const r={to:t};return Ke(e,((e,n)=>n in t||(r[n]=e))),r}return{...e}}function Gr(e){return e=Wt(e),Qe.arr(e)?e.map(Gr):fr(e)?Ae.createStringInterpolator({range:[0,1],output:[e,e]})(1):e}function $r(e){return Qe.fun(e)||Qe.arr(e)&&Qe.obj(e[0])}function Wr(e,t){e.ref?.delete(e),t?.delete(e)}var qr={tension:170,friction:26,mass:1,damping:1,easing:Ht.linear,clamp:!1},Qr=class{constructor(){this.velocity=0,Object.assign(this,qr)}};function Zr(e,t){if(Qe.und(t.decay)){const r=!Qe.und(t.tension)||!Qe.und(t.friction);!r&&Qe.und(t.frequency)&&Qe.und(t.damping)&&Qe.und(t.mass)||(e.duration=void 0,e.decay=void 0),r&&(e.frequency=void 0)}else e.duration=void 0}var Yr=[],Kr=class{constructor(){this.changed=!1,this.values=Yr,this.toValues=null,this.fromValues=Yr,this.config=new Qr,this.immediate=!1}};function Jr(e,{key:t,props:r,defaultProps:n,state:o,actions:s}){return new Promise(((i,a)=>{let c,l,u=Fr(r.cancel??n?.cancel,t);if(u)m();else{Qe.und(r.pause)||(o.paused=Fr(r.pause,t));let e=n?.pause;!0!==e&&(e=o.paused||Fr(e,t)),c=Mr(r.delay||0,t),e?(o.resumeQueue.add(p),s.pause()):(s.resume(),p())}function d(){o.resumeQueue.add(p),o.timeouts.delete(l),l.cancel(),c=l.time-Oe.now()}function p(){c>0&&!Ae.skipAnimation?(o.delayed=!0,l=Oe.setTimeout(m,c),o.pauseQueue.add(d),o.timeouts.add(l)):m()}function m(){o.delayed&&(o.delayed=!1),o.pauseQueue.delete(d),o.timeouts.delete(l),e<=(o.cancelId||0)&&(u=!0);try{s.start({...r,callId:e,cancel:u},i)}catch(e){a(e)}}}))}var Xr=(e,t)=>1==t.length?t[0]:t.some((e=>e.cancelled))?rn(e.get()):t.every((e=>e.noop))?en(e.get()):tn(e.get(),t.every((e=>e.finished))),en=e=>({value:e,noop:!0,finished:!0,cancelled:!1}),tn=(e,t,r=!1)=>({value:e,finished:t,cancelled:r}),rn=e=>({value:e,cancelled:!0,finished:!1});function nn(e,t,r,n){const{callId:o,parentId:s,onRest:i}=t,{asyncTo:a,promise:c}=r;return s||e!==a||t.reset?r.promise=(async()=>{r.asyncId=o,r.asyncTo=e;const l=Br(t,((e,t)=>"onRest"===t?void 0:e));let u,d;const p=new Promise(((e,t)=>(u=e,d=t))),m=e=>{const t=o<=(r.cancelId||0)&&rn(n)||o!==r.asyncId&&tn(n,!1);if(t)throw e.result=t,d(e),e},f=(e,t)=>{const s=new sn,i=new an;return(async()=>{if(Ae.skipAnimation)throw on(r),i.result=tn(n,!1),d(i),i;m(s);const a=Qe.obj(e)?{...e}:{...t,to:e};a.parentId=o,Ke(l,((e,t)=>{Qe.und(a[t])&&(a[t]=e)}));const c=await n.start(a);return m(s),r.paused&&await new Promise((e=>{r.resumeQueue.add(e)})),c})()};let h;if(Ae.skipAnimation)return on(r),tn(n,!1);try{let t;t=Qe.arr(e)?(async e=>{for(const t of e)await f(t)})(e):Promise.resolve(e(f,n.stop.bind(n))),await Promise.all([t.then(u),p]),h=tn(n.get(),!0,!1)}catch(e){if(e instanceof sn)h=e.result;else{if(!(e instanceof an))throw e;h=e.result}}finally{o==r.asyncId&&(r.asyncId=s,r.asyncTo=s?a:void 0,r.promise=s?c:void 0)}return Qe.fun(i)&&Oe.batchedUpdates((()=>{i(h,n,n.item)})),h})():c}function on(e,t){Xe(e.timeouts,(e=>e.cancel())),e.pauseQueue.clear(),e.resumeQueue.clear(),e.asyncId=e.asyncTo=e.promise=void 0,t&&(e.cancelId=t)}var sn=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},an=class extends Error{constructor(){super("SkipAnimationSignal")}},cn=e=>e instanceof un,ln=1,un=class extends Zt{constructor(){super(...arguments),this.id=ln++,this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){const e=br(this);return e&&e.getValue()}to(...e){return Ae.to(this,e)}interpolate(...e){return pr(`${ur}The "interpolate" function is deprecated in v9 (use "to" instead)`),Ae.to(this,e)}toJSON(){return this.get()}observerAdded(e){1==e&&this._attach()}observerRemoved(e){0==e&&this._detach()}_attach(){}_detach(){}_onChange(e,t=!1){Qt(this,{type:"change",parent:this,value:e,idle:t})}_onPriorityChange(e){this.idle||pt.sort(this),Qt(this,{type:"priority",parent:this,priority:e})}},dn=Symbol.for("SpringPhase"),pn=e=>(1&e[dn])>0,mn=e=>(2&e[dn])>0,fn=e=>(4&e[dn])>0,hn=(e,t)=>t?e[dn]|=3:e[dn]&=-3,yn=(e,t)=>t?e[dn]|=4:e[dn]&=-5,gn=class extends un{constructor(e,t){if(super(),this.animation=new Kr,this.defaultProps={},this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._pendingCalls=new Set,this._lastCallId=0,this._lastToId=0,this._memoizedDuration=0,!Qe.und(e)||!Qe.und(t)){const r=Qe.obj(e)?{...e}:{...t,from:e};Qe.und(r.default)&&(r.default=!0),this.start(r)}}get idle(){return!(mn(this)||this._state.asyncTo)||fn(this)}get goal(){return Wt(this.animation.to)}get velocity(){const e=br(this);return e instanceof Cr?e.lastVelocity||0:e.getPayload().map((e=>e.lastVelocity||0))}get hasAnimated(){return pn(this)}get isAnimating(){return mn(this)}get isPaused(){return fn(this)}get isDelayed(){return this._state.delayed}advance(e){let t=!0,r=!1;const n=this.animation;let{toValues:o}=n;const{config:s}=n,i=xr(n.to);!i&&$t(n.to)&&(o=Je(Wt(n.to))),n.values.forEach(((a,c)=>{if(a.done)return;const l=a.constructor==kr?1:i?i[c].lastPosition:o[c];let u=n.immediate,d=l;if(!u){if(d=a.lastPosition,s.tension<=0)return void(a.done=!0);let t=a.elapsedTime+=e;const r=n.fromValues[c],o=null!=a.v0?a.v0:a.v0=Qe.arr(s.velocity)?s.velocity[c]:s.velocity;let i;const p=s.precision||(r==l?.005:Math.min(1,.001*Math.abs(l-r)));if(Qe.und(s.duration))if(s.decay){const e=!0===s.decay?.998:s.decay,n=Math.exp(-(1-e)*t);d=r+o/(1-e)*(1-n),u=Math.abs(a.lastPosition-d)<=p,i=o*n}else{i=null==a.lastVelocity?o:a.lastVelocity;const t=s.restVelocity||p/10,n=s.clamp?0:s.bounce,c=!Qe.und(n),m=r==l?a.v0>0:r<l;let f,h=!1;const y=1,g=Math.ceil(e/y);for(let e=0;e<g&&(f=Math.abs(i)>t,f||(u=Math.abs(l-d)<=p,!u));++e)c&&(h=d==l||d>l==m,h&&(i=-i*n,d=l)),i+=(1e-6*-s.tension*(d-l)+.001*-s.friction*i)/s.mass*y,d+=i*y}else{let n=1;s.duration>0&&(this._memoizedDuration!==s.duration&&(this._memoizedDuration=s.duration,a.durationProgress>0&&(a.elapsedTime=s.duration*a.durationProgress,t=a.elapsedTime+=e)),n=(s.progress||0)+t/this._memoizedDuration,n=n>1?1:n<0?0:n,a.durationProgress=n),d=r+s.easing(n)*(l-r),i=(d-a.lastPosition)/e,u=1==n}a.lastVelocity=i,Number.isNaN(d)&&(console.warn("Got NaN while animating:",this),u=!0)}i&&!i[c].done&&(u=!1),u?a.done=!0:t=!1,a.setValue(d,s.round)&&(r=!0)}));const a=br(this),c=a.getValue();if(t){const e=Wt(n.to);c===e&&!r||s.decay?r&&s.decay&&this._onChange(c):(a.setValue(e),this._onChange(e)),this._stop()}else r&&this._onChange(c)}set(e){return Oe.batchedUpdates((()=>{this._stop(),this._focus(e),this._set(e)})),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(mn(this)){const{to:e,config:t}=this.animation;Oe.batchedUpdates((()=>{this._onStart(),t.decay||this._set(e,!1),this._stop()}))}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,t){let r;return Qe.und(e)?(r=this.queue||[],this.queue=[]):r=[Qe.obj(e)?e:{...t,to:e}],Promise.all(r.map((e=>this._update(e)))).then((e=>Xr(this,e)))}stop(e){const{to:t}=this.animation;return this._focus(this.get()),on(this._state,e&&this._lastCallId),Oe.batchedUpdates((()=>this._stop(t,e))),this}reset(){this._update({reset:!0})}eventObserved(e){"change"==e.type?this._start():"priority"==e.type&&(this.priority=e.priority+1)}_prepareNode(e){const t=this.key||"";let{to:r,from:n}=e;r=Qe.obj(r)?r[t]:r,(null==r||$r(r))&&(r=void 0),n=Qe.obj(n)?n[t]:n,null==n&&(n=void 0);const o={to:r,from:n};return pn(this)||(e.reverse&&([r,n]=[n,r]),n=Wt(n),Qe.und(n)?br(this)||this._set(r):this._set(n)),o}_update({...e},t){const{key:r,defaultProps:n}=this;e.default&&Object.assign(n,Br(e,((e,t)=>/^on/.test(t)?Lr(e,r):e))),Sn(this,e,"onProps"),Cn(this,"onProps",e,this);const o=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");const s=this._state;return Jr(++this._lastCallId,{key:r,props:e,defaultProps:n,state:s,actions:{pause:()=>{fn(this)||(yn(this,!0),rt(s.pauseQueue),Cn(this,"onPause",tn(this,wn(this,this.animation.to)),this))},resume:()=>{fn(this)&&(yn(this,!1),mn(this)&&this._resume(),rt(s.resumeQueue),Cn(this,"onResume",tn(this,wn(this,this.animation.to)),this))},start:this._merge.bind(this,o)}}).then((r=>{if(e.loop&&r.finished&&(!t||!r.noop)){const t=vn(e);if(t)return this._update(t,!0)}return r}))}_merge(e,t,r){if(t.cancel)return this.stop(!0),r(rn(this));const n=!Qe.und(e.to),o=!Qe.und(e.from);if(n||o){if(!(t.callId>this._lastToId))return r(rn(this));this._lastToId=t.callId}const{key:s,defaultProps:i,animation:a}=this,{to:c,from:l}=a;let{to:u=c,from:d=l}=e;!o||n||t.default&&!Qe.und(u)||(u=d),t.reverse&&([u,d]=[d,u]);const p=!Ze(d,l);p&&(a.from=d),d=Wt(d);const m=!Ze(u,c);m&&this._focus(u);const f=$r(t.to),{config:h}=a,{decay:y,velocity:g}=h;(n||o)&&(h.velocity=0),t.config&&!f&&function(e,t,r){r&&(Zr(r={...r},t),t={...r,...t}),Zr(e,t),Object.assign(e,t);for(const t in qr)null==e[t]&&(e[t]=qr[t]);let{frequency:n,damping:o}=e;const{mass:s}=e;Qe.und(n)||(n<.01&&(n=.01),o<0&&(o=0),e.tension=Math.pow(2*Math.PI/n,2)*s,e.friction=4*Math.PI*o*s/n)}(h,Mr(t.config,s),t.config!==i.config?Mr(i.config,s):void 0);let w=br(this);if(!w||Qe.und(u))return r(tn(this,!0));const v=Qe.und(t.reset)?o&&!t.default:!Qe.und(d)&&Fr(t.reset,s),b=v?d:this.get(),_=Gr(u),x=Qe.num(_)||Qe.arr(_)||fr(_),S=!f&&(!x||Fr(i.immediate||t.immediate,s));if(m){const e=Or(u);if(e!==w.constructor){if(!S)throw Error(`Cannot animate between ${w.constructor.name} and ${e.name}, as the "to" prop suggests`);w=this._set(_)}}const C=w.constructor;let k=$t(u),j=!1;if(!k){const e=v||!pn(this)&&p;(m||e)&&(j=Ze(Gr(b),_),k=!j),(Ze(a.immediate,S)||S)&&Ze(h.decay,y)&&Ze(h.velocity,g)||(k=!0)}if(j&&mn(this)&&(a.changed&&!v?k=!0:k||this._stop(c)),!f&&((k||$t(c))&&(a.values=w.getPayload(),a.toValues=$t(u)?null:C==kr?[1]:Je(_)),a.immediate!=S&&(a.immediate=S,S||v||this._set(c)),k)){const{onRest:e}=a;Ye(xn,(e=>Sn(this,t,e)));const n=tn(this,wn(this,c));rt(this._pendingCalls,n),this._pendingCalls.add(r),a.changed&&Oe.batchedUpdates((()=>{a.changed=!v,e?.(n,this),v?Mr(i.onRest,n):a.onStart?.(n,this)}))}v&&this._set(b),f?r(nn(t.to,t,this._state,this)):k?this._start():mn(this)&&!m?this._pendingCalls.add(r):r(en(b))}_focus(e){const t=this.animation;e!==t.to&&(qt(this)&&this._detach(),t.to=e,qt(this)&&this._attach())}_attach(){let e=0;const{to:t}=this.animation;$t(t)&&(Kt(t,this),cn(t)&&(e=t.priority+1)),this.priority=e}_detach(){const{to:e}=this.animation;$t(e)&&Jt(e,this)}_set(e,t=!0){const r=Wt(e);if(!Qe.und(r)){const e=br(this);if(!e||!Ze(r,e.getValue())){const n=Or(r);e&&e.constructor==n?e.setValue(r):_r(this,n.create(r)),e&&Oe.batchedUpdates((()=>{this._onChange(r,t)}))}}return br(this)}_onStart(){const e=this.animation;e.changed||(e.changed=!0,Cn(this,"onStart",tn(this,wn(this,e.to)),this))}_onChange(e,t){t||(this._onStart(),Mr(this.animation.onChange,e,this)),Mr(this.defaultProps.onChange,e,this),super._onChange(e,t)}_start(){const e=this.animation;br(this).reset(Wt(e.to)),e.immediate||(e.fromValues=e.values.map((e=>e.lastPosition))),mn(this)||(hn(this,!0),fn(this)||this._resume())}_resume(){Ae.skipAnimation?this.finish():pt.start(this)}_stop(e,t){if(mn(this)){hn(this,!1);const r=this.animation;Ye(r.values,(e=>{e.done=!0})),r.toValues&&(r.onChange=r.onPause=r.onResume=void 0),Qt(this,{type:"idle",parent:this});const n=t?rn(this.get()):tn(this.get(),wn(this,e??r.to));rt(this._pendingCalls,n),r.changed&&(r.changed=!1,Cn(this,"onRest",n,this))}}};function wn(e,t){const r=Gr(t);return Ze(Gr(e.get()),r)}function vn(e,t=e.loop,r=e.to){const n=Mr(t);if(n){const o=!0!==n&&zr(n),s=(o||e).reverse,i=!o||o.reset;return bn({...e,loop:t,default:!1,pause:void 0,to:!s||$r(r)?r:void 0,from:i?e.from:void 0,reset:i,...o})}}function bn(e){const{to:t,from:r}=e=zr(e),n=new Set;return Qe.obj(t)&&_n(t,n),Qe.obj(r)&&_n(r,n),e.keys=n.size?Array.from(n):null,e}function _n(e,t){Ke(e,((e,r)=>null!=e&&t.add(r)))}var xn=["onStart","onRest","onChange","onPause","onResume"];function Sn(e,t,r){e.animation[r]=t[r]!==Dr(t,r)?Lr(t[r],e.key):void 0}function Cn(e,t,...r){e.animation[t]?.(...r),e.defaultProps[t]?.(...r)}var kn=["onStart","onChange","onRest"],jn=1,En=class{constructor(e,t){this.id=jn++,this.springs={},this.queue=[],this._lastAsyncId=0,this._active=new Set,this._changed=new Set,this._started=!1,this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._events={onStart:new Map,onChange:new Map,onRest:new Map},this._onFrame=this._onFrame.bind(this),t&&(this._flush=t),e&&this.start({default:!0,...e})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every((e=>e.idle&&!e.isDelayed&&!e.isPaused))}get item(){return this._item}set item(e){this._item=e}get(){const e={};return this.each(((t,r)=>e[r]=t.get())),e}set(e){for(const t in e){const r=e[t];Qe.und(r)||this.springs[t].set(r)}}update(e){return e&&this.queue.push(bn(e)),this}start(e){let{queue:t}=this;return e?t=Je(e).map(bn):this.queue=[],this._flush?this._flush(this,t):(Nn(this,t),function(e,t){return Promise.all(t.map((t=>An(e,t)))).then((t=>Xr(e,t)))}(this,t))}stop(e,t){if(e!==!!e&&(t=e),t){const r=this.springs;Ye(Je(t),(t=>r[t].stop(!!e)))}else on(this._state,this._lastAsyncId),this.each((t=>t.stop(!!e)));return this}pause(e){if(Qe.und(e))this.start({pause:!0});else{const t=this.springs;Ye(Je(e),(e=>t[e].pause()))}return this}resume(e){if(Qe.und(e))this.start({pause:!1});else{const t=this.springs;Ye(Je(e),(e=>t[e].resume()))}return this}each(e){Ke(this.springs,e)}_onFrame(){const{onStart:e,onChange:t,onRest:r}=this._events,n=this._active.size>0,o=this._changed.size>0;(n&&!this._started||o&&!this._started)&&(this._started=!0,Xe(e,(([e,t])=>{t.value=this.get(),e(t,this,this._item)})));const s=!n&&this._started,i=o||s&&r.size?this.get():null;o&&t.size&&Xe(t,(([e,t])=>{t.value=i,e(t,this,this._item)})),s&&(this._started=!1,Xe(r,(([e,t])=>{t.value=i,e(t,this,this._item)})))}eventObserved(e){if("change"==e.type)this._changed.add(e.parent),e.idle||this._active.add(e.parent);else{if("idle"!=e.type)return;this._active.delete(e.parent)}Oe.onFrame(this._onFrame)}};async function An(e,t,r){const{keys:n,to:o,from:s,loop:i,onRest:a,onResolve:c}=t,l=Qe.obj(t.default)&&t.default;i&&(t.loop=!1),!1===o&&(t.to=null),!1===s&&(t.from=null);const u=Qe.arr(o)||Qe.fun(o)?o:void 0;u?(t.to=void 0,t.onRest=void 0,l&&(l.onRest=void 0)):Ye(kn,(r=>{const n=t[r];if(Qe.fun(n)){const o=e._events[r];t[r]=({finished:e,cancelled:t})=>{const r=o.get(n);r?(e||(r.finished=!1),t&&(r.cancelled=!0)):o.set(n,{value:null,finished:e||!1,cancelled:t||!1})},l&&(l[r]=t[r])}}));const d=e._state;t.pause===!d.paused?(d.paused=t.pause,rt(t.pause?d.pauseQueue:d.resumeQueue)):d.paused&&(t.pause=!0);const p=(n||Object.keys(e.springs)).map((r=>e.springs[r].start(t))),m=!0===t.cancel||!0===Dr(t,"cancel");(u||m&&d.asyncId)&&p.push(Jr(++e._lastAsyncId,{props:t,state:d,actions:{pause:qe,resume:qe,start(t,r){m?(on(d,e._lastAsyncId),r(rn(e))):(t.onRest=a,r(nn(u,t,d,e)))}}})),d.paused&&await new Promise((e=>{d.resumeQueue.add(e)}));const f=Xr(e,await Promise.all(p));if(i&&f.finished&&(!r||!f.noop)){const r=vn(t,i,o);if(r)return Nn(e,[r]),An(e,r,!0)}return c&&Oe.batchedUpdates((()=>c(f,e,e.item))),f}function Pn(e,t){const r={...e.springs};return t&&Ye(Je(t),(e=>{Qe.und(e.keys)&&(e=bn(e)),Qe.obj(e.to)||(e={...e,to:void 0}),Tn(r,e,(e=>On(e)))})),function(e,t){Ke(t,((t,r)=>{e.springs[r]||(e.springs[r]=t,Kt(t,e))}))}(e,r),r}function On(e,t){const r=new gn;return r.key=e,t&&Kt(r,t),r}function Tn(e,t,r){t.keys&&Ye(t.keys,(n=>{(e[n]||(e[n]=r(n)))._prepareNode(t)}))}function Nn(e,t){Ye(t,(t=>{Tn(e.springs,t,(t=>On(t,e)))}))}var In,Rn,Mn=({children:e,...t})=>{const r=(0,re.useContext)(Fn),n=t.pause||!!r.pause,o=t.immediate||!!r.immediate;t=function(e,t){const[r]=(0,re.useState)((()=>({inputs:t,result:e()}))),n=(0,re.useRef)(),o=n.current;let s=o;return s?Boolean(t&&s.inputs&&function(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(t,s.inputs))||(s={inputs:t,result:e()}):s=r,(0,re.useEffect)((()=>{n.current=s,o==r&&(r.inputs=r.result=void 0)}),[s]),s.result}((()=>({pause:n,immediate:o})),[n,o]);const{Provider:s}=Fn;return re.createElement(s,{value:t},e)},Fn=(In=Mn,Rn={},Object.assign(In,re.createContext(Rn)),In.Provider._context=In,In.Consumer._context=In,In);Mn.Provider=Fn.Provider,Mn.Consumer=Fn.Consumer;var Ln=()=>{const e=[],t=function(t){mr(`${ur}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`);const n=[];return Ye(e,((e,o)=>{if(Qe.und(t))n.push(e.start());else{const s=r(t,e,o);s&&n.push(e.start(s))}})),n};t.current=e,t.add=function(t){e.includes(t)||e.push(t)},t.delete=function(t){const r=e.indexOf(t);~r&&e.splice(r,1)},t.pause=function(){return Ye(e,(e=>e.pause(...arguments))),this},t.resume=function(){return Ye(e,(e=>e.resume(...arguments))),this},t.set=function(t){Ye(e,((e,r)=>{const n=Qe.fun(t)?t(r,e):t;n&&e.set(n)}))},t.start=function(t){const r=[];return Ye(e,((e,n)=>{if(Qe.und(t))r.push(e.start());else{const o=this._getProps(t,e,n);o&&r.push(e.start(o))}})),r},t.stop=function(){return Ye(e,(e=>e.stop(...arguments))),this},t.update=function(t){return Ye(e,((e,r)=>e.update(this._getProps(t,e,r)))),this};const r=function(e,t,r){return Qe.fun(e)?e(r,t):e};return t._getProps=r,t};var Dn=1,Vn=class extends un{constructor(e,t){super(),this.source=e,this.idle=!0,this._active=new Set,this.calc=Mt(...t);const r=this._get(),n=Or(r);_r(this,n.create(r))}advance(e){const t=this._get();Ze(t,this.get())||(br(this).setValue(t),this._onChange(t,this.idle)),!this.idle&&Un(this._active)&&Hn(this)}_get(){const e=Qe.arr(this.source)?this.source.map(Wt):Je(Wt(this.source));return this.calc(...e)}_start(){this.idle&&!Un(this._active)&&(this.idle=!1,Ye(xr(this),(e=>{e.done=!1})),Ae.skipAnimation?(Oe.batchedUpdates((()=>this.advance())),Hn(this)):pt.start(this))}_attach(){let e=1;Ye(Je(this.source),(t=>{$t(t)&&Kt(t,this),cn(t)&&(t.idle||this._active.add(t),e=Math.max(e,t.priority+1))})),this.priority=e,this._start()}_detach(){Ye(Je(this.source),(e=>{$t(e)&&Jt(e,this)})),this._active.clear(),Hn(this)}eventObserved(e){"change"==e.type?e.idle?this.advance():(this._active.add(e.parent),this._start()):"idle"==e.type?this._active.delete(e.parent):"priority"==e.type&&(this.priority=Je(this.source).reduce(((e,t)=>Math.max(e,(cn(t)?t.priority:0)+1)),0))}};function Bn(e){return!1!==e.idle}function Un(e){return!e.size||Array.from(e).every(Bn)}function Hn(e){e.idle||(e.idle=!0,Ye(xr(e),(e=>{e.done=!0})),Qt(e,{type:"idle",parent:e}))}Ae.assign({createStringInterpolator:lr,to:(e,t)=>new Vn(e,t)}),pt.advance;var zn=o(75795),Gn=/^--/;function $n(e,t){return null==t||"boolean"==typeof t||""===t?"":"number"!=typeof t||0===t||Gn.test(e)||qn.hasOwnProperty(e)&&qn[e]?(""+t).trim():t+"px"}var Wn={},qn={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Qn=["Webkit","Ms","Moz","O"];qn=Object.keys(qn).reduce(((e,t)=>(Qn.forEach((r=>e[((e,t)=>e+t.charAt(0).toUpperCase()+t.substring(1))(r,t)]=e[t])),e)),qn);var Zn=/^(matrix|translate|scale|rotate|skew)/,Yn=/^(translate)/,Kn=/^(rotate|skew)/,Jn=(e,t)=>Qe.num(e)&&0!==e?e+t:e,Xn=(e,t)=>Qe.arr(e)?e.every((e=>Xn(e,t))):Qe.num(e)?e===t:parseFloat(e)===t,eo=class extends Er{constructor({x:e,y:t,z:r,...n}){const o=[],s=[];(e||t||r)&&(o.push([e||0,t||0,r||0]),s.push((e=>[`translate3d(${e.map((e=>Jn(e,"px"))).join(",")})`,Xn(e,0)]))),Ke(n,((e,t)=>{if("transform"===t)o.push([e||""]),s.push((e=>[e,""===e]));else if(Zn.test(t)){if(delete n[t],Qe.und(e))return;const r=Yn.test(t)?"px":Kn.test(t)?"deg":"";o.push(Je(e)),s.push("rotate3d"===t?([e,t,n,o])=>[`rotate3d(${e},${t},${n},${Jn(o,r)})`,Xn(o,0)]:e=>[`${t}(${e.map((e=>Jn(e,r))).join(",")})`,Xn(e,t.startsWith("scale")?1:0)])}})),o.length&&(n.transform=new to(o,s)),super(n)}},to=class extends Zt{constructor(e,t){super(),this.inputs=e,this.transforms=t,this._value=null}get(){return this._value||(this._value=this._get())}_get(){let e="",t=!0;return Ye(this.inputs,((r,n)=>{const o=Wt(r[0]),[s,i]=this.transforms[n](Qe.arr(o)?o:r.map(Wt));e+=" "+s,t=t&&i})),t?"none":e}observerAdded(e){1==e&&Ye(this.inputs,(e=>Ye(e,(e=>$t(e)&&Kt(e,this)))))}observerRemoved(e){0==e&&Ye(this.inputs,(e=>Ye(e,(e=>$t(e)&&Jt(e,this)))))}eventObserved(e){"change"==e.type&&(this._value=null),Qt(this,e)}};Ae.assign({batchedUpdates:zn.unstable_batchedUpdates,createStringInterpolator:lr,colors:{transparent:0,aliceblue:4042850303,antiquewhite:4209760255,aqua:16777215,aquamarine:2147472639,azure:4043309055,beige:4126530815,bisque:4293182719,black:255,blanchedalmond:4293643775,blue:65535,blueviolet:2318131967,brown:2771004159,burlywood:3736635391,burntsienna:3934150143,cadetblue:1604231423,chartreuse:2147418367,chocolate:3530104575,coral:4286533887,cornflowerblue:1687547391,cornsilk:4294499583,crimson:3692313855,cyan:16777215,darkblue:35839,darkcyan:9145343,darkgoldenrod:3095792639,darkgray:2846468607,darkgreen:6553855,darkgrey:2846468607,darkkhaki:3182914559,darkmagenta:2332068863,darkolivegreen:1433087999,darkorange:4287365375,darkorchid:2570243327,darkred:2332033279,darksalmon:3918953215,darkseagreen:2411499519,darkslateblue:1211993087,darkslategray:793726975,darkslategrey:793726975,darkturquoise:13554175,darkviolet:2483082239,deeppink:4279538687,deepskyblue:12582911,dimgray:1768516095,dimgrey:1768516095,dodgerblue:512819199,firebrick:2988581631,floralwhite:4294635775,forestgreen:579543807,fuchsia:4278255615,gainsboro:3705462015,ghostwhite:4177068031,gold:4292280575,goldenrod:3668254975,gray:2155905279,green:8388863,greenyellow:2919182335,grey:2155905279,honeydew:4043305215,hotpink:4285117695,indianred:3445382399,indigo:1258324735,ivory:4294963455,khaki:4041641215,lavender:3873897215,lavenderblush:4293981695,lawngreen:2096890111,lemonchiffon:4294626815,lightblue:2916673279,lightcoral:4034953471,lightcyan:3774873599,lightgoldenrodyellow:4210742015,lightgray:3553874943,lightgreen:2431553791,lightgrey:3553874943,lightpink:4290167295,lightsalmon:4288707327,lightseagreen:548580095,lightskyblue:2278488831,lightslategray:2005441023,lightslategrey:2005441023,lightsteelblue:2965692159,lightyellow:4294959359,lime:16711935,limegreen:852308735,linen:4210091775,magenta:4278255615,maroon:2147483903,mediumaquamarine:1724754687,mediumblue:52735,mediumorchid:3126187007,mediumpurple:2473647103,mediumseagreen:1018393087,mediumslateblue:2070474495,mediumspringgreen:16423679,mediumturquoise:1221709055,mediumvioletred:3340076543,midnightblue:421097727,mintcream:4127193855,mistyrose:4293190143,moccasin:4293178879,navajowhite:4292783615,navy:33023,oldlace:4260751103,olive:2155872511,olivedrab:1804477439,orange:4289003775,orangered:4282712319,orchid:3664828159,palegoldenrod:4008225535,palegreen:2566625535,paleturquoise:2951671551,palevioletred:3681588223,papayawhip:4293907967,peachpuff:4292524543,peru:3448061951,pink:4290825215,plum:3718307327,powderblue:2967529215,purple:2147516671,rebeccapurple:1714657791,red:4278190335,rosybrown:3163525119,royalblue:1097458175,saddlebrown:2336560127,salmon:4202722047,sandybrown:4104413439,seagreen:780883967,seashell:4294307583,sienna:2689740287,silver:3233857791,skyblue:2278484991,slateblue:1784335871,slategray:1887473919,slategrey:1887473919,snow:4294638335,springgreen:16744447,steelblue:1182971135,tan:3535047935,teal:8421631,thistle:3636451583,tomato:4284696575,turquoise:1088475391,violet:4001558271,wheat:4125012991,white:4294967295,whitesmoke:4126537215,yellow:4294902015,yellowgreen:2597139199}});var ro=((e,{applyAnimatedValues:t=()=>!1,createAnimatedStyle:r=e=>new Er(e),getComponentProps:n=e=>e}={})=>{const o={applyAnimatedValues:t,createAnimatedStyle:r,getComponentProps:n},s=e=>{const t=Rr(e)||"Anonymous";return(e=Qe.str(e)?s[e]||(s[e]=Tr(e,o)):e[Ir]||(e[Ir]=Tr(e,o))).displayName=`Animated(${t})`,e};return Ke(e,((t,r)=>{Qe.arr(e)&&(r=Rr(t)),s[r]=s(t)})),{animated:s}})(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],{applyAnimatedValues:function(e,t){if(!e.nodeType||!e.setAttribute)return!1;const r="filter"===e.nodeName||e.parentNode&&"filter"===e.parentNode.nodeName,{style:n,children:o,scrollTop:s,scrollLeft:i,viewBox:a,...c}=t,l=Object.values(c),u=Object.keys(c).map((t=>r||e.hasAttribute(t)?t:Wn[t]||(Wn[t]=t.replace(/([A-Z])/g,(e=>"-"+e.toLowerCase())))));void 0!==o&&(e.textContent=o);for(const t in n)if(n.hasOwnProperty(t)){const r=$n(t,n[t]);Gn.test(t)?e.style.setProperty(t,r):e.style[t]=r}u.forEach(((t,r)=>{e.setAttribute(t,l[r])})),void 0!==s&&(e.scrollTop=s),void 0!==i&&(e.scrollLeft=i),void 0!==a&&e.setAttribute("viewBox",a)},createAnimatedStyle:e=>new eo(e),getComponentProps:({scrollTop:e,scrollLeft:t,...r})=>r}),no=ro.animated;const oo=window.wp.a11y,so=window.wp.warning;var io=o.n(so);const ao=(0,i.forwardRef)((function({className:e,children:t,spokenMessage:r=t,politeness:n="polite",actions:o=[],onRemove:s=E.noop,icon:a=null,explicitDismiss:c=!1,onDismiss:u=null,__unstableHTML:d=!1},p){function m(e){e&&e.preventDefault&&e.preventDefault(),u(),s()}u=u||E.noop,function(e,t){const r="string"==typeof e?e:(0,i.renderToString)(e);(0,i.useEffect)((()=>{r&&(0,oo.speak)(r,t)}),[r,t])}(r,n),(0,i.useEffect)((()=>{const e=setTimeout((()=>{c||(u(),s())}),1e4);return()=>clearTimeout(e)}),[c,u,s]);const f=(0,z.A)(e,"components-snackbar",{"components-snackbar-explicit-dismiss":!!c});o&&o.length>1&&(!0===globalThis.SCRIPT_DEBUG&&io()("Snackbar can only have 1 action, use Notice if your message require many messages"),o=[o[0]]);const h=(0,z.A)("components-snackbar__content",{"components-snackbar__content-with-icon":!!a});return!0===d&&(t=(0,l.jsx)(i.RawHTML,{children:t})),(0,l.jsx)("div",{ref:p,className:f,onClick:c?E.noop:m,tabIndex:"0",role:c?"":"button",onKeyPress:c?E.noop:m,"aria-label":c?"":(0,N.__)("Dismiss this notice","woocommerce"),children:(0,l.jsxs)("div",{className:h,children:[a&&(0,l.jsx)("div",{className:"components-snackbar__icon",children:a}),t,o.map((({label:e,onClick:t,url:r},n)=>(0,l.jsx)(A.Button,{href:r,isTertiary:!0,onClick:e=>function(e,t){e.stopPropagation(),s(),t&&t(e)}(e,t),className:"components-snackbar__action",children:e},n))),c&&(0,l.jsx)("span",{role:"button","aria-label":"Dismiss this notice",tabIndex:"0",className:"components-snackbar__dismiss-button",onClick:m,onKeyPress:m,children:"✕"})]})})})),co=function({notices:e,className:t,children:r,onRemove:n=E.noop,onRemove2:o=E.noop}){const s=(0,j.useReducedMotion)(),[a]=(0,i.useState)((()=>new WeakMap)),c=function(e,t,r){const n=Qe.fun(t)&&t,{reset:o,sort:s,trail:i=0,expires:a=!0,exitBeforeEnter:c=!1,onDestroyed:l,ref:u,config:d}=n?n():t,p=(0,re.useMemo)((()=>n||3==arguments.length?Ln():void 0),[]),m=Je(e),f=[],h=(0,re.useRef)(null),y=o?null:h.current;hr((()=>{h.current=f})),gr((()=>(Ye(f,(e=>{p?.add(e.ctrl),e.ctrl.ref=p})),()=>{Ye(h.current,(e=>{e.expired&&clearTimeout(e.expirationId),Wr(e.ctrl,p),e.ctrl.stop(!0)}))})));const g=function(e,{key:t,keys:r=t},n){if(null===r){const t=new Set;return e.map((e=>{const r=n&&n.find((r=>r.item===e&&"leave"!==r.phase&&!t.has(r)));return r?(t.add(r),r.key):Dn++}))}return Qe.und(r)?e:Qe.fun(r)?e.map(r):Je(r)}(m,n?n():t,y),w=o&&h.current||[];hr((()=>Ye(w,(({ctrl:e,item:t,key:r})=>{Wr(e,p),Mr(l,t,r)}))));const v=[];if(y&&Ye(y,((e,t)=>{e.expired?(clearTimeout(e.expirationId),w.push(e)):~(t=v[t]=g.indexOf(e.key))&&(f[t]=e)})),Ye(m,((e,t)=>{f[t]||(f[t]={key:g[t],item:e,phase:"mount",ctrl:new En},f[t].ctrl.item=e)})),v.length){let e=-1;const{leave:r}=n?n():t;Ye(v,((t,n)=>{const o=y[n];~t?(e=f.indexOf(o),f[e]={...o,item:m[t]}):r&&f.splice(++e,0,o)}))}Qe.fun(s)&&f.sort(((e,t)=>s(e.item,t.item)));let b=-i;const _=yr(),x=Br(t),S=new Map,C=(0,re.useRef)(new Map),k=(0,re.useRef)(!1);Ye(f,((e,r)=>{const o=e.key,s=e.phase,l=n?n():t;let p,m;const f=Mr(l.delay||0,o);if("mount"==s)p=l.enter,m="enter";else{const e=g.indexOf(o)<0;if("leave"!=s)if(e)p=l.leave,m="leave";else{if(!(p=l.update))return;m="update"}else{if(e)return;p=l.enter,m="enter"}}if(p=Mr(p,e.item,r),p=Qe.obj(p)?zr(p):{to:p},!p.config){const t=d||x.config;p.config=Mr(t,e.item,r,m)}b+=i;const w={...x,delay:f+b,ref:u,immediate:l.immediate,reset:!1,...p};if("enter"==m&&Qe.und(w.from)){const o=n?n():t,s=Qe.und(o.initial)||y?o.from:o.initial;w.from=Mr(s,e.item,r)}const{onResolve:v}=w;w.onResolve=e=>{Mr(v,e);const t=h.current,r=t.find((e=>e.key===o));if(r&&(!e.cancelled||"update"==r.phase)&&r.ctrl.idle){const e=t.every((e=>e.ctrl.idle));if("leave"==r.phase){const t=Mr(a,r.item);if(!1!==t){const n=!0===t?0:t;if(r.expired=!0,!e&&n>0)return void(n<=2147483647&&(r.expirationId=setTimeout(_,n)))}}e&&t.some((e=>e.expired))&&(C.current.delete(r),c&&(k.current=!0),_())}};const j=Pn(e.ctrl,w);"leave"===m&&c?C.current.set(e,{phase:m,springs:j,payload:w}):S.set(e,{phase:m,springs:j,payload:w})}));const j=(0,re.useContext)(Mn),E=function(e){const t=(0,re.useRef)();return(0,re.useEffect)((()=>{t.current=e})),t.current}(j),A=j!==E&&function(e){for(const t in e)return!0;return!1}(j);hr((()=>{A&&Ye(f,(e=>{e.ctrl.start({default:j})}))}),[j]),Ye(S,((e,t)=>{if(C.current.size){const e=f.findIndex((e=>e.key===t.key));f.splice(e,1)}})),hr((()=>{Ye(C.current.size?C.current:S,(({phase:e,payload:t},r)=>{const{ctrl:n}=r;r.phase=e,p?.add(n),A&&"enter"==e&&n.start({default:j}),t&&(function(e,t){t&&e.ref!==t&&(e.ref?.delete(e),t.add(e),e.ref=t)}(n,t.ref),!n.ref&&!p||k.current?(n.start(t),k.current&&(k.current=!1)):n.update(t))}))}),o?void 0:r);const P=e=>re.createElement(re.Fragment,null,f.map(((t,r)=>{const{springs:n}=S.get(t)||t.ctrl,o=e({...n},t.item,t,r);return o&&o.type?re.createElement(o.type,{...o.props,key:Qe.str(t.key)||Qe.num(t.key)?t.key:t.ctrl.id,ref:o.ref}):o})));return p?[P,p]:P}(e,{keys:e=>e.id,from:{opacity:0,height:0},enter:e=>async t=>await t({opacity:1,height:a.get(e).offsetHeight}),leave:()=>async e=>{await e({opacity:0}),await e({height:0})},immediate:s});t=(0,z.A)("components-snackbar-list",t);const u=e=>()=>{n(e.id),o(e.id)};return(0,l.jsxs)("div",{className:t,children:[r,c(((e,t)=>(0,l.jsx)(no.div,{style:e,children:(0,l.jsx)("div",{className:"components-snackbar-list__notice-container",ref:e=>e&&a.set(t,e),children:(0,l.jsx)(ao,{...(0,E.omit)(t,["content"]),onRemove:u(t),children:t.content})})})))]})},lo="woocommerce_admin_transient_notices_queue";function uo(e){const{removeNotice:t}=(0,h.useDispatch)("core/notices"),{createNotice:r,removeNotice:n}=(0,h.useDispatch)("core/notices2"),{updateOptions:o}=(0,h.useDispatch)(y.optionsStore),{currentUser:s={},notices:a=[],notices2:c=[],noticesQueue:u={}}=(0,h.useSelect)((e=>({currentUser:e(y.userStore).getCurrentUser(),notices:e("core/notices").getNotices(),notices2:e("core/notices2").getNotices(),noticesQueue:e(y.optionsStore).getOption(lo)})));(0,i.useEffect)((()=>{Object.values(u).filter((e=>e.user_id===s.id||!e.user_id)).forEach((e=>{const t=(0,p.applyFilters)("woocommerce_admin_queued_notice_filter",e);r(t.status,t.content,{onDismiss:()=>{(e=>{const t={...u};delete t[e],o({[lo]:t})})(t.id)},...t.options||{}})}))}),[]);const{className:d}=e,f=(0,z.A)("woocommerce-transient-notices","components-notices__snackbar",d),g=a.concat(c);return(0,l.jsx)(m.WooFooterItem,{children:(0,l.jsx)(co,{notices:g,className:f,onRemove:t,onRemove2:n})})}const po=()=>{const e=(0,D.useSlot)(m.WC_FOOTER_SLOT_NAME),t=Boolean(e?.fills?.length),{atBottom:r}=Se();return t?(0,l.jsx)("div",{className:(0,z.A)("woocommerce-layout__footer",{"at-bottom":r}),children:(0,l.jsx)(m.WooFooterItem.Slot,{})}):null},mo=(0,d.Qk)("dataEndpoints"),fo=(0,j.compose)((0,d.Qk)("preloadOptions")?(0,y.withOptionsHydration)({...(0,d.Qk)("preloadOptions")}):E.identity,(0,y.withPluginsHydration)({...(0,d.Qk)("plugins",{}),jetpackStatus:mo&&mo.jetpackStatus||!1}))((()=>{const e=(0,d.Qk)("embedBreadcrumbs",[]);var t;t={breadcrumbs:e},(0,i.useEffect)((()=>{if(!t.path)return;const e=`woocommerce-admin-page_${"/"===(r=t.path)?"_home":r.replace(/:[a-zA-Z?]+/g,(function(e){return(t=e,t.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))).replace(":","");var t})).replace(/\//g,"_")}`;var r;return document.body.classList.add(e),()=>{document.body.classList.remove(e)}}),[t.path]),(0,i.useEffect)((()=>{const e=document.location.pathname+document.location.search;(0,O.recordPageView)(e,{is_embedded:!0})}),[]);const r=(0,P.getQuery)();return(0,l.jsx)(m.LayoutContextProvider,{value:(0,m.getLayoutContextValue)(["page"]),children:(0,l.jsxs)(A.SlotFillProvider,{children:[(0,l.jsxs)("div",{className:"woocommerce-layout",children:[(0,l.jsx)(je,{sections:(0,E.isFunction)(e)?e({}):e,query:r}),(0,l.jsx)(uo,{}),(0,l.jsx)(po,{}),(0,l.jsx)(s.CustomerEffortScoreModalContainer,{})]}),(0,l.jsx)(T.PluginArea,{scope:"woocommerce-admin"})]})})})),ho=(0,l.jsx)(R.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,l.jsx)(R.Path,{d:"M10.5 4v4h3V4H15v4h1.5a1 1 0 011 1v4l-3 4v2a1 1 0 01-1 1h-3a1 1 0 01-1-1v-2l-3-4V9a1 1 0 011-1H9V4h1.5zm.5 12.5v2h2v-2l3-4v-3H8v3l3 4z"})}),yo="wc/marketing",go="/wc-admin/marketing",wo=window.wp.dataControls,vo={SET_INSTALLED_PLUGINS:"SET_INSTALLED_PLUGINS",SET_ACTIVATING_PLUGIN:"SET_ACTIVATING_PLUGIN",REMOVE_ACTIVATING_PLUGIN:"REMOVE_ACTIVATING_PLUGIN",SET_RECOMMENDED_PLUGINS:"SET_RECOMMENDED_PLUGINS",INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN:"INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN",SET_BLOG_POSTS:"SET_BLOG_POSTS",SET_MISC_RECOMMENDATIONS:"SET_MISC_RECOMMENDATIONS",SET_ERROR:"SET_ERROR"};function bo(e){return{type:vo.SET_INSTALLED_PLUGINS,plugins:e}}function _o(e){return{type:vo.SET_ACTIVATING_PLUGIN,pluginSlug:e}}function xo(e){return{type:vo.REMOVE_ACTIVATING_PLUGIN,pluginSlug:e}}function So(e,t){return{type:vo.SET_RECOMMENDED_PLUGINS,data:{plugins:e,category:t}}}function Co(e){return{type:vo.SET_MISC_RECOMMENDATIONS,data:{miscRecommendations:e}}}function ko(e,t){return{type:vo.SET_BLOG_POSTS,data:{posts:e,category:t}}}function jo(e,t){const{createNotice:r}=(0,h.dispatch)("core/notices");r("error",t),console.log(e)}function Eo(e,t){return{type:vo.SET_ERROR,category:e,error:t}}function*Ao(e){try{const t=yield(0,wo.apiFetch)({path:`${go}/overview/installed-plugins`});if(!t)throw new Error;yield bo(t),yield xo(e)}catch(e){yield jo(e,(0,N.__)("There was an error loading installed extensions.","woocommerce"))}}function*Po(e){const{createNotice:t}=(0,h.dispatch)("core/notices");yield _o(e);try{if(!(yield(0,wo.apiFetch)({path:go+"/overview/activate-plugin",method:"POST",data:{plugin:e}})))throw new Error;yield t("success",(0,N.__)("The extension has been successfully activated.","woocommerce")),yield Ao(e)}catch(t){yield jo(t,(0,N.__)("There was an error trying to activate the extension.","woocommerce")),yield xo(e)}return!0}function*Oo(e,t){return{type:vo.INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN,data:{pluginSlug:e,category:t}}}function To(e){return e.installedPlugins}function No(e){return e.activatingPlugins}function Io(e,t){return e.recommendedPlugins[t]||[]}function Ro(e){return e.miscRecommendations}function Mo(e,t){return e.blogPosts[t]||[]}function Fo(e,t){return e.errors.blogPosts&&e.errors.blogPosts[t]}function*Lo(e){try{const t=yield e?`&category=${e}`:"",r=yield(0,wo.apiFetch)({path:`${go}/recommended?per_page=50${t}`});if(!r)throw new Error;yield So(r,e)}catch(e){yield jo(e,(0,N.__)("There was an error loading recommended extensions.","woocommerce"))}}function*Do(){try{const e=yield(0,wo.apiFetch)({path:`${go}/misc-recommendations`});if(!e)throw new Error;yield Co(e)}catch(e){yield jo(e,(0,N.__)("There was an error loading misc recommendations","woocommerce"))}}function*Vo(e){try{const t=yield e?`?category=${e}`:"",r=yield(0,wo.apiFetch)({path:`${go}/knowledge-base${t}`,method:"GET"});if(!r)throw new Error;yield ko(r,e)}catch(t){yield Eo(e,t)}}const{installedExtensions:Bo}=(0,d.Qk)("marketing",{}),Uo={installedPlugins:Bo,activatingPlugins:[],recommendedPlugins:{},miscRecommendations:[],blogPosts:{},errors:{}},Ho=(0,h.createReduxStore)(yo,{actions:e,selectors:t,resolvers:r,controls:wo.controls,reducer:(e=Uo,t)=>{switch(t.type){case vo.SET_INSTALLED_PLUGINS:return{...e,installedPlugins:t.plugins};case vo.SET_ACTIVATING_PLUGIN:return{...e,activatingPlugins:[...e.activatingPlugins,t.pluginSlug]};case vo.REMOVE_ACTIVATING_PLUGIN:return{...e,activatingPlugins:(0,E.without)(e.activatingPlugins,t.pluginSlug)};case vo.SET_RECOMMENDED_PLUGINS:return{...e,recommendedPlugins:{...e.recommendedPlugins,[t.data.category]:t.data.plugins}};case vo.INSTALL_AND_ACTIVATE_RECOMMENDED_PLUGIN:const r=e.recommendedPlugins[t.data.category]?.filter((e=>e.product!==t.data.pluginSlug));return{...e,recommendedPlugins:{...e.recommendedPlugins,[t.data.category]:r}};case vo.SET_BLOG_POSTS:return{...e,blogPosts:{...e.blogPosts,[t.data.category]:t.data.posts}};case vo.SET_MISC_RECOMMENDATIONS:return{...e,miscRecommendations:t.data.miscRecommendations};case vo.SET_ERROR:return{...e,errors:{...e.errors,blogPosts:{...e.errors.blogPosts,[t.category]:t.error}}};default:return e}}});(0,h.register)(Ho);const zo="order_attribution_install_banner_dismissed",Go="woocommerce_remote_variant_assignment",$o="order-attribution-install-banner",Wo="order-attribution-install-banner-small",qo="order-attribution-install-banner-header";var Qo=o(46772);const Zo=({bannerImage:e=null,bannerType:t="order-attribution-install-banner-big",eventContext:r="analytics-overview",dismissable:n=!1,badgeText:o="",title:s="",description:a="",buttonText:c=""})=>{const[u,d]=(0,i.useState)(!1),{isDismissed:p,dismiss:m,shouldShowBanner:f}=(({isInstalling:e})=>{const{currentUserCan:t}=(0,y.useUser)(),{[zo]:r,updateUserPreferences:n}=(0,y.useUserPreferences)(),{canUserInstallPlugins:o,orderAttributionInstallState:s}=(0,h.useSelect)((e=>{const{getPluginInstallState:r}=e(y.pluginsStore);return{orderAttributionInstallState:r("woocommerce-analytics"),canUserInstallPlugins:t("install_plugins")}}),[t]),{loading:a,isBannerDismissed:c,remoteVariantAssignment:l}=(0,h.useSelect)((e=>{const{getOption:t,hasFinishedResolution:n}=e(y.optionsStore);return{loading:!n("getOption",[Go]),isBannerDismissed:r,remoteVariantAssignment:t(Go)}}),[r]),{loadingRecommendations:u,recommendations:d}=(0,h.useSelect)((e=>{const{getMiscRecommendations:t,hasFinishedResolution:r}=e(yo);return{loadingRecommendations:!o||!r("getMiscRecommendations"),recommendations:o?t():[]}}),[o]),p=(0,i.useMemo)((()=>{if(u||!Array.isArray(d)||0===d.length)return null;for(const e of d)if("woocommerce-analytics"===e.id)return e?.order_attribution_promotion_percentage||null;return null}),[u,d]),m=(0,i.useCallback)((()=>!(!o||a)&&(!!e||!["installed","activated"].includes(s)&&((e,t)=>{if(e=parseInt(e,10),isNaN(e))return!1;const r=(e=>{Array.isArray(e)&&0!==e.length||(e=[["9.7",10],["9.6",10],["9.5",1]]),e.sort(((e,t)=>parseFloat(t[0])-parseFloat(e[0])));for(let[t,r]of e)if((0,ne.isWcVersion)(t,">="))return r=parseInt(r,10),isNaN(r)?12:r/100*120;return 12})(t);return e<=r})(l,p))),[a,o,s,l,p,e]);return{loading:a,isDismissed:"yes"===c,dismiss:(e="analytics-overview")=>{n({[zo]:"yes"}),(0,O.recordEvent)("order_attribution_install_banner_dismissed",{path:(0,P.getPath)(),context:e})},shouldShowBanner:m()}})({isInstalling:u}),{installAndActivatePlugins:g}=(0,h.useDispatch)(y.pluginsStore),w=()=>{d(!0),(0,O.recordEvent)("order_attribution_install_banner_clicked",{path:(0,P.getPath)(),context:r}),g(["woocommerce-analytics"]).then((e=>{window.location.href="admin.php?page=wc-admin&path=/analytics/order-attribution",(0,Qo.R)(e)})).catch((e=>{(0,Qo.R)(e),d(!1)}))},v=(0,i.useCallback)((()=>t===qo?f&&p:n?f&&!p:f),[t,f,p,n])();if((0,i.useEffect)((()=>{v&&(0,O.recordEvent)("order_attribution_install_banner_viewed",{path:(0,P.getPath)(),context:r})}),[r,v]),!v)return null;if(t===qo)return(0,l.jsx)(A.Button,{className:"woocommerce-order-attribution-install-header-banner",variant:"secondary",icon:ho,size:"default",onClick:w,isBusy:u,disabled:u,children:(0,N.__)("Try Order Attribution","woocommerce")});const b=t===Wo;return(0,l.jsx)(A.Card,{size:"medium",className:"woocommerce-order-attribution-install-banner "+(b?"small":""),children:(0,l.jsxs)(A.CardBody,{className:"woocommerce-order-attribution-install-banner__body "+(b?"small":""),children:[(0,l.jsx)("div",{className:"woocommerce-order-attribution-install-banner__image_container",children:e}),(0,l.jsxs)("div",{className:"woocommerce-order-attribution-install-banner__text_container "+(b?"small":""),children:[o&&(0,l.jsx)("div",{className:"woocommerce-order-attribution-install-banner__text-badge",children:(0,l.jsx)(D.Text,{className:"woocommerce-order-attribution-install-banner__text-description",as:"p",size:"12",align:"center",children:o})}),s&&(0,l.jsx)(D.Text,{className:"woocommerce-order-attribution-install-banner__text-title",as:"p",size:"16",children:s}),a&&(0,l.jsx)(D.Text,{className:"woocommerce-order-attribution-install-banner__text-description",as:"p",size:"12",children:a}),(0,l.jsxs)("div",{children:[(0,l.jsx)(A.Button,{className:b?"small":"",variant:b?"secondary":"primary",onClick:w,iconPosition:b?"right":null,isBusy:u,disabled:u,children:c}),n&&(0,l.jsx)(A.Button,{variant:"tertiary",onClick:()=>m(r),disabled:u,children:(0,N.__)("Dismiss","woocommerce")})]})]})]})})},{Slot:Yo,Fill:Ko}=(0,A.createSlotFill)("__EXPERIMENTAL__WcAdminOrderAttributionSlots"),Jo=()=>(0,l.jsx)(Ko,{children:(0,l.jsx)(Zo,{eventContext:"order-editor-order-attribution-metabox",bannerType:Wo,description:(0,N.__)("View all of your orders in our new Order Attribution extension.","woocommerce"),buttonText:(0,N.__)("Install the extension","woocommerce")})}),Xo=c()("wc-admin:client"),es=(e,t,r)=>{try{((e,t,r)=>{let n=(0,y.withSettingsHydration)(r,window.wcSettings?.admin)(fo);t&&(n=(0,y.withCurrentUserHydration)(t)(n)),(0,i.createRoot)(e).render((0,l.jsx)(n,{}))})(e,t,r),e.classList.remove("is-embed-loading");const n=document.getElementById("wpbody-content");if(!n)return Xo("WP Body content element not found"),!1;const o=(e=>e.querySelector(".wrap.woocommerce")||document.querySelector("#wpbody-content > .woocommerce")||e.querySelector(".wrap")||(Xo("Wrap element not found"),null))(n);return!!o&&(((e,t)=>{const r=document.createElement("div");(0,i.createRoot)(e.insertBefore(r,t)).render((0,l.jsx)("div",{className:"woocommerce-layout",children:(0,l.jsx)(k,{})}))})(n,o),((e,t)=>{const r=document.createElement("div");(0,i.createRoot)(e.insertBefore(r,t.nextSibling)).render((0,l.jsx)(_,{}))})(n,o),(()=>{const e=document.getElementById("order-attribution-install-banner-slotfill");e&&(0,i.createRoot)(e).render((0,l.jsx)(l.Fragment,{children:(0,l.jsxs)(A.SlotFillProvider,{children:[(0,l.jsx)(Yo,{}),(0,l.jsx)(T.PluginArea,{scope:$o})]})}))})(),(0,T.registerPlugin)("woocommerce-admin-order-editor-order-attribution-install-banner-slotfill",{scope:$o,render:Jo}),!0)}catch(e){console.error("Failed to initialize embedded layout:",e)}};(0,n.init)({errorRateLimitMs:6e4});const ts=document.getElementById("woocommerce-embedded-root");if(ts){const e="wc_admin";es(ts,(0,d.Qk)("currentUserData"),e),rs=ts,window.wcAdminFeatures&&!0===window.wcAdminFeatures["customer-effort-score-tracks"]&&(rs?(0,i.createRoot)(rs.insertBefore(document.createElement("div"),null)).render((0,l.jsx)(s.CustomerEffortScoreTracksContainer,{})):u("Customer Effort Score Tracks root not found"))}var rs})(),(window.wc=window.wc||{}).embed={}})();