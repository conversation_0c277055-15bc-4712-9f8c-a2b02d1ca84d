"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[7210],{12974:(e,t,n)=>{n.d(t,{Ay:()=>s});var i=n(13240);const a=["a","b","em","i","strong","p","br"],o=["target","href","rel","name","download"],s=e=>({__html:(0,i.sanitize)(e,{ALLOWED_TAGS:a,ALLOWED_ATTR:o})})},75753:(e,t,n)=>{n.d(t,{LO:()=>g,PE:()=>_,S:()=>m,CS:()=>w}),n(18982);var i=n(27723),a=n(56427),o=n(86087),s=n(47143),r=n(40314),c=n(96476),l=n(1069),d=n(39793);const m=({gatewayId:e,gatewayState:t,settingsHref:n,onboardingHref:m,isOffline:g,acceptIncentive:_=()=>{},gatewayHasRecommendedPaymentMethods:w,installingPlugin:p,buttonText:u=(0,i.__)("Enable","woocommerce"),incentive:y=null,setOnboardingModalOpen:h,onboardingType:f})=>{const[b,v]=(0,o.useState)(!1),{createErrorNotice:x}=(0,s.dispatch)("core/notices"),{togglePaymentGateway:S,invalidateResolutionForStoreSelector:j}=(0,s.useDispatch)(r.paymentSettingsStore),P=()=>{x((0,i.__)("An error occurred. You will be redirected to the settings page, try enabling the payment gateway there.","woocommerce"),{type:"snackbar",explicitDismiss:!0})};return(0,d.jsx)(a.Button,{variant:"primary",isBusy:b,disabled:b||!!p,onClick:a=>{if(a.preventDefault(),t.enabled)return;(0,l.TH)("provider_enable_click",{provider_id:e});const o=window.woocommerce_admin.nonces?.gateway_toggle||"";if(!o)return P(),void(window.location.href=n);v(!0),y&&_(y.promo_id),S(e,window.woocommerce_admin.ajax_url,o).then((a=>{if("needs_setup"===a.data)if(t.account_connected)x((0,i.__)("The provider could not be enabled. Check the Manage page for details.","woocommerce"),{type:"snackbar",explicitDismiss:!0,actions:[{label:(0,i.__)("Manage","woocommerce"),url:n}]});else if((0,l.TH)("provider_enable",{provider_id:e}),"native_in_context"===f&&h)h(!0);else{if(!w)return void(window.location.href=m);(0,c.getHistory)().push((0,c.getNewPath)({},"/payment-methods"))}j(g?"getOfflinePaymentGateways":"getPaymentProviders"),v(!1)})).catch((()=>{v(!1),P(),window.location.href=n}))},href:n,children:u})},g=({acceptIncentive:e,installingPlugin:t,buttonText:n=(0,i.__)("Activate payments","woocommerce"),incentive:s=null,setOnboardingModalOpen:r,onboardingType:c})=>{const[m,g]=(0,o.useState)(!1);return(0,d.jsx)(a.Button,{variant:"primary",isBusy:m,disabled:m||!!t,onClick:()=>{g(!0),(0,l.AC)().then((()=>{s&&e(s.promo_id),"native_in_context"===c?(r(!0),g(!1)):window.location.href=(0,l.ZV)()})).catch((()=>{g(!1)}))},children:n})},_=({gatewayId:e,gatewayState:t,onboardingState:n,settingsHref:m,onboardingHref:g,gatewayHasRecommendedPaymentMethods:_,installingPlugin:w,buttonText:p=(0,i.__)("Complete setup","woocommerce"),setOnboardingModalOpen:u,onboardingType:y})=>{const[h,f]=(0,o.useState)(!1),{select:b}=(0,s.useSelect)((e=>({select:e})),[]),v=t.account_connected,x=n.started,S=n.completed;return(0,o.useEffect)((()=>{"woocommerce_payments"!==e||"native_in_context"!==y||S||b(r.woopaymentsOnboardingStore).getOnboardingData()}),[e,y,S,b]),(0,d.jsx)(a.Button,{variant:"primary",isBusy:h,disabled:h||!!w,onClick:()=>{if((0,l.TH)("provider_complete_setup_click",{provider_id:e,onboarding_started:n.started,onboarding_completed:n.completed,onboarding_test_mode:n.test_mode}),f(!0),"native_in_context"===y)u(!0);else{if(v&&x)return v&&x&&!S?void(window.location.href=g):void(window.location.href=m);if(!_)return void(window.location.href=g);(0,c.getHistory)().push((0,c.getNewPath)({},"/payment-methods"))}f(!1)},children:p},e)},w=({gatewayId:e,settingsHref:t,isInstallingPlugin:n,buttonText:o=(0,i.__)("Manage","woocommerce")})=>(0,d.jsx)(a.Button,{variant:"secondary",href:t,disabled:n,onClick:()=>{(0,l.TH)("provider_manage_click",{provider_id:e})},children:o})},69222:(e,t,n)=>{n.r(t),n.d(t,{SettingsPaymentsOffline:()=>w,default:()=>p});var i=n(47143),a=n(86087),o=n(40314),s=n(51881),r=n(18537),c=n(12974),l=n(15698),d=n(75753),m=n(39793);const g=({gateway:e,...t})=>(0,m.jsx)(l.Uq,{id:e.id,className:"woocommerce-list__item woocommerce-list__item-enter-done"+(t.className?` ${t.className}`:""),...t,children:(0,m.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,m.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,m.jsx)(l.Gh,{}),e.icon&&(0,m.jsx)("img",{className:"woocommerce-list__item-image",src:e.icon,alt:e.title+" logo"})]}),(0,m.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,m.jsx)("span",{className:"woocommerce-list__item-title",children:e.title}),(0,m.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,c.Ay)((0,r.decodeEntities)(e.description))})]}),(0,m.jsx)("div",{className:"woocommerce-list__item-after",children:(0,m.jsx)("div",{className:"woocommerce-list__item-after__actions",children:e.state.enabled?(0,m.jsx)(d.CS,{gatewayId:e.id,settingsHref:e.management._links.settings.href,isInstallingPlugin:!1}):(0,m.jsx)(d.S,{installingPlugin:null,gatewayId:e.id,gatewayState:e.state,settingsHref:e.management._links.settings.href,onboardingHref:e.onboarding._links.onboard.href,isOffline:!0,gatewayHasRecommendedPaymentMethods:!1})})})]})},e.id),_=({gateways:e,setGateways:t})=>(0,m.jsx)(l.q6,{className:"woocommerce-list",items:e,setItems:t,children:e.map(((t,n)=>(0,m.jsx)(g,{gateway:t,className:"woocommerce-list__item"+(n===e.length-1?" is-last":"")},t.id)))}),w=()=>{const{offlinePaymentGateways:e,isFetching:t}=(0,i.useSelect)((e=>{const t=e(o.paymentSettingsStore);return{isFetching:t.isFetching(),offlinePaymentGateways:t.getOfflinePaymentGateways()}}),[]),{updateProviderOrdering:n}=(0,i.useDispatch)(o.paymentSettingsStore),[r,c]=(0,a.useState)(null);return(0,a.useEffect)((()=>{c(null)}),[e]),(0,m.jsx)("div",{className:"settings-payments-offline__container",children:t?(0,m.jsx)(s.i,{rows:3}):(0,m.jsx)(_,{gateways:r||e,setGateways:function(e){const t=e.map((e=>e._order)).sort(((e,t)=>e-t)),i={};e.forEach(((e,n)=>{i[e.id]=t[n]})),n(i),c(e)}})})},p=w}}]);