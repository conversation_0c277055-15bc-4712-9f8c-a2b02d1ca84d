"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[6664],{26664:(e,t,s)=>{s.r(t),s.d(t,{ActionItem:()=>G.A,ComplementaryArea:()=>Q,ComplementaryAreaMoreMenuItem:()=>U,FullscreenMode:()=>W,InterfaceSkeleton:()=>X.A,NavigableRegion:()=>Z.A,PinnedItems:()=>J.A,store:()=>O});var r={};s.r(r),s.d(r,{closeModal:()=>I,disableComplementaryArea:()=>L,enableComplementaryArea:()=>C,openModal:()=>S,pinItem:()=>M,setDefaultComplementaryArea:()=>w,setFeatureDefaults:()=>P,setFeatureValue:()=>N,toggleFeature:()=>E,unpinItem:()=>j});var a={};s.r(a),s.d(a,{getActiveComplementaryArea:()=>R,isComplementaryAreaLoading:()=>k,isFeatureActive:()=>D,isItemPinned:()=>T,isModalActive:()=>F});var n=s(4921),i=s(56427),c=s(47143),o=s(27723),l=s(5573),m=s(39793);const d=(0,m.jsx)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,m.jsx)(l.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})}),u=(0,m.jsx)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,m.jsx)(l.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"})}),p=(0,m.jsx)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,m.jsx)(l.Path,{fillRule:"evenodd",d:"M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z",clipRule:"evenodd"})});var h=s(86087),A=s(26873),f=s(41233),g=s(29491);const v=(0,m.jsx)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,m.jsx)(l.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})});var y=s(64040),b=s.n(y);function _(e){return["core/edit-post","core/edit-site"].includes(e)?(b()(`${e} interface scope`,{alternative:"core interface scope",hint:"core/edit-post and core/edit-site are merging.",version:"6.6"}),"core"):e}function x(e,t){return"core"===e&&"edit-site/template"===t?(b()("edit-site/template sidebar",{alternative:"edit-post/document",version:"6.6"}),"edit-post/document"):"core"===e&&"edit-site/block-inspector"===t?(b()("edit-site/block-inspector sidebar",{alternative:"edit-post/block",version:"6.6"}),"edit-post/block"):t}const w=(e,t)=>({type:"SET_DEFAULT_COMPLEMENTARY_AREA",scope:e=_(e),area:t=x(e,t)}),C=(e,t)=>({registry:s,dispatch:r})=>{t&&(e=_(e),t=x(e,t),s.select(f.store).get(e,"isComplementaryAreaVisible")||s.dispatch(f.store).set(e,"isComplementaryAreaVisible",!0),r({type:"ENABLE_COMPLEMENTARY_AREA",scope:e,area:t}))},L=e=>({registry:t})=>{e=_(e),t.select(f.store).get(e,"isComplementaryAreaVisible")&&t.dispatch(f.store).set(e,"isComplementaryAreaVisible",!1)},M=(e,t)=>({registry:s})=>{if(!t)return;e=_(e),t=x(e,t);const r=s.select(f.store).get(e,"pinnedItems");!0!==r?.[t]&&s.dispatch(f.store).set(e,"pinnedItems",{...r,[t]:!0})},j=(e,t)=>({registry:s})=>{if(!t)return;e=_(e),t=x(e,t);const r=s.select(f.store).get(e,"pinnedItems");s.dispatch(f.store).set(e,"pinnedItems",{...r,[t]:!1})};function E(e,t){return function({registry:s}){b()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),s.dispatch(f.store).toggle(e,t)}}function N(e,t,s){return function({registry:r}){b()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),r.dispatch(f.store).set(e,t,!!s)}}function P(e,t){return function({registry:s}){b()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),s.dispatch(f.store).setDefaults(e,t)}}function S(e){return{type:"OPEN_MODAL",name:e}}function I(){return{type:"CLOSE_MODAL"}}const R=(0,c.createRegistrySelector)((e=>(t,s)=>{s=_(s);const r=e(f.store).get(s,"isComplementaryAreaVisible");if(void 0!==r)return!1===r?null:t?.complementaryAreas?.[s]})),k=(0,c.createRegistrySelector)((e=>(t,s)=>{s=_(s);const r=e(f.store).get(s,"isComplementaryAreaVisible"),a=t?.complementaryAreas?.[s];return r&&void 0===a})),T=(0,c.createRegistrySelector)((e=>(t,s,r)=>{var a;r=x(s=_(s),r);const n=e(f.store).get(s,"pinnedItems");return null===(a=n?.[r])||void 0===a||a})),D=(0,c.createRegistrySelector)((e=>(t,s,r)=>(b()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(f.store).get(s,r))));function F(e,t){return e.activeModal===t}const V=(0,c.combineReducers)({complementaryAreas:function(e={},t){switch(t.type){case"SET_DEFAULT_COMPLEMENTARY_AREA":{const{scope:s,area:r}=t;return e[s]?e:{...e,[s]:r}}case"ENABLE_COMPLEMENTARY_AREA":{const{scope:s,area:r}=t;return{...e,[s]:r}}}return e},activeModal:function(e=null,t){switch(t.type){case"OPEN_MODAL":return t.name;case"CLOSE_MODAL":return null}return e}}),O=(0,c.createReduxStore)("core/interface",{reducer:V,actions:r,selectors:a});(0,c.register)(O);const B=(0,s(92279).withPluginContext)(((e,t)=>({icon:t.icon||e.icon,identifier:t.identifier||`${e.name}/${t.name}`}))),z=B((function({as:e=i.Button,scope:t,identifier:s,icon:r,selectedIcon:a,name:n,...o}){const l=e,d=(0,c.useSelect)((e=>e(O).getActiveComplementaryArea(t)===s),[s,t]),{enableComplementaryArea:u,disableComplementaryArea:p}=(0,c.useDispatch)(O);return(0,m.jsx)(l,{icon:a&&d?a:r,"aria-controls":s.replace("/",":"),onClick:()=>{d?p(t):u(t,s)},...o})})),$=({smallScreenTitle:e,children:t,className:s,toggleButtonProps:r})=>{const a=(0,m.jsx)(z,{icon:v,...r});return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsxs)("div",{className:"components-panel__header interface-complementary-area-header__small",children:[e&&(0,m.jsx)("h2",{className:"interface-complementary-area-header__small-title",children:e}),a]}),(0,m.jsxs)("div",{className:(0,n.A)("components-panel__header","interface-complementary-area-header",s),tabIndex:-1,children:[t,a]})]})};var G=s(78269);const Y=({__unstableExplicitMenuItem:e,__unstableTarget:t,...s})=>(0,m.jsx)(i.MenuItem,{...s});function U({scope:e,target:t,__unstableExplicitMenuItem:s,...r}){return(0,m.jsx)(z,{as:r=>(0,m.jsx)(G.A,{__unstableExplicitMenuItem:s,__unstableTarget:`${e}/${t}`,as:Y,name:`${e}/plugin-more-menu`,...r}),role:"menuitemcheckbox",selectedIcon:d,name:t,scope:e,...r})}var J=s(88150);const q={open:{width:280},closed:{width:0},mobileOpen:{width:"100vw"}};function H({activeArea:e,isActive:t,scope:s,children:r,className:a,id:n}){const c=(0,g.useReducedMotion)(),o=(0,g.useViewportMatch)("medium","<"),l=(0,g.usePrevious)(e),d=(0,g.usePrevious)(t),[,u]=(0,h.useState)({});(0,h.useEffect)((()=>{u({})}),[t]);const p={type:"tween",duration:c||o||l&&e&&e!==l?0:.3,ease:[.6,0,.4,1]};return(0,m.jsx)(i.Fill,{name:`ComplementaryArea/${s}`,children:(0,m.jsx)(i.__unstableAnimatePresence,{initial:!1,children:(d||t)&&(0,m.jsx)(i.__unstableMotion.div,{variants:q,initial:"closed",animate:o?"mobileOpen":"open",exit:"closed",transition:p,className:"interface-complementary-area__fill",children:(0,m.jsx)("div",{id:n,className:a,style:{width:o?"100vw":280},children:r})})})})}const K=B((function({children:e,className:t,closeLabel:s=(0,o.__)("Close plugin"),identifier:r,header:a,headerClassName:l,icon:g,isPinnable:v=!0,panelClassName:y,scope:b,name:_,smallScreenTitle:x,title:w,toggleShortcut:C,isActiveByDefault:L}){const[M,j]=(0,h.useState)(!1),{isLoading:E,isActive:N,isPinned:P,activeArea:S,isSmall:I,isLarge:R,showIconLabels:k}=(0,c.useSelect)((e=>{const{getActiveComplementaryArea:t,isComplementaryAreaLoading:s,isItemPinned:a}=e(O),{get:n}=e(f.store),i=t(b);return{isLoading:s(b),isActive:i===r,isPinned:a(b,r),activeArea:i,isSmall:e(A.store).isViewportMatch("< medium"),isLarge:e(A.store).isViewportMatch("large"),showIconLabels:n("core","showIconLabels")}}),[r,b]);!function(e,t,s,r,a){const n=(0,h.useRef)(!1),i=(0,h.useRef)(!1),{enableComplementaryArea:o,disableComplementaryArea:l}=(0,c.useDispatch)(O);(0,h.useEffect)((()=>{r&&a&&!n.current?(l(e),i.current=!0):i.current&&!a&&n.current?(i.current=!1,o(e,t)):i.current&&s&&s!==t&&(i.current=!1),a!==n.current&&(n.current=a)}),[r,a,e,t,s,l,o])}(b,r,S,N,I);const{enableComplementaryArea:T,disableComplementaryArea:D,pinItem:F,unpinItem:V}=(0,c.useDispatch)(O);if((0,h.useEffect)((()=>{L&&void 0===S&&!I?T(b,r):void 0===S&&I&&D(b,r),j(!0)}),[S,L,b,r,I,T,D]),M)return(0,m.jsxs)(m.Fragment,{children:[v&&(0,m.jsx)(J.A,{scope:b,children:P&&(0,m.jsx)(z,{scope:b,identifier:r,isPressed:N&&(!k||R),"aria-expanded":N,"aria-disabled":E,label:w,icon:k?d:g,showTooltip:!k,variant:k?"tertiary":void 0,size:"compact"})}),_&&v&&(0,m.jsx)(U,{target:_,scope:b,icon:g,children:w}),(0,m.jsxs)(H,{activeArea:S,isActive:N,className:(0,n.A)("interface-complementary-area",t),scope:b,id:r.replace("/",":"),children:[(0,m.jsx)($,{className:l,closeLabel:s,onClose:()=>D(b),smallScreenTitle:x,toggleButtonProps:{label:s,size:"small",shortcut:C,scope:b,identifier:r},children:a||(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("h2",{className:"interface-complementary-area-header__title",children:w}),v&&(0,m.jsx)(i.Button,{className:"interface-complementary-area__pin-unpin-item",icon:P?u:p,label:P?(0,o.__)("Unpin from toolbar"):(0,o.__)("Pin to toolbar"),onClick:()=>(P?V:F)(b,r),isPressed:P,"aria-expanded":P,size:"compact"})]})}),(0,m.jsx)(i.Panel,{className:y,children:e})]})]})}));K.Slot=function({scope:e,...t}){return(0,m.jsx)(i.Slot,{name:`ComplementaryArea/${e}`,...t})};const Q=K,W=({isActive:e})=>((0,h.useEffect)((()=>{let e=!1;return document.body.classList.contains("sticky-menu")&&(e=!0,document.body.classList.remove("sticky-menu")),()=>{e&&document.body.classList.add("sticky-menu")}}),[]),(0,h.useEffect)((()=>(e?document.body.classList.add("is-fullscreen-mode"):document.body.classList.remove("is-fullscreen-mode"),()=>{e&&document.body.classList.remove("is-fullscreen-mode")})),[e]),null);var X=s(95248),Z=s(40073)}}]);