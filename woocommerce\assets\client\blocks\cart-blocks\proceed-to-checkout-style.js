"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[9936],{31335:(e,t,o)=>{o.r(t),o.d(t,{default:()=>m});var n=o(41616),c=o(4921),r=o(86087),s=o(89874),a=o(78331),i=o(10790);const l={bottom:0,left:0,opacity:0,pointerEvents:"none",position:"absolute",right:0,top:0,zIndex:-1};var u=o(15703),b=o(47143),d=o(47594),p=o(71e3),g=o(73993),h=o(30743),k=o(14656),f=o(91869);var v=o(66867);const m=(0,n.withFilteredAttributes)(v.A)((({checkoutPageId:e,className:t,buttonLabel:n})=>{const v=(0,u.getSetting)("page-"+e,!1),m=(0,b.useSelect)((e=>e(d.checkoutStore).isCalculating()),[]),[w,C]=(()=>{const[e,t]=(0,r.useState)(""),o=(0,r.useRef)(null),n=(0,r.useRef)(new IntersectionObserver((e=>{e[0].isIntersecting?t("visible"):t(e[0].boundingClientRect.top>0?"below":"above")}),{threshold:[0,.5,1]}));return(0,r.useLayoutEffect)((()=>{const e=o.current,t=n.current;return e&&t.observe(e),()=>{t.unobserve(e)}}),[]),[(0,i.jsx)("div",{"aria-hidden":!0,ref:o,style:l}),e]})(),[y,S]=(0,r.useState)(!1);(0,r.useEffect)((()=>{if("function"!=typeof o.g.addEventListener||"function"!=typeof o.g.removeEventListener)return;const e=()=>{S(!1)};return o.g.addEventListener("pageshow",e),()=>{o.g.removeEventListener("pageshow",e)}}),[]);const _=(0,b.useSelect)((e=>e(d.cartStore).getCartData()),[]),E=(0,p.applyCheckoutFilter)({filterName:"proceedToCheckoutButtonLabel",defaultValue:n||f.G,arg:{cart:_}}),L=(0,p.applyCheckoutFilter)({filterName:"proceedToCheckoutButtonLink",defaultValue:v||a.tn,arg:{cart:_}}),{dispatchOnProceedToCheckout:x}=(0,h.e)(),A=(0,i.jsxs)(s.A,{className:(0,c.A)("wc-block-cart__submit-button",{"wc-block-cart__submit-button--loading":y}),href:L,disabled:m,onClick:e=>{x().then((t=>{t.some(g.isErrorResponse)?e.preventDefault():S(!0)}))},children:[y&&(0,i.jsx)(k.Spinner,{}),E]}),N=(0,r.useMemo)((()=>getComputedStyle(document.body).backgroundColor),[]),j="below"===C,T=(0,c.A)("wc-block-cart__submit-container",{"wc-block-cart__submit-container--sticky":j});return(0,i.jsxs)("div",{className:(0,c.A)("wc-block-cart__submit",t),children:[w,(0,i.jsx)("div",{className:T,style:j?{backgroundColor:N}:{},children:A})]})}))}}]);