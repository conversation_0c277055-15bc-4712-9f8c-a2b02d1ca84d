/*! For license information please see product-tour.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,t,o)=>{var r=o(51609),c=Symbol.for("react.element"),n=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function a(e,t,o){var r,n={},a=null,u=null;for(r in void 0!==o&&(a=""+o),void 0!==t.key&&(a=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,r)&&!d.hasOwnProperty(r)&&(n[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===n[r]&&(n[r]=t[r]);return{$$typeof:c,type:e,key:a,ref:u,props:n,_owner:s.current}}t.Fragment=n,t.jsx=a,t.jsxs=a},39793:(e,t,o)=>{e.exports=o(94931)},51609:e=>{e.exports=window.React}},t={};const o=window.wp.element,r=window.wp.hooks,c=window.wp.i18n,n=window.wc.components,i=window.wc.tracks,s=({iframeSelector:e,isActive:t})=>((0,o.useEffect)((()=>{if(!t)return;const o=(({iframeSelector:e,childSelector:t,className:o})=>{const r=document.querySelector(e),c=r?.contentDocument||r?.contentWindow&&r?.contentWindow.document;if(c){const e=()=>r?.classList.add(o),n=()=>r?.classList.remove(o),i=c.querySelector(t);return i?.addEventListener("focus",e),i?.addEventListener("blur",n),()=>{i?.removeEventListener("focus",e),i?.removeEventListener("blur",n)}}return()=>({})})({iframeSelector:`${e}`,childSelector:"#tinymce",className:"focus-within"});return()=>{o()}}),[t,e]),{style:t?`\n\t\t\t\t${e}.focus-within {\n\t\t\t\t\tborder: 1.5px solid #007CBA;\n\t\t\t\t}\n\t\t\t\t`:null}),d=({editorWrapSelector:e})=>{const t=(0,o.useRef)(document.querySelector(e));t||console.warn(`Editor Wrap ${e} not found`);const[r,c]=(0,o.useState)(t.current&&t.current.classList.contains("html-active")?"html":"tmce");return(0,o.useEffect)((()=>{const t=e=>{e.target&&c(e.target.classList.contains("switch-html")?"html":"tmce")},o=document.querySelector(`${e} .switch-tmce`);o?.addEventListener("click",t);const r=document.querySelector(`${e} .switch-html`);return r?.addEventListener("click",t),()=>{o?.removeEventListener("click",t),r?.removeEventListener("click",t)}}),[e]),{activeEditor:r,isTmce:"tmce"===r,isHtml:"html"===r}},a=e=>document.querySelector(e).value,u=e=>{const t=document.querySelector(e),o=t?.contentWindow?.document.querySelector("#tinymce");return o?.innerHTML||""},p=e=>document.querySelector(e)?.value||"",l=e=>e?u("#content_ifr"):p("#wp-content-editor-container > .wp-editor-area"),m=e=>e?u("#excerpt_ifr"):p("#wp-excerpt-editor-container > .wp-editor-area");var w=function o(r){var c=t[r];if(void 0!==c)return c.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,o),n.exports}(39793);const h=()=>{const[e,t]=(0,o.useState)(!1),{setIsLoaded:u,hasUpdatedInfo:p}=(()=>{const{isTmce:e}=d({editorWrapSelector:"#wp-content-wrap"}),{isTmce:t}=d({editorWrapSelector:"#wp-excerpt-wrap"}),[r,c]=(0,o.useState)({}),[n,i]=(0,o.useState)(!1),s=(0,o.useCallback)((()=>({"product-name":a("#title"),"product-description":l(e),"product-data":a("#_regular_price"),"product-short-description":m(t),"product-image":document.querySelector("#set-post-thumbnail img")?.src||"","product-tags":Array.from(document.querySelectorAll("#product_tag li")).map((e=>e.lastChild.textContent)).join(","),"product-categories":Array.from(document.querySelectorAll("#product_cat-all #product_catchecklist input")).map((e=>e.checked)).join(",")})),[e,t]),u=(0,o.useCallback)((e=>{const t=s();return r[e]!==t[e]&&""!==t[e]}),[s,r]);return(0,o.useEffect)((()=>{n&&c(s())}),[c,n,s]),{setIsLoaded:i,hasUpdatedInfo:u}})(),{isTmce:h}=d({editorWrapSelector:"#wp-content-wrap"}),{isTmce:_}=d({editorWrapSelector:"#wp-excerpt-wrap"}),{style:f}=s({isActive:e&&h,iframeSelector:"#content_ifr"}),{style:y}=s({isActive:e&&_,iframeSelector:"#excerpt_ifr"}),g=(({isExcerptEditorTmceActive:e,isContentEditorTmceActive:t,closeHandler:o,onNextStepHandler:n})=>{const i=new URLSearchParams(window.location.search),s=[{referenceElements:{desktop:"#title"},focusElement:{desktop:"#title"},meta:{name:"product-name",heading:(0,c.__)("Product name","woocommerce"),descriptions:{desktop:(0,c.__)("Start typing your new product name here. This will be what your customers will see in your store.","woocommerce")}}},{referenceElements:{desktop:"#postdivrich"},focusElement:{iframe:t?"#content_ifr":void 0,desktop:t?"#tinymce":"#wp-content-editor-container > .wp-editor-area"},meta:{name:"product-description",heading:(0,c.__)("Add your product description","woocommerce"),descriptions:{desktop:(0,c.__)("Add your full product description here. Describe your product in detail.","woocommerce")}}},{referenceElements:{desktop:"#woocommerce-product-data"},focusElement:{desktop:"#_regular_price"},meta:{name:"product-data",heading:(0,c.__)("Add your product data","woocommerce"),descriptions:{desktop:(0,c.__)("Use the tabs to switch between sections and insert product details. Start by adding your product price.","woocommerce")}}},{referenceElements:{desktop:"#postexcerpt"},focusElement:{iframe:e?"#excerpt_ifr":void 0,desktop:e?"#tinymce":"#wp-excerpt-editor-container > .wp-editor-area"},meta:{name:"product-short-description",heading:(0,c.__)("Add your short product description","woocommerce"),descriptions:{desktop:(0,c.__)("Type a quick summary for your product here. This will appear on the product page right under the product name.","woocommerce")}}},{referenceElements:{desktop:"#postimagediv"},focusElement:{desktop:"#set-post-thumbnail"},meta:{name:"product-image",heading:(0,c.__)("Add your product image","woocommerce"),descriptions:{desktop:(0,c.__)("Upload an image to your product here. Ideally a JPEG or PNG about 600 px wide or bigger. This image will be shown in your store’s catalog.","woocommerce")}}},{referenceElements:{desktop:"#tagsdiv-product_tag"},focusElement:{desktop:"#new-tag-product_tag"},meta:{name:"product-tags",heading:(0,c.__)("Add your product tags","woocommerce"),descriptions:{desktop:(0,c.__)("Add your product tags here. Tags are a method of labeling your products to make them easier for customers to find. For example, if you sell clothing, and you have a lot of cat prints, you could make a tag for “cat.”","woocommerce")}}},{referenceElements:{desktop:"#product_catdiv"},meta:{name:"product-categories",heading:(0,c.__)("Add your product categories","woocommerce"),descriptions:{desktop:(0,c.__)("Add your product categories here. Assign categories to your products to make them easier to browse through and find in your store.","woocommerce")}}},{referenceElements:{desktop:"#submitdiv"},focusElement:{desktop:"#submitdiv"},meta:{name:"publish",heading:(0,c.__)("Publish your product 🎉","woocommerce"),descriptions:{desktop:(0,c.__)("Good work! Now you can publish your product to your store by hitting the “Publish” button or keep editing it.","woocommerce")},primaryButton:{text:(0,c.__)("Keep editing","woocommerce")}}}],d=(0,r.applyFilters)("experimental_woocommerce_admin_product_tour_steps",s,i.get("tutorial_type"));if(!Array.isArray(d))throw new Error("Tour guide steps must be an array.");return{placement:"bottom-start",options:{effects:{spotlight:{interactivity:{enabled:!0,rootElementSelector:"#wpwrap"}},arrowIndicator:!0,autoScroll:{behavior:"auto",block:"center"},liveResize:{mutation:!0,resize:!0,rootElementSelector:"#wpwrap"}},popperModifiers:[{name:"arrow",options:{padding:({popper:e})=>({right:e.width-34})}}],callbacks:{onNextStep:n}},steps:d,closeHandler:o}})({isContentEditorTmceActive:h,isExcerptEditorTmceActive:_,closeHandler:(e,o)=>{t(!1),e.length-1===o?(0,i.recordEvent)("walkthrough_product_completed"):(0,i.recordEvent)("walkthrough_product_dismissed",{step_name:e[o].meta.name})},onNextStepHandler:e=>{const t=g.steps[e-1].meta.name;(0,i.recordEvent)("walkthrough_product_step_completed",{step_name:t,added_info:p(t)?"yes":"no"})}});return(0,o.useEffect)((()=>{var e;if(e=e=>{e.preventDefault(),t(!0),(0,i.recordEvent)("walkthrough_product_enable_button_click")},window.document.querySelector(".wp-heading-inline + .page-title-action")?.addEventListener("click",e),"true"===new URLSearchParams(window.location.search).get("tutorial")&&g.steps?.length>0){const e=(e=>{const o=document.querySelector(e);let r=o?.getBoundingClientRect().top;const c=setInterval((()=>{const e=o?.getBoundingClientRect().top;r===e&&(t(!0),(0,i.recordEvent)("walkthrough_product_view",{spotlight:"yes",product_template:"physical"}),u(!0),clearInterval(c)),r=e}),500);return c})(g.steps[0].referenceElements?.desktop||"");return()=>clearInterval(e)}}),[]),(e=>{const t=(0,o.useRef)((()=>{}));(0,o.useEffect)((()=>(e?t.current=(e=>{const t=window.document.querySelector("#publish");return t&&t.addEventListener("click",e),function(){t?.removeEventListener("click",e)}})((()=>{(0,i.recordEvent)("walkthrough_product_completed")})):(t.current(),t.current=()=>{}),function(){t.current()})),[e])})(e),e?(0,w.jsxs)(w.Fragment,{children:[(0,w.jsxs)("style",{children:[f,y,".wp-editor-area:focus {\n\t\t\t\t\t\tborder: 1.5px solid #007CBA;\n\t\t\t\t\t}"]}),(0,w.jsx)(n.TourKit,{config:g})]}):null},_=document.createElement("div");_.setAttribute("id","product-tour-root"),(0,o.createRoot)(document.body.appendChild(_)).render((0,w.jsx)(h,{})),(window.wc=window.wc||{}).productTour={}})();