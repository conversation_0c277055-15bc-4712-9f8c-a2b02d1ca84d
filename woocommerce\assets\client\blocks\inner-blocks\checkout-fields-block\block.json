{"name": "woocommerce/checkout-fields-block", "version": "1.0.0", "title": "Checkout Fields", "description": "Column containing checkout address fields.", "category": "woocommerce", "supports": {"align": false, "html": false, "multiple": false, "reusable": false, "inserter": false, "lock": false}, "attributes": {"className": {"type": "string", "default": ""}, "lock": {"type": "object", "default": {"remove": true, "move": true}}}, "parent": ["woocommerce/checkout"], "textdomain": "woocommerce", "$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3}