"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[2227],{5299:(e,t,o)=>{o.d(t,{A:()=>r});var s=o(7723);const r=({defaultTitle:e=(0,s.__)("Step","woocommerce"),defaultDescription:t=(0,s.__)("Step description text.","woocommerce"),defaultShowStepNumber:o=!0})=>({title:{type:"string",default:e},description:{type:"string",default:t},showStepNumber:{type:"boolean",default:o}})},6663:(e,t,o)=>{o.r(t),o.d(t,{default:()=>F});var s=o(4921),r=o(4656),i=o(8331),d=o(7143),l=o(7594),c=o(1616),n=o(4199),a=o(7792),u=o(5336),p=o(6087),h=o(7370),m=o(8696),b=o(3001),f=o(790);const k=()=>{const{additionalFields:e}=(0,d.useSelect)((e=>({additionalFields:e(l.checkoutStore).getAdditionalFields()})),[]),{isEditor:t}=(0,h.m)(),{setAdditionalFields:o}=(0,d.useDispatch)(l.checkoutStore),s={...e},c=t?b.A:p.Fragment;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(r.StoreNoticesContainer,{context:m.tG.ORDER_INFORMATION}),(0,f.jsx)(c,{children:(0,f.jsx)(u.l,{id:"order",addressType:"order",onChange:e=>{o(e)},fields:i.pt,values:s})})]})};var S=o(7723);const w={...(0,o(5299).A)({defaultTitle:(0,S.__)("Additional order information","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!1,remove:!0}}},F=(0,c.withFilteredAttributes)(w)((({title:e,description:t,children:o,className:c})=>{const{showFormStepNumbers:p}=(0,n.O)(),{defaultFields:h}=(0,a.C)(),m=(0,u.b)(i.pt,h,"order"),b=(0,d.useSelect)((e=>e(l.checkoutStore).isProcessing()),[]);return 0===m.length||m.every((e=>!!e.hidden))?null:(0,f.jsxs)(r.FormStep,{id:"order-fields",disabled:b,className:(0,s.A)("wc-block-checkout__order-fields",c),title:e,description:t,showStepNumber:p,children:[(0,f.jsx)(k,{}),o]})}))}}]);