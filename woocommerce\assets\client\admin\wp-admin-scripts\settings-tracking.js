(()=>{"use strict";const e=window.wc.customerEffortScore;function t(e){const t=e.querySelectorAll("input, select, textarea"),o={};for(const e of t){const t=e.name||e.id;if("button"!==e.type&&"image"!==e.type&&"submit"!==e.type&&"hidden"!==e.type&&t)switch(e.type){case"checkbox":o[t]=+e.checked;break;case"radio":void 0===o[t]&&(o[t]=""),e.checked&&(o[t]=e.value);break;case"select-multiple":const c=[];for(const t of e.options)t.selected&&c.push(t.value);o[t]=c;break;default:o[t]=e.value}}return o}const o=document.forms;if(o&&o.mainform){let c=!1;const n=document.querySelector(".woocommerce-save-button");n&&n.addEventListener("click",(()=>{c=!0}));const r=t(o.mainform);(0,e.addCustomerEffortScoreExitPageListener)("settings_change",(()=>{if(c)return!1;const e=o.mainform?t(o.mainform):{};let n=!1;for(const t of Object.keys(r))if(("object"==typeof r[t]?JSON.stringify(r[t]):r[t])!==("object"==typeof e[t]?JSON.stringify(e[t]):e[t])){n=!0;break}return n}))}(window.wc=window.wc||{}).settingsTracking={}})();