"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[9336],{69336:(e,t,o)=>{o.r(t),o.d(t,{ModalEditor:()=>de});var r=o(86087),n=o(47143),c=o(56427),l=o(29491),a=o(92279),s=o(17697),i=o.n(s),m=o(41233),d=o(94715),u=o(26664),p=o(27723),h=o(42059);function _({onClick:e}){return(0,r.createElement)(c.<PERSON><PERSON>,{className:"woocommerce-iframe-editor__back-button",icon:h.A,onClick:e},(0,p.__)("Back","woocommerce"))}function b({children:e,enableResizing:t,settings:o,...n}){const c=(0,d.__unstableUseMouseMoveTypingReset)();return(0,r.createElement)(d.__unstableIframe,{ref:c,name:"editor-canvas",className:"edit-site-visual-editor__editor-canvas",...n},(0,r.createElement)(r.Fragment,null,(0,r.createElement)(d.__unstableEditorStyles,{styles:o?.styles}),(0,r.createElement)("style",null,".is-root-container {\n\t\t\t\t\t\t\t\tpadding: 36px;\n\t\t\t\t\t\t\t\tdisplay: flow-root;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbody { position: relative; }"),t&&(0,r.createElement)("style",null,".is-root-container { min-height: 0 !important; }"),e))}const w=(0,r.createContext)({hasRedo:!1,hasUndo:!1,isDocumentOverviewOpened:!1,isInserterOpened:!1,redo:()=>{},setIsDocumentOverviewOpened:()=>{},setIsInserterOpened:()=>{},undo:()=>{}});var v=o(67606),f=o(2111),E=o(52887),g=o(88150),k=o(22794),C=o(51388),S=o(48558);const y=(0,r.forwardRef)((function(e,t){const o=(0,S.isAppleOS)()?S.displayShortcut.primaryShift("z"):S.displayShortcut.primary("y"),{hasRedo:n,redo:l}=(0,r.useContext)(w);return(0,r.createElement)(c.Button,{...e,ref:t,icon:(0,p.isRTL)()?C.A:k.A,label:(0,p.__)("Redo","woocommerce"),shortcut:o,"aria-disabled":!n,onClick:n?l:void 0,className:"editor-history__redo"})})),I=(0,r.forwardRef)((function(e,t){const{hasUndo:o,undo:n}=(0,r.useContext)(w);return(0,r.createElement)(c.Button,{...e,ref:t,icon:(0,p.isRTL)()?k.A:C.A,label:(0,p.__)("Undo","woocommerce"),shortcut:S.displayShortcut.primary("z"),"aria-disabled":!o,onClick:o?n:void 0,className:"editor-history__undo"})}));var O=o(6006);const T=(0,r.forwardRef)((function(e,t){const{isDocumentOverviewOpened:o,setIsDocumentOverviewOpened:n}=(0,r.useContext)(w);return(0,r.createElement)(c.Button,{...e,ref:t,icon:O.A,isPressed:o,label:(0,p.__)("Document overview","woocommerce"),shortcut:S.displayShortcut.access("o"),onClick:function(){n(!o)},className:"document-overview"})}));var R=o(78269),B=o(74997),D=o(83306);const x=()=>{const{createNotice:e}=(0,n.useDispatch)("core/notices"),{blocks:t}=(0,n.useSelect)((e=>{const{getBlocks:t}=e(d.store);return{blocks:t()}}),[]),o=(0,l.useCopyToClipboard)((()=>(0,B.serialize)(t)),(()=>{e("success",(0,p.__)("All content copied.","woocommerce"))}));return(0,r.createElement)(c.MenuItem,{ref:o,role:"menuitem",onClick:()=>{(0,D.recordEvent)("product_iframe_editor_copy_all_content_menu_item_click")},disabled:!t.length},(0,p.__)("Copy all content","woocommerce"))};var z=o(67237);const N=()=>(0,r.createElement)(c.MenuItem,{role:"menuitem",icon:z.A,href:(0,p.__)("https://wordpress.org/documentation/article/wordpress-block-editor/","woocommerce"),onClick:()=>{(0,D.recordEvent)("product_iframe_editor_help_menu_item_click")},target:"_blank",rel:"noopener noreferrer"},(0,p.__)("Help","woocommerce"),(0,r.createElement)(c.VisuallyHidden,{as:"span"},(0,p.__)("(opens in a new tab)","woocommerce"))),A=()=>(0,r.createElement)(c.MenuGroup,{label:(0,p.__)("Tools","woocommerce")},(0,r.createElement)(x,null),(0,r.createElement)(N,null));function M(){const{set:e}=(0,n.useDispatch)(m.store);return(0,l.useViewportMatch)("medium")?(0,r.createElement)(c.MenuGroup,{label:(0,p.__)("View","woocommerce")},(0,r.createElement)(m.PreferenceToggleMenuItem,{scope:"core",name:"fixedToolbar",onToggle:()=>{e("core","distractionFree",!1)},label:(0,p.__)("Top toolbar","woocommerce"),info:(0,p.__)("Access all block and document tools in a single place","woocommerce"),messageActivated:(0,p.__)("Top toolbar activated","woocommerce"),messageDeactivated:(0,p.__)("Top toolbar deactivated","woocommerce")})):null}var L=o(36952),P=o(3846);const H=()=>(0,r.createElement)(P.Y,null,(e=>(0,r.createElement)(r.Fragment,null,(0,r.createElement)(M,null),(0,r.createElement)(R.A.Slot,{name:L.g1,label:(0,p.__)("Plugins","woocommerce"),as:c.MenuGroup,fillProps:{onClick:e}}),(0,r.createElement)(A,null))));function V({onSave:e=()=>{},onCancel:t=()=>{}}){const{isInserterOpened:o,setIsInserterOpened:a}=(0,r.useContext)(w),[s,u]=(0,r.useState)(!0),h=(0,l.useViewportMatch)("medium"),_=(0,r.useRef)(null),{isInserterEnabled:b,isTextModeEnabled:k,hasBlockSelection:C,hasFixedToolbar:S}=(0,n.useSelect)((e=>{var t,o;const{hasInserterItems:r,getBlockRootClientId:n,getBlockSelectionEnd:c,__unstableGetEditorMode:l,getBlockSelectionStart:a}=e(d.store),{get:s}=e(m.store);return{isTextModeEnabled:"text"===l(),isInserterEnabled:r(null!==(t=n(null!==(o=c())&&void 0!==o?o:""))&&void 0!==t?t:void 0),hasBlockSelection:!!a(),hasFixedToolbar:s("core","fixedToolbar")}}),[]),O=(0,r.useCallback)((()=>a(!o)),[o,a]);return(0,r.useEffect)((()=>{C&&u(!1)}),[C]),(0,r.createElement)("div",{className:"woocommerce-iframe-editor__header"},(0,r.createElement)("div",{className:"woocommerce-iframe-editor__header-left"},(0,r.createElement)(d.NavigableToolbar,{className:"woocommerce-iframe-editor-document-tools","aria-label":(0,p.__)("Document tools","woocommerce"),variant:"unstyled"},(0,r.createElement)("div",{className:"woocommerce-iframe-editor-document-tools__left"},(0,r.createElement)(c.ToolbarItem,{ref:_,as:c.Button,className:"woocommerce-iframe-editor__header-inserter-toggle",variant:"primary",isPressed:o,onMouseDown:e=>{e.preventDefault()},onClick:O,disabled:!b,icon:v.A,label:(0,p.__)("Toggle block inserter","woocommerce"),"aria-expanded":o,showTooltip:!0}),h&&(0,r.createElement)(c.ToolbarItem,{as:d.ToolSelector,disabled:k,size:"compact"}),(0,r.createElement)(c.ToolbarItem,{as:I,size:"compact"}),(0,r.createElement)(c.ToolbarItem,{as:y,size:"compact"}),(0,r.createElement)(c.ToolbarItem,{as:T,size:"compact"}))),S&&h&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:i()("selected-block-tools-wrapper",{"is-collapsed":s})},(0,r.createElement)(d.BlockToolbar,{hideDragHandle:!0})),(0,r.createElement)(c.Popover.Slot,{name:"block-toolbar"}),C&&(0,r.createElement)(c.Button,{className:"edit-post-header__block-tools-toggle",icon:s?f.A:E.A,onClick:()=>{u((e=>!e))},label:s?(0,p.__)("Show block tools","woocommerce"):(0,p.__)("Hide block tools","woocommerce")}))),(0,r.createElement)("div",{className:"woocommerce-iframe-editor__header-right"},(0,r.createElement)(c.Button,{variant:"tertiary",className:"woocommerce-modal-actions__cancel-button",onClick:t,text:(0,p.__)("Cancel","woocommerce")}),(0,r.createElement)(c.Button,{variant:"primary",className:"woocommerce-modal-actions__done-button",onClick:e,text:(0,p.__)("Done","woocommerce")}),(0,r.createElement)(g.A.Slot,{scope:L.L1}),(0,r.createElement)(H,null)))}const F=()=>{const e=(0,n.useRegistry)();return(0,r.useEffect)((()=>{e.register(u.store)}),[e]),null},U=20;function K({direction:e,resizeWidthBy:t}){return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("button",{className:`resizable-editor__drag-handle is-${e}`,"aria-label":(0,p.__)("Drag to resize","woocommerce"),"aria-describedby":`resizable-editor__resize-help-${e}`,onKeyDown:function(o){const{keyCode:r}=o;"left"===e&&r===S.LEFT||"right"===e&&r===S.RIGHT?t(U):("left"===e&&r===S.RIGHT||"right"===e&&r===S.LEFT)&&t(-U)}}),(0,r.createElement)(c.VisuallyHidden,{id:`resizable-editor__resize-help-${e}`},(0,p.__)("Use left and right arrow keys to resize the canvas.","woocommerce")))}const W={position:void 0,userSelect:void 0,cursor:void 0,width:void 0,height:void 0,top:void 0,right:void 0,bottom:void 0,left:void 0};function G({enableResizing:e,height:t,children:o}){const[n,l]=(0,r.useState)("100%"),a=(0,r.useRef)(),s=(0,r.useCallback)((e=>{a.current&&l((a.current.offsetWidth+e).toString())}),[]);return(0,r.createElement)(c.ResizableBox,{ref:e=>{a.current=e?.resizable},size:{width:e?n:"100%",height:e&&t?t:"100%"},onResizeStop:(e,t,o)=>{l(o.style.width)},minWidth:300,maxWidth:"100%",maxHeight:"100%",minHeight:t,enable:{right:e,left:e},showHandle:e,resizeRatio:2,handleComponent:{left:(0,r.createElement)(K,{direction:"left",resizeWidthBy:s}),right:(0,r.createElement)(K,{direction:"right",resizeWidthBy:s})},handleClasses:void 0,handleStyles:{left:W,right:W}},o)}function $(){const{setIsInserterOpened:e}=(0,r.useContext)(w),t=(0,l.useViewportMatch)("medium","<"),{rootClientId:o}=(0,n.useSelect)((e=>{const{getBlockRootClientId:t}=e(d.store);return{rootClientId:t("")}}),[]),c=(0,r.useCallback)((()=>e(!1)),[e]),a=(0,r.useCallback)((e=>{e.keyCode!==S.ESCAPE||e.defaultPrevented||(e.preventDefault(),c())}),[c]),s=(0,r.useRef)(null);return(0,r.useEffect)((()=>{s.current?.focusSearch?.()}),[]),(0,r.createElement)("div",{onKeyDown:e=>a(e),className:"woocommerce-iframe-editor__inserter-panel"},(0,r.createElement)("div",{className:"woocommerce-iframe-editor__inserter-panel-content"},(0,r.createElement)(d.__experimentalLibrary,{showInserterHelpPanel:!0,shouldFocusBlock:t,rootClientId:o,ref:s,onClose:c,onSelect:()=>{t&&c()}})))}var J=o(91218);function j(){const{setIsDocumentOverviewOpened:e}=(0,r.useContext)(w),t=(0,l.useFocusOnMount)("firstElement"),o=(0,l.useFocusReturn)(),n=(0,l.useFocusReturn)(),[a,s]=(0,r.useState)(null),[i,m]=(0,r.useState)("list-view"),u=(0,r.useRef)(null),h=(0,l.useMergeRefs)([n,t,u,s]);return(0,r.createElement)("div",{className:"woocommerce-iframe-editor__document-overview-sidebar",onKeyDown:function(t){"Escape"!==t.code||t.defaultPrevented||(t.preventDefault(),e(!1))}},(0,r.createElement)(c.Button,{className:"woocommerce-iframe-editor__document-overview-sidebar-close-button",ref:o,icon:J.A,label:(0,p.__)("Close","woocommerce"),onClick:()=>e(!1)}),(0,r.createElement)(c.TabPanel,{className:"woocommerce-iframe-editor__document-overview-sidebar-tab-panel",initialTabName:i,onSelect:m,tabs:[{name:"list-view",title:(0,p.__)("List View","woocommerce"),className:"woocommerce-iframe-editor__document-overview-sidebar-tab-item"}]},(e=>(0,r.createElement)("div",{className:"woocommerce-iframe-editor__document-overview-sidebar-tab-content",ref:h},"list-view"===e.name?(0,r.createElement)(d.__experimentalListView,{dropZoneElement:a}):null))))}function q(){const{isInserterOpened:e,isDocumentOverviewOpened:t}=(0,r.useContext)(w);return e?(0,r.createElement)($,null):t?(0,r.createElement)(j,null):null}const Y=(0,r.createElement)("svg",{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM8.5 18.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h2.5v13zm10-.5c0 .3-.2.5-.5.5h-8v-13h8c.3 0 .5.2.5.5v12z"})),Z=(0,r.createElement)("svg",{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-4 14.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h8v13zm4.5-.5c0 .3-.2.5-.5.5h-2.5v-13H18c.3 0 .5.2.5.5v12z"}));var Q=o(96699);const X=()=>(0,r.createElement)("strong",null,(0,p.__)("Settings","woocommerce")),ee=({smallScreenTitle:e})=>(0,r.createElement)(Q.w,{identifier:L.PK,title:(0,p.__)("Settings","woocommerce"),icon:(0,p.isRTL)()?Z:Y,isActiveByDefault:!0,header:(0,r.createElement)(X,null),closeLabel:(0,p.__)("Close settings","woocommerce"),smallScreenTitle:e},(0,r.createElement)(d.BlockInspector,null)),te=50;var oe=o(3450),re=o(53031);const ne=()=>{const{isDocumentOverviewOpened:e,redo:t,setIsDocumentOverviewOpened:o,undo:c}=(0,r.useContext)(w),{isSettingsSidebarOpen:l}=(0,n.useSelect)((e=>{const{getActiveComplementaryArea:t}=e(u.store);return{isSettingsSidebarOpen:t(L.L1)===L.PK}}),[]),{disableComplementaryArea:a,enableComplementaryArea:s}=(0,n.useDispatch)(u.store);return(0,re.useShortcut)("woocommerce/product-editor/modal-block-editor/undo",(e=>{c(),e.preventDefault()})),(0,re.useShortcut)("woocommerce/product-editor/modal-block-editor/redo",(e=>{t(),e.preventDefault()})),(0,re.useShortcut)("woocommerce/product-editor/modal-block-editor/toggle-list-view",(t=>{o(!e),t.preventDefault()})),(0,re.useShortcut)("woocommerce/product-editor/modal-block-editor/toggle-sidebar",(e=>{l?a(L.L1):s(L.L1,L.PK),e.preventDefault()})),null},ce=()=>{const{registerShortcut:e}=(0,n.useDispatch)(re.store);return(0,r.useEffect)((()=>{e({name:"woocommerce/product-editor/modal-block-editor/undo",category:"global",description:(0,p.__)("Undo your last changes.","woocommerce"),keyCombination:{modifier:"primary",character:"z"}}),e({name:"woocommerce/product-editor/modal-block-editor/redo",category:"global",description:(0,p.__)("Redo your last undo.","woocommerce"),keyCombination:{modifier:"primaryShift",character:"z"},aliases:(0,S.isAppleOS)()?[]:[{modifier:"primary",character:"y"}]}),e({name:"woocommerce/product-editor/modal-block-editor/toggle-list-view",category:"global",description:(0,p.__)("Open the block list view.","woocommerce"),keyCombination:{modifier:"access",character:"o"}}),e({name:"woocommerce/product-editor/modal-block-editor/toggle-sidebar",category:"global",description:(0,p.__)("Show or hide the Settings sidebar.","woocommerce"),keyCombination:{modifier:"primaryShift",character:","}})}),[e]),null},le="SET_IS_INSERTER_OPENED",ae="SET_IS_LISTVIEW_OPENED",se={isInserterOpened:!1,isListViewOpened:!1};function ie(e,t){switch(t.type){case le:return{...e,isInserterOpened:t.value,isListViewOpened:!t.value&&e.isListViewOpened};case ae:return{...e,isListViewOpened:t.value,isInserterOpened:!t.value&&e.isInserterOpened}}return e}function me({onChange:e=()=>{},onClose:t,onInput:o=()=>{},settings:s,showBackButton:p=!1,name:h}){const[v]=(0,l.useResizeObserver)(),[f,E]=(0,r.useState)([]),g=(0,n.useSelect)((e=>e(oe.p).getModalEditorBlocks()),[]),{setModalEditorBlocks:k,setModalEditorContentHasChanged:C}=(0,n.useDispatch)(oe.p),{appendEdit:S,hasRedo:y,hasUndo:I,redo:O,undo:T}=function({maxHistory:e=te,setBlocks:t}){const[o,n]=(0,r.useState)([]),[c,a]=(0,r.useState)(0),s=(0,l.useDebounce)((0,r.useCallback)((t=>{const r=[...o.slice(0,c+1),t].slice(-1*e);n(r),a(r.length-1)}),[o,e,c]),500),i=(0,r.useCallback)((()=>{s.flush();const e=Math.max(0,c-1);o[e]&&(t(o[e]),a(e))}),[s,o,c,t]),m=(0,r.useCallback)((()=>{s.flush();const e=Math.min(o.length-1,c+1);o[e]&&(t(o[e]),a(e))}),[s,o,c,t]);return{appendEdit:s,hasRedo:!!o.length&&c<o.length-1,hasUndo:!!o.length&&c>0,redo:m,undo:i}}({setBlocks:E});(0,r.useEffect)((()=>{S(g),E(g)}),[]);const[{isInserterOpened:R,isListViewOpened:B},D]=(0,r.useReducer)(ie,se),x=(0,r.useCallback)((e=>{D({type:le,value:e})}),[]),z=(0,r.useCallback)((e=>{D({type:ae,value:e})}),[]),{clearSelectedBlock:N,updateSettings:A}=(0,n.useDispatch)(d.store),M=(0,n.useSelect)((e=>e(d.store).getSettings()),[]),{hasFixedToolbar:P}=(0,n.useSelect)((e=>{const{get:t}=e(m.store);return{hasFixedToolbar:t("core","fixedToolbar")}}),[]);(0,r.useEffect)((()=>{A(productBlockEditorSettings)}),[]);const H=s||M;return(0,r.createElement)("div",{className:"woocommerce-iframe-editor"},(0,r.createElement)(w.Provider,{value:{hasRedo:y,hasUndo:I,isInserterOpened:R,isDocumentOverviewOpened:B,redo:O,setIsInserterOpened:x,setIsDocumentOverviewOpened:z,undo:T}},(0,r.createElement)(d.BlockEditorProvider,{settings:{...H,hasFixedToolbar:P,templateLock:!1},value:f,onChange:t=>{S(t),E(t),e(t)},onInput:e=>{S(e),E(e),o(e)},useSubRegistry:!0},(0,r.createElement)(F,null),(0,r.createElement)(ne,null),(0,r.createElement)(ce,null),(0,r.createElement)(V,{onSave:()=>{k(function(e){if(!e?.length)return!0;if(1===e.length){const t=e[0];if("core/paragraph"===t.name){const{content:e,dropCap:o,backgroundColor:r,...n}=t.attributes,c=!e||!e.trim(),l=!!r,a=Object.keys(n).length>0;if(c&&!l&&!a)return!0}}return!1}(f)?[]:f),C(!0),e(f),t?.()},onCancel:()=>{k(g),e(g),E(g),t?.()}}),(0,r.createElement)("div",{className:"woocommerce-iframe-editor__main"},(0,r.createElement)(q,null),(0,r.createElement)(d.BlockTools,{className:i()("woocommerce-iframe-editor__content"),onClick:e=>{e.target===e.currentTarget&&N()}},(0,r.createElement)(d.BlockEditorKeyboardShortcuts.Register,null),p&&t&&(0,r.createElement)(_,{onClick:()=>{setTimeout(t,550)}}),(0,r.createElement)(G,{enableResizing:!0,height:"100%"},(0,r.createElement)(b,{enableResizing:!0,settings:H},v,(0,r.createElement)(d.BlockList,{className:"edit-site-block-editor__block-list wp-site-blocks"})),(0,r.createElement)(c.Popover.Slot,null)),(0,r.createElement)("div",{className:"woocommerce-iframe-editor__content-inserter-clipper"})),(0,r.createElement)(u.ComplementaryArea.Slot,{scope:L.L1})),(0,r.createElement)(a.PluginArea,{scope:"woocommerce-product-editor-modal-block-editor"}),(0,r.createElement)(ee,{smallScreenTitle:h}))))}function de({initialBlocks:e,onChange:t,onClose:o,title:a,name:s}){const{closeModalEditor:i}=(0,n.useDispatch)(oe.p),m=(0,l.useDebounce)((e=>{t?.(e)}),250);function d(){const e=m.flush();e&&t?.(e),i(),o?.()}return(0,r.createElement)(c.Modal,{className:"woocommerce-modal-editor",title:a,onRequestClose:d,shouldCloseOnClickOutside:!1},(0,r.createElement)(me,{initialBlocks:e,onInput:m,onChange:m,onClose:d,name:s}))}}}]);