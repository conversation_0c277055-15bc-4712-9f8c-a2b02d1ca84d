"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[5941],{5214:(e,t,s)=>{s.r(t),s.d(t,{default:()=>D});var o=s(27723),r=s(56427),a=s(86087),i=s(29491),c=s(47143),n=s(98846),l=s(40314),m=s(83306),d=s(15838),p=s(66087),h=s(39793);class u extends a.Component{constructor(e){super(e),this.state={disabled:!1}}renderInput=()=>{const{handleChange:e,name:t,inputText:s,inputType:o,options:a,value:i,component:c}=this.props,{disabled:n}=this.state;switch(o){case"checkboxGroup":return a.map((e=>e.options.length>0&&(0,h.jsxs)("div",{className:"woocommerce-setting__options-group","aria-labelledby":t+"-label",children:[e.label&&(0,h.jsx)("span",{className:"woocommerce-setting__options-group-label",children:e.label}),this.renderCheckboxOptions(e.options)]},e.key)));case"checkbox":return this.renderCheckboxOptions(a);case"button":return(0,h.jsx)(r.Button,{isSecondary:!0,onClick:this.handleInputCallback,disabled:n,children:s});case"component":const o=c;return(0,h.jsx)(o,{value:i,onChange:e,...this.props});case"select":return(0,h.jsx)(r.SelectControl,{options:a,value:i,onChange:s=>e({target:{name:t,type:"select",value:s}})});default:const l=(0,p.uniqueId)(t);return(0,h.jsx)("input",{id:l,type:"text",name:t,onChange:e,value:i,placeholder:s,disabled:n})}};handleInputCallback=()=>{const{createNotice:e,callback:t}=this.props;if("function"==typeof t)return new Promise(((s,o)=>{this.setState({disabled:!0}),t(s,o,e)})).then((()=>{this.setState({disabled:!1})})).catch((()=>{this.setState({disabled:!1})}))};renderCheckboxOptions(e){const{handleChange:t,name:s,value:o}=this.props,{disabled:a}=this.state;return e.map((e=>(0,h.jsx)(r.CheckboxControl,{label:e.label,name:s,checked:o&&o.includes(e.value),onChange:o=>t({target:{checked:o,name:s,type:"checkbox",value:e.value}}),disabled:a},s+"-"+e.value)))}render(){const{helpText:e,label:t,name:s}=this.props;return(0,h.jsxs)("div",{className:"woocommerce-setting",children:[(0,h.jsx)("div",{className:"woocommerce-setting__label",id:s+"-label",children:t}),(0,h.jsxs)("div",{className:"woocommerce-setting__input",children:[this.renderInput(),e&&(0,h.jsx)("span",{className:"woocommerce-setting__help",children:e})]})]})}}const g=(0,i.compose)((0,c.withDispatch)((e=>{const{createNotice:t}=e("core/notices");return{createNotice:t}})))(u);var _=s(76154),w=s.n(_);const v=(e,t,s)=>{const o={};if(s&&(o.skip_existing=!0),"all"!==t.label)if("custom"===t.label){const s=w()().diff(w()(t.date,e),"days",!0);o.days=Math.floor(s)}else o.days=parseInt(t.label,10);return o};var S=s(93832);const I=(0,i.compose)([(0,c.withSelect)((e=>{const{getFormSettings:t}=e(l.importStore),{period:s,skipPrevious:o}=t();return{selectedPeriod:s,skipChecked:o}})),(0,c.withDispatch)((e=>{const{updateImportation:t,setImportStarted:s}=e(l.importStore),{createNotice:o}=e("core/notices");return{createNotice:o,setImportStarted:s,updateImportation:t}}))])((function({clearStatusAndTotalsCache:e,createNotice:t,dateFormat:s,importDate:i,onImportStarted:c,selectedPeriod:n,stopImport:l,skipChecked:d,status:p,setImportStarted:u,updateImportation:g}){const _=(e,s,o=!1)=>{g(e,o).then((e=>{"success"===e.status?t("success",e.message):(t("error",s),u(!1),l())})).catch((e=>{e&&e.message&&(t("error",e.message),u(!1),l())}))},w=()=>{const e=(0,S.addQueryArgs)("/wc-analytics/reports/import",v(s,n,d)),t=(0,o.__)("There was a problem rebuilding your report data.","woocommerce");_(e,t,!0),c()},I=()=>{l();const e=(0,o.__)("There was a problem stopping your current import.","woocommerce");_("/wc-analytics/reports/import/cancel",e)},b=()=>{const e=(0,o.__)("There was a problem deleting your previous data.","woocommerce");_("/wc-analytics/reports/import/delete",e),(0,m.recordEvent)("analytics_import_delete_previous"),u(!1)},x=()=>{u(!1),e()};return(0,h.jsx)("div",{className:"woocommerce-settings__actions woocommerce-settings-historical-data__actions",children:(()=>{const e="ready"!==p;return["initializing","customers","orders","finalizing"].includes(p)?(0,h.jsxs)(a.Fragment,{children:[(0,h.jsx)(r.Button,{className:"woocommerce-settings-historical-data__action-button",isPrimary:!0,onClick:I,children:(0,o.__)("Stop Import","woocommerce")}),(0,h.jsxs)("div",{className:"woocommerce-setting__help woocommerce-settings-historical-data__action-help",children:[(0,o.__)("Imported data will not be lost if the import is stopped.","woocommerce"),(0,h.jsx)("br",{}),(0,o.__)("Navigating away from this page will not affect the import.","woocommerce")]})]}):["ready","nothing"].includes(p)?i?(0,h.jsxs)(a.Fragment,{children:[(0,h.jsx)(r.Button,{isPrimary:!0,onClick:w,disabled:e,children:(0,o.__)("Start","woocommerce")}),(0,h.jsx)(r.Button,{isSecondary:!0,onClick:b,children:(0,o.__)("Delete Previously Imported Data","woocommerce")})]}):(0,h.jsx)(a.Fragment,{children:(0,h.jsx)(r.Button,{isPrimary:!0,onClick:w,disabled:e,children:(0,o.__)("Start","woocommerce")})}):("error"===p&&t("error",(0,o.__)("Something went wrong with the importation process.","woocommerce")),(0,h.jsxs)(a.Fragment,{children:[(0,h.jsx)(r.Button,{isSecondary:!0,onClick:x,children:(0,o.__)("Re-import Data","woocommerce")}),(0,h.jsx)(r.Button,{isSecondary:!0,onClick:b,children:(0,o.__)("Delete Previously Imported Data","woocommerce")})]}))})()})}));var b=s(77374);const x=(0,c.withDispatch)((e=>{const{setImportPeriod:t}=e(l.importStore);return{setImportPeriod:t}}))((function({dateFormat:e,disabled:t,setImportPeriod:s,value:a}){const i=t=>{const o=!0;t.date&&t.date.isValid?s(t.date.format(e),o):s(t.text,o)},c=t=>t.isValid()&&a.date.length===e.length?t.isAfter(new Date,"day")?b.dateValidationMessages.future:null:b.dateValidationMessages.invalid;return(0,h.jsxs)("div",{className:"woocommerce-settings-historical-data__columns",children:[(0,h.jsx)("div",{className:"woocommerce-settings-historical-data__column",children:(0,h.jsx)(r.SelectControl,{label:(0,o.__)("Import historical data","woocommerce"),value:a.label,disabled:t,onChange:e=>{s(e)},options:[{label:"All",value:"all"},{label:"Last 365 days",value:"365"},{label:"Last 90 days",value:"90"},{label:"Last 30 days",value:"30"},{label:"Last 7 days",value:"7"},{label:"Last 24 hours",value:"1"},{label:"Custom",value:"custom"}]})}),"custom"===a.label&&(()=>{const s=w()(a.date,e);return(0,h.jsxs)("div",{className:"woocommerce-settings-historical-data__column",children:[(0,h.jsx)("div",{className:"woocommerce-settings-historical-data__column-label",children:(0,o.__)("Beginning on","woocommerce")}),(0,h.jsx)(n.DatePicker,{date:s.isValid()?s.toDate():null,dateFormat:e,disabled:t,error:c(s),isInvalidDate:e=>w()(e).isAfter(new Date,"day"),onUpdate:i,text:a.date})]})})()]})})),C=function({label:e,progress:t,total:s}){const r=(0,o.sprintf)((0,o.__)("Imported %(label)s","woocommerce"),{label:e}),a=(0,p.isNil)(s)?null:(0,o.sprintf)((0,o.__)("%(progress)s of %(total)s","woocommerce"),{progress:t||0,total:s});return(0,h.jsxs)("div",{className:"woocommerce-settings-historical-data__progress",children:[(0,h.jsx)("span",{className:"woocommerce-settings-historical-data__progress-label",children:r}),a&&(0,h.jsx)("span",{className:"woocommerce-settings-historical-data__progress-label",children:a}),(0,h.jsx)("progress",{className:"woocommerce-settings-historical-data__progress-bar",max:s,value:t||0})]})};var k=s(52619);const j=function({importDate:e,status:t}){const s=(0,k.applyFilters)("woocommerce_admin_import_status",{nothing:(0,o.__)("Nothing To Import","woocommerce"),ready:(0,o.__)("Ready To Import","woocommerce"),initializing:[(0,o.__)("Initializing","woocommerce"),(0,h.jsx)(r.Spinner,{},"spinner")],customers:[(0,o.__)("Importing Customers","woocommerce"),(0,h.jsx)(r.Spinner,{},"spinner")],orders:[(0,o.__)("Importing Orders","woocommerce"),(0,h.jsx)(r.Spinner,{},"spinner")],finalizing:[(0,o.__)("Finalizing","woocommerce"),(0,h.jsx)(r.Spinner,{},"spinner")],finished:-1===e?(0,o.__)("All historical data imported","woocommerce"):(0,o.sprintf)((0,o.__)("Historical data from %s onward imported","woocommerce"),w()(e).format("YYYY-MM-DD"))});return(0,h.jsxs)("span",{className:"woocommerce-settings-historical-data__status",children:[(0,o.__)("Status:","woocommerce")+" ",s[t]]})},y=(0,c.withDispatch)((e=>{const{setSkipPrevious:t}=e(l.importStore);return{setSkipPrevious:t}}))((function({checked:e,disabled:t,setSkipPrevious:s}){return(0,h.jsx)(r.CheckboxControl,{className:"woocommerce-settings-historical-data__skip-checkbox",checked:e,disabled:t,label:(0,o.__)("Skip previously imported customers and orders","woocommerce"),onChange:e=>{s(e)}})}));class N extends a.Component{render(){const{customersProgress:e,customersTotal:t,dateFormat:s,importDate:r,inProgress:i,lastImportStartTimestamp:c,clearStatusAndTotalsCache:l,ordersProgress:m,ordersTotal:d,onImportStarted:p,period:u,stopImport:g,skipChecked:_,status:w}=this.props;return(0,h.jsxs)(a.Fragment,{children:[(0,h.jsx)(n.SectionHeader,{title:(0,o.__)("Import historical data","woocommerce")}),(0,h.jsx)("div",{className:"woocommerce-settings__wrapper",children:(0,h.jsx)("div",{className:"woocommerce-setting",children:(0,h.jsxs)("div",{className:"woocommerce-setting__input",children:[(0,h.jsx)("span",{className:"woocommerce-setting__help",children:(0,o.__)("This tool populates historical analytics data by processing customers and orders created prior to activating WooCommerce Admin.","woocommerce")}),"finished"!==w&&(0,h.jsxs)(a.Fragment,{children:[(0,h.jsx)(x,{dateFormat:s,disabled:i,value:u}),(0,h.jsx)(y,{disabled:i,checked:_}),(0,h.jsx)(C,{label:(0,o.__)("Registered Customers","woocommerce"),progress:e,total:t}),(0,h.jsx)(C,{label:(0,o.__)("Orders and Refunds","woocommerce"),progress:m,total:d})]}),(0,h.jsx)(j,{importDate:r,status:w})]})})}),(0,h.jsx)(I,{clearStatusAndTotalsCache:l,dateFormat:s,importDate:r,lastImportStartTimestamp:c,onImportStarted:p,stopImport:g,status:w})]})}}const f=(0,c.withSelect)(((e,t)=>{const{getImportError:s,getImportStatus:o,getImportTotals:r}=e(l.importStore),{activeImport:a,cacheNeedsClearing:i,dateFormat:c,inProgress:n,onImportStarted:m,onImportFinished:d,period:h,startStatusCheckInterval:u,skipChecked:g}=t,_=v(c,h,g),{customers:w,orders:S,lastImportStartTimestamp:I}=r(_),{customers:b,imported_from:x,is_importing:C,orders:k}=o(I),{imported:j,total:y}=b||{},{imported:N,total:f}=k||{},T=Boolean(s(I)||s(_));Boolean(!I&&!n&&!0===C)&&m();const P=Boolean(n&&!i&&!1===C&&(y>0||f>0)&&j===y&&N===f);let D={customersTotal:w,isError:T,ordersTotal:S};a&&(D={cacheNeedsClearing:i,customersProgress:j,customersTotal:(0,p.isNil)(y)?w:y,inProgress:n,isError:T,ordersProgress:N,ordersTotal:(0,p.isNil)(f)?S:f});const F=(({cacheNeedsClearing:e,customersProgress:t,customersTotal:s,isError:o,inProgress:r,ordersProgress:a,ordersTotal:i})=>o?"error":r?(0,p.isNil)(t)||(0,p.isNil)(a)||(0,p.isNil)(s)||(0,p.isNil)(i)||e?"initializing":t<s?"customers":a<i?"orders":"finalizing":s>0||i>0?t===s&&a===i?"finished":"ready":"nothing")(D);return"initializing"===F&&u(),P&&d(),{...D,importDate:x,status:F}}))(N);class T extends a.Component{constructor(){super(...arguments),this.dateFormat=(0,o.__)("MM/DD/YYYY","woocommerce"),this.intervalId=-1,this.lastImportStopTimestamp=0,this.cacheNeedsClearing=!0,this.onImportFinished=this.onImportFinished.bind(this),this.onImportStarted=this.onImportStarted.bind(this),this.clearStatusAndTotalsCache=this.clearStatusAndTotalsCache.bind(this),this.stopImport=this.stopImport.bind(this),this.startStatusCheckInterval=this.startStatusCheckInterval.bind(this),this.cancelStatusCheckInterval=this.cancelStatusCheckInterval.bind(this)}startStatusCheckInterval(){this.intervalId<0&&(this.cacheNeedsClearing=!0,this.intervalId=setInterval((()=>{this.clearCache("getImportStatus")}),3*l.SECOND))}cancelStatusCheckInterval(){clearInterval(this.intervalId),this.intervalId=-1}clearCache(e,t){const{invalidateResolution:s,lastImportStartTimestamp:o}=this.props;s(e,["getImportStatus"===e?o:t]).then((()=>{this.cacheNeedsClearing=!1}))}stopImport(){this.cancelStatusCheckInterval(),this.lastImportStopTimestamp=Date.now()}onImportFinished(){const{debouncedSpeak:e}=this.props;this.cacheNeedsClearing||(e("Import complete"),this.stopImport())}onImportStarted(){const{notes:e,setImportStarted:t,updateNote:s}=this.props,o=e.find((e=>"wc-admin-historical-data"===e.name));o&&s(o.id,{status:"actioned"}),t(!0)}clearStatusAndTotalsCache(){const{selectedPeriod:e,skipChecked:t}=this.props,s=v(this.dateFormat,e,t);this.clearCache("getImportTotals",s),this.clearCache("getImportStatus")}isImportationInProgress(){const{lastImportStartTimestamp:e}=this.props;return void 0!==e&&void 0===this.lastImportStopTimestamp||e>this.lastImportStopTimestamp}render(){const{activeImport:e,createNotice:t,lastImportStartTimestamp:s,selectedPeriod:o,skipChecked:r}=this.props;return(0,h.jsx)(f,{activeImport:e,cacheNeedsClearing:this.cacheNeedsClearing,createNotice:t,dateFormat:this.dateFormat,inProgress:this.isImportationInProgress(),onImportFinished:this.onImportFinished,onImportStarted:this.onImportStarted,lastImportStartTimestamp:s,clearStatusAndTotalsCache:this.clearStatusAndTotalsCache,period:o,skipChecked:r,startStatusCheckInterval:this.startStatusCheckInterval,stopImport:this.stopImport})}}const P=(0,i.compose)([(0,c.withSelect)((e=>{const{getNotes:t}=e(l.notesStore),{getImportStarted:s,getFormSettings:o}=e(l.importStore),r=t({page:1,per_page:l.QUERY_DEFAULTS.pageSize,type:"update",status:"unactioned"}),{activeImport:a,lastImportStartTimestamp:i}=s(),{period:c,skipPrevious:n}=o();return{activeImport:a,lastImportStartTimestamp:i,notes:r,selectedPeriod:c,skipChecked:n}})),(0,c.withDispatch)((e=>{const{updateNote:t}=e(l.notesStore),{invalidateResolution:s,setImportStarted:o}=e(l.importStore);return{invalidateResolution:s,setImportStarted:o,updateNote:t}})),r.withSpokenMessages])(T),D=(0,i.compose)((0,c.withDispatch)((e=>{const{createNotice:t}=e("core/notices");return{createNotice:t}})))((({createNotice:e,query:t})=>{const{settingsError:s,isRequesting:i,isDirty:c,persistSettings:p,updateAndPersistSettings:u,updateSettings:_,wcAdminSettings:w}=(0,l.useSettings)("wc_admin",["wcAdminSettings"]),v=(0,a.useRef)(!1);(0,a.useEffect)((()=>{function e(e){if(c)return e.returnValue=(0,o.__)("You have unsaved changes. If you proceed, they will be lost.","woocommerce"),e.returnValue}return window.addEventListener("beforeunload",e),()=>window.removeEventListener("beforeunload",e)}),[c]),(0,a.useEffect)((()=>{i?v.current=!0:!i&&v.current&&(s?e("error",(0,o.__)("There was an error saving your settings. Please try again.","woocommerce")):e("success",(0,o.__)("Your settings have been successfully saved.","woocommerce")),v.current=!1)}),[i,s,e]);const S=e=>{const{checked:t,name:s,type:o,value:r}=e.target,a={...w};a[s]="checkbox"===o?t?[...a[s],r]:a[s].filter((e=>e!==r)):r,_("wcAdminSettings",a)};return(0,h.jsxs)(a.Fragment,{children:[(0,h.jsx)(n.SectionHeader,{title:(0,o.__)("Analytics settings","woocommerce")}),(0,h.jsxs)("div",{className:"woocommerce-settings__wrapper",children:[Object.keys(d.$W).map((e=>(0,h.jsx)(g,{handleChange:S,value:w[e],name:e,...d.$W[e]},e))),(0,h.jsxs)("div",{className:"woocommerce-settings__actions",children:[(0,h.jsx)(r.Button,{isSecondary:!0,onClick:()=>{if(window.confirm((0,o.__)("Are you sure you want to reset all settings to default values?","woocommerce"))){const e=Object.keys(d.$W).reduce(((e,t)=>(e[t]=d.$W[t].defaultValue,e)),{});u("wcAdminSettings",e),(0,m.recordEvent)("analytics_settings_reset_defaults")}},children:(0,o.__)("Reset defaults","woocommerce")}),(0,h.jsx)(r.Button,{isPrimary:!0,isBusy:i,onClick:()=>{p(),(0,m.recordEvent)("analytics_settings_save",w),t.period=void 0,t.compare=void 0,t.before=void 0,t.after=void 0,t.interval=void 0,t.type=void 0,window.wpNavMenuUrlUpdate(t)},children:(0,o.__)("Save settings","woocommerce")})]})]}),"true"===t.import?(0,h.jsx)(n.ScrollTo,{offset:"-56",children:(0,h.jsx)(P,{createNotice:e})}):(0,h.jsx)(P,{createNotice:e})]})}))}}]);