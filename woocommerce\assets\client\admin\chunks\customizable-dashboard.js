"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[3404],{39194:(e,t,o)=>{o.d(t,{A:()=>s});var n=o(5573),r=o(39793);const s=(0,r.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})})},88711:(e,t,o)=>{o.d(t,{A:()=>f});var n=o(86087),r=o(29491),s=o(66087),i=o(47143),a=o(27752),c=o(98846),l=o(40314),d=o(77374),h=o(83306),u=o(94111),p=o(56109),m=o(39793);class v extends n.Component{constructor(){super(),this.onDateSelect=this.onDateSelect.bind(this),this.onFilterSelect=this.onFilterSelect.bind(this),this.onAdvancedFilterAction=this.onAdvancedFilterAction.bind(this)}onDateSelect(e){const{report:t,addCesSurveyForAnalytics:o}=this.props;o(),(0,h.recordEvent)("datepicker_update",{report:t,...(0,s.omitBy)(e,s.isUndefined)})}onFilterSelect(e){const{report:t,addCesSurveyForAnalytics:o}=this.props,n=e.filter||e["filter-variations"];["single_product","single_category","single_coupon","single_variation"].includes(n)&&o();const r={report:t,filter:e.filter||"all"};"single_product"===e.filter&&(r.filter_variation=e["filter-variations"]||"all"),(0,h.recordEvent)("analytics_filter",r)}onAdvancedFilterAction(e,t){const{report:o,addCesSurveyForAnalytics:n}=this.props;switch(e){case"add":(0,h.recordEvent)("analytics_filters_add",{report:o,filter:t.key});break;case"remove":(0,h.recordEvent)("analytics_filters_remove",{report:o,filter:t.key});break;case"filter":const e=Object.keys(t).reduce(((e,o)=>(e[(0,s.snakeCase)(o)]=t[o],e)),{});n(),(0,h.recordEvent)("analytics_filters_filter",{report:o,...e});break;case"clear_all":(0,h.recordEvent)("analytics_filters_clear_all",{report:o});break;case"match":(0,h.recordEvent)("analytics_filters_all_any",{report:o,value:t.match})}}render(){const{advancedFilters:e,filters:t,path:o,query:n,showDatePicker:r,defaultDateRange:s}=this.props,{period:i,compare:a,before:l,after:h}=(0,d.getDateParamsFromQuery)(n,s),{primary:u,secondary:v}=(0,d.getCurrentDates)(n,s),f={period:i,compare:a,before:l,after:h,primaryDate:u,secondaryDate:v},_=this.context;return(0,m.jsx)(c.ReportFilters,{query:n,siteLocale:p.ne.siteLocale,currency:_.getCurrencyConfig(),path:o,filters:t,advancedFilters:e,showDatePicker:r,onDateSelect:this.onDateSelect,onFilterSelect:this.onFilterSelect,onAdvancedFilterAction:this.onAdvancedFilterAction,dateQuery:f,isoDateFormat:d.isoDateFormat})}}v.contextType=u.CurrencyContext;const f=(0,r.compose)((0,i.withSelect)((e=>{const{woocommerce_default_date_range:t}=e(l.settingsStore).getSetting("wc_admin","wcAdminSettings");return{defaultDateRange:t}})),(0,i.withDispatch)((e=>{const{addCesSurveyForAnalytics:t}=e(a.STORE_KEY);return{addCesSurveyForAnalytics:t}})))(v)},53847:(e,t,o)=>{o.r(t),o.d(t,{default:()=>z});var n=o(27723),r=o(86087),s=o(29491),i=o(66087),a=o(56427),c=o(52619),l=o(24148),d=o(5573),h=o(39793);const u=(0,h.jsx)(d.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,h.jsx)(d.Path,{d:"M2 12C2 6.44444 6.44444 2 12 2C17.5556 2 22 6.44444 22 12C22 17.5556 17.5556 22 12 22C6.44444 22 2 17.5556 2 12ZM13 11V7H11V11H7V13H11V17H13V13H17V11H13Z"})});var p=o(47143),m=o(98846),v=o(40314),f=o(96476),_=o(77374),g=o(83306),y=o(94111);const w=(0,h.jsx)(d.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,h.jsx)(d.Path,{d:"m14.5 6.5-1 1 3.7 3.7H4v1.6h13.2l-3.7 3.7 1 1 5.6-5.5z"})}),b=(0,h.jsx)(d.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,h.jsx)(d.Path,{fillRule:"evenodd",d:"M11.25 5h1.5v15h-1.5V5zM6 10h1.5v10H6V10zm12 4h-1.5v6H18v-6z",clipRule:"evenodd"})});var x=o(47603);const k=(0,r.lazy)((()=>Promise.all([o.e(2304),o.e(1133)]).then(o.bind(o,31861)))),j=(0,r.lazy)((()=>Promise.all([o.e(3240),o.e(823)]).then(o.bind(o,39254)))),C=(0,r.lazy)((()=>o.e(6115).then(o.bind(o,92072)))),S="woocommerce_dashboard_default_sections",A=(0,c.applyFilters)(S,[{key:"store-performance",component:e=>(0,h.jsx)(r.Suspense,{fallback:(0,h.jsx)(m.Spinner,{}),children:(0,h.jsx)(C,{...e})}),title:(0,n.__)("Performance","woocommerce"),isVisible:!0,icon:w,hiddenBlocks:["coupons/amount","coupons/orders_count","downloads/download_count","taxes/order_tax","taxes/total_tax","taxes/shipping_tax","revenue/shipping","orders/avg_order_value","revenue/refunds","revenue/gross_sales"]},{key:"charts",component:e=>(0,h.jsx)(r.Suspense,{fallback:(0,h.jsx)(m.Spinner,{}),children:(0,h.jsx)(k,{...e})}),title:(0,n.__)("Charts","woocommerce"),isVisible:!0,icon:b,hiddenBlocks:["orders_avg_order_value","avg_items_per_order","products_items_sold","revenue_total_sales","revenue_refunds","coupons_amount","coupons_orders_count","revenue_shipping","taxes_total_tax","taxes_order_tax","taxes_shipping_tax","downloads_download_count"]},{key:"leaderboards",component:e=>(0,h.jsx)(r.Suspense,{fallback:(0,h.jsx)(m.Spinner,{}),children:(0,h.jsx)(j,{...e})}),title:(0,n.__)("Leaderboards","woocommerce"),isVisible:!0,icon:(0,h.jsx)(x.A,{}),hiddenBlocks:["coupons","customers"]}]);var O=o(39194),B=o(98672),F=o(49649);class D extends r.Component{constructor(e){super(e),this.onMoveUp=this.onMoveUp.bind(this),this.onMoveDown=this.onMoveDown.bind(this)}onMoveUp(){const{onMove:e,onToggle:t}=this.props;e(-1),t()}onMoveDown(){const{onMove:e,onToggle:t}=this.props;e(1),t()}render(){const{onRemove:e,isFirst:t,isLast:o,onTitleBlur:s,onTitleChange:i,titleInput:c}=this.props;return(0,h.jsxs)(r.Fragment,{children:[(0,h.jsx)("div",{className:"woocommerce-ellipsis-menu__item",children:(0,h.jsx)(a.TextControl,{label:(0,n.__)("Section title","woocommerce"),onBlur:s,onChange:i,required:!0,value:c})}),(0,h.jsxs)("div",{className:"woocommerce-dashboard-section-controls",children:[!t&&(0,h.jsxs)(m.MenuItem,{isClickable:!0,onInvoke:this.onMoveUp,children:[(0,h.jsx)(l.A,{icon:(0,h.jsx)(B.A,{}),label:(0,n.__)("Move up","woocommerce"),size:20,className:"icon-control"}),(0,n.__)("Move up","woocommerce")]}),!o&&(0,h.jsxs)(m.MenuItem,{isClickable:!0,onInvoke:this.onMoveDown,children:[(0,h.jsx)(l.A,{icon:(0,h.jsx)(F.A,{}),size:20,label:(0,n.__)("Move down","woocommerce"),className:"icon-control"}),(0,n.__)("Move down","woocommerce")]}),(0,h.jsxs)(m.MenuItem,{isClickable:!0,onInvoke:e,children:[(0,h.jsx)(l.A,{icon:O.A,size:20,label:(0,n.__)("Remove block","woocommerce"),className:"icon-control"}),(0,n.__)("Remove section","woocommerce")]})]})]})}}const M=D;class T extends r.Component{constructor(e){super(e);const{title:t}=e;this.state={titleInput:t},this.onToggleHiddenBlock=this.onToggleHiddenBlock.bind(this),this.onTitleChange=this.onTitleChange.bind(this),this.onTitleBlur=this.onTitleBlur.bind(this)}onTitleChange(e){this.setState({titleInput:e})}onTitleBlur(){const{onTitleUpdate:e,title:t}=this.props,{titleInput:o}=this.state;""===o?this.setState({titleInput:t}):e&&e(o)}onToggleHiddenBlock(e){return()=>{const t=(0,i.xor)(this.props.hiddenBlocks,[e]);this.props.onChangeHiddenBlocks(t)}}render(){const{component:e,...t}=this.props,{titleInput:o}=this.state;return(0,h.jsx)("div",{className:"woocommerce-dashboard-section",children:(0,h.jsx)(e,{onTitleChange:this.onTitleChange,onTitleBlur:this.onTitleBlur,onToggleHiddenBlock:this.onToggleHiddenBlock,titleInput:o,controls:M,...t})})}}var E=o(88711);const V=(0,c.applyFilters)("woocommerce_admin_dashboard_filters",[]),z=(0,s.compose)((0,p.withSelect)((e=>{const{woocommerce_default_date_range:t}=e(v.settingsStore).getSetting("wc_admin","wcAdminSettings");return{defaultDateRange:t}})))((({defaultDateRange:e,path:t,query:o})=>{const{updateUserPreferences:s,...c}=(0,v.useUserPreferences)(),d=(0,r.useMemo)((()=>(e=>{if(!e||!Array.isArray(e)||0===e.length)return A.reduce(((e,t)=>[...e,{...t}]),[]);if(!Array.isArray(A))throw new Error(`The \`defaultSections\` is not an array, please make sure \`${S}\` filter is used correctly.`);const t=A.map((e=>e.key)),o=e.map((e=>e.key)),n=new Set([...o,...t]),r=[];return n.forEach((t=>{const o=A.find((e=>e.key===t));if(!o)return;const n=e.find((e=>e.key===t));n&&delete n.icon,r.push({...o,...n})})),r})(c.dashboard_sections)),[c.dashboard_sections]),p=e=>{s({dashboard_sections:e})},w=(e,t)=>{const o=d.map((o=>(delete o.icon,o.key===e?{...o,...t}:o)));p(o)},b=e=>t=>{(0,g.recordEvent)("dash_section_rename",{key:e}),w(e,{title:t})},x=(e,t)=>()=>{t&&t();const o=d.findIndex((t=>e===t.key)),n=d.splice(o,1).shift();n.isVisible=!n.isVisible,d.push(n),n.isVisible?(0,g.recordEvent)("dash_section_add",{key:n.key}):(0,g.recordEvent)("dash_section_remove",{key:n.key}),p(d)},k=(e,t)=>{const o=d.splice(e,1).shift(),n=e+t;if(d[t<0?n:n-1].isVisible||0===e||e===d.length-1){d.splice(n,0,o),p(d);const e={key:o.key,direction:t>0?"down":"up"};(0,g.recordEvent)("dash_section_order_change",e)}else k(e,t+t)},j=()=>{const e=d.filter((e=>!1===e.isVisible));return 0===e.length?null:(0,h.jsx)(a.Dropdown,{className:"woocommerce-dashboard-section__add-more",renderToggle:({onToggle:e,isOpen:t})=>(0,h.jsx)(a.Button,{onClick:e,title:(0,n.__)("Add more sections","woocommerce"),"aria-expanded":t,children:(0,h.jsx)(l.A,{icon:u})}),renderContent:({onToggle:t})=>(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(m.H,{children:(0,n.__)("Dashboard Sections","woocommerce")}),(0,h.jsx)("div",{className:"woocommerce-dashboard-section__add-more-choices",children:e.map((e=>(0,h.jsxs)(a.Button,{onClick:x(e.key,t),className:"woocommerce-dashboard-section__add-more-btn",title:(0,n.sprintf)((0,n.__)("Add %s section","woocommerce"),e.title),children:[(0,h.jsx)(l.A,{className:e.key+"__icon",icon:e.icon,size:30}),(0,h.jsx)("span",{className:"woocommerce-dashboard-section__add-more-btn-title",children:e.title})]},e.key)))})]})})};return(0,h.jsx)(y.CurrencyContext.Provider,{value:(0,y.getFilteredCurrencyInstance)((0,f.getQuery)()),children:(()=>{const{period:n,compare:r,before:s,after:a}=(0,_.getDateParamsFromQuery)(o,e),{primary:c,secondary:l}=(0,_.getCurrentDates)(o,e),u={period:n,compare:r,before:s,after:a,primaryDate:c,secondaryDate:l},p=d.filter((e=>e.isVisible)).map((e=>e.key));return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(E.A,{report:"dashboard",query:o,path:t,dateQuery:u,isoDateFormat:_.isoDateFormat,filters:V}),d.map(((n,r)=>{return n.isVisible?(0,h.jsx)(T,{component:n.component,hiddenBlocks:n.hiddenBlocks,onChangeHiddenBlocks:(s=n.key,e=>{w(s,{hiddenBlocks:e})}),onTitleUpdate:b(n.key),path:t,defaultDateRange:e,query:o,title:n.title,onMove:(0,i.partial)(k,r),onRemove:x(n.key),isFirst:n.key===p[0],isLast:n.key===p[p.length-1],filters:V},n.key):null;var s})),j()]})})()})}))},49649:(e,t,o)=>{t.A=function(e){var t=e.size,o=void 0===t?24:t,n=e.onClick,a=(e.icon,e.className),c=function(e,t){if(null==e)return{};var o,n,r=function(e,t){if(null==e)return{};var o,n,r={},s=Object.keys(e);for(n=0;n<s.length;n++)o=s[n],0<=t.indexOf(o)||(r[o]=e[o]);return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)o=s[n],0<=t.indexOf(o)||Object.prototype.propertyIsEnumerable.call(e,o)&&(r[o]=e[o])}return r}(e,s),l=["gridicon","gridicons-chevron-down",a,!1,!1,!1].filter(Boolean).join(" ");return r.default.createElement("svg",i({className:l,height:o,width:o,onClick:n},c,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),r.default.createElement("g",null,r.default.createElement("path",{d:"M20 9l-8 8-8-8 1.414-1.414L12 14.172l6.586-6.586z"})))};var n,r=(n=o(51609))&&n.__esModule?n:{default:n},s=["size","onClick","icon","className"];function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t,o=1;o<arguments.length;o++)for(var n in t=arguments[o])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},i.apply(this,arguments)}},98672:(e,t,o)=>{t.A=function(e){var t=e.size,o=void 0===t?24:t,n=e.onClick,a=(e.icon,e.className),c=function(e,t){if(null==e)return{};var o,n,r=function(e,t){if(null==e)return{};var o,n,r={},s=Object.keys(e);for(n=0;n<s.length;n++)o=s[n],0<=t.indexOf(o)||(r[o]=e[o]);return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)o=s[n],0<=t.indexOf(o)||Object.prototype.propertyIsEnumerable.call(e,o)&&(r[o]=e[o])}return r}(e,s),l=["gridicon","gridicons-chevron-up",a,!1,!1,!1].filter(Boolean).join(" ");return r.default.createElement("svg",i({className:l,height:o,width:o,onClick:n},c,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),r.default.createElement("g",null,r.default.createElement("path",{d:"M4 15l8-8 8 8-1.414 1.414L12 9.828l-6.586 6.586z"})))};var n,r=(n=o(51609))&&n.__esModule?n:{default:n},s=["size","onClick","icon","className"];function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t,o=1;o<arguments.length;o++)for(var n in t=arguments[o])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},i.apply(this,arguments)}},47603:(e,t,o)=>{t.A=function(e){var t=e.size,o=void 0===t?24:t,n=e.onClick,a=(e.icon,e.className),c=function(e,t){if(null==e)return{};var o,n,r=function(e,t){if(null==e)return{};var o,n,r={},s=Object.keys(e);for(n=0;n<s.length;n++)o=s[n],0<=t.indexOf(o)||(r[o]=e[o]);return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)o=s[n],0<=t.indexOf(o)||Object.prototype.propertyIsEnumerable.call(e,o)&&(r[o]=e[o])}return r}(e,s),l=["gridicon","gridicons-list-ordered",a,!!function(e){return 0==e%18}(o)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return r.default.createElement("svg",i({className:l,height:o,width:o,onClick:n},c,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),r.default.createElement("g",null,r.default.createElement("path",{d:"M8 19h13v-2H8v2zm0-6h13v-2H8v2zm0-8v2h13V5H8zm-4.425.252c.107-.096.197-.188.269-.275-.012.228-.018.48-.018.756V8h1.175V3.717H3.959L2.488 4.915l.601.738.486-.401zm.334 7.764c.475-.426.785-.715.93-.867.146-.152.262-.297.35-.435.088-.138.153-.278.195-.42.042-.143.063-.298.063-.466 0-.225-.06-.427-.18-.608s-.289-.32-.507-.417a1.775 1.775 0 00-.742-.148c-.221 0-.419.022-.596.067s-.34.11-.491.195c-.15.085-.336.226-.557.423l.636.744c.174-.15.33-.264.467-.341a.835.835 0 01.409-.116.44.44 0 01.305.097.335.335 0 01.108.264c0 .09-.018.176-.054.258-.036.082-.1.18-.192.294-.092.114-.287.328-.586.64l-1.046 1.058V14h3.108v-.955h-1.62v-.029zm.53 4.746v-.018c.307-.086.541-.225.703-.414.162-.191.243-.419.243-.685a.839.839 0 00-.378-.727c-.252-.176-.6-.264-1.043-.264-.307 0-.579.033-.816.1s-.469.178-.696.334l.48.773c.293-.184.576-.275.85-.275.147 0 .263.027.35.082s.13.139.13.252c0 .301-.294.451-.882.451h-.27v.87h.264c.217 0 .393.016.527.049.135.031.232.08.293.143.061.064.091.154.091.271 0 .152-.058.264-.174.336-.116.07-.301.106-.555.106a2.3 2.3 0 01-.538-.069 2.502 2.502 0 01-.573-.212v.961c.228.088.441.148.637.182.196.033.41.05.64.05.561 0 .998-.114 1.314-.343.315-.228.473-.542.473-.94.003-.585-.355-.923-1.07-1.013z"})))};var n,r=(n=o(51609))&&n.__esModule?n:{default:n},s=["size","onClick","icon","className"];function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t,o=1;o<arguments.length;o++)for(var n in t=arguments[o])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},i.apply(this,arguments)}}}]);