"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[12],{5639:(e,o,r)=>{r.r(o),r.d(o,{default:()=>k});var a=r(30749),c=r(70910),n=r(2328),s=r(27723),t=r(94530),l=r(50559),i=r(42174),d=r(86087),u=r(4921),m=r(98889),p=r(25656),h=r(39946),b=r(55668),v=r(10790);const k=({children:e,className:o=""})=>{const{cartTotals:r}=(0,n.V)(),{isLarge:k}=(0,p.G)(),[g,w]=(0,d.useState)(!1),x=(0,c.getCurrencyFromPriceResponse)(r),_=parseInt(r.total_price,10),y=(0,d.useId)(),f=k?{}:{role:"button",onClick:()=>w(!g),"aria-expanded":g,"aria-controls":y,tabIndex:0,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||w(!g)}};return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsxs)("div",{className:o,children:[(0,v.jsxs)("div",{className:(0,u.A)("wc-block-components-checkout-order-summary__title",{"is-open":g}),...f,children:[(0,v.jsx)("p",{className:"wc-block-components-checkout-order-summary__title-text",role:"heading",children:(0,s.__)("Order summary","woocommerce")}),!k&&(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(h.qk,{currency:x,value:_}),(0,v.jsx)(t.A,{className:"wc-block-components-checkout-order-summary__title-icon",icon:g?l.A:i.A})]})]}),(0,v.jsxs)("div",{className:(0,u.A)("wc-block-components-checkout-order-summary__content",{"is-open":g}),id:y,children:[e,(0,v.jsx)("div",{className:"wc-block-components-totals-wrapper",children:(0,v.jsx)(a.Ay,{currency:x,values:r})}),(0,v.jsx)(m.Xm,{})]})]}),!k&&(0,v.jsx)(m.iG,{children:(0,v.jsxs)("div",{className:`${o} checkout-order-summary-block-fill-wrapper`,children:[(0,v.jsx)(b.A,{children:(0,v.jsx)(v.Fragment,{children:(0,s.__)("Order summary","woocommerce")})}),(0,v.jsxs)("div",{className:"checkout-order-summary-block-fill",children:[e,(0,v.jsx)("div",{className:"wc-block-components-totals-wrapper",children:(0,v.jsx)(a.Ay,{currency:x,values:r})}),(0,v.jsx)(m.Xm,{})]})]})})]})}},85002:(e,o,r)=>{r.d(o,{N2:()=>a.N2});var a=r(97597);r(47143),r(47594)},97597:(e,o,r)=>{r.d(o,{N2:()=>c});var a=r(27723);const c=(e,o,r)=>o.validity.valid||o.validity.customError?o.validationMessage:(r||((e,o)=>r=>{const c=(e=>{var o;const r=(0,a.getLocaleData)();return["de","de_AT","de_CH"].includes(null!==(o=r?.[""]?.lang)&&void 0!==o?o:"en")?e:e?.toLocaleLowerCase()||(0,a.__)("field","woocommerce")})(e);let n=(0,a.sprintf)((0,a.__)("Please enter a valid %s","woocommerce"),c);if("checkbox"===o.type&&(n=(0,a.__)("Please check this box if you want to proceed.","woocommerce")),r.valueMissing||r.badInput||r.typeMismatch)return n})(e,o))(o.validity)||o.validationMessage;var n=r(89712);const s=new Map([["BA",/^([7-8]{1})([0-9]{4})$/],["GB",/^([A-Z]){1}([0-9]{1,2}|[A-Z][0-9][A-Z]|[A-Z][0-9]{2}|[A-Z][0-9]|[0-9][A-Z]){1}([ ])?([0-9][A-Z]{2}){1}|BFPO(?:\s)?([0-9]{1,4})$|BFPO(c\/o[0-9]{1,3})$/i],["IN",/^[1-9]{1}[0-9]{2}\s{0,1}[0-9]{3}$/],["JP",/^([0-9]{3})([-]?)([0-9]{4})$/],["KH",/^[0-9]{6}$/],["LI",/^(94[8-9][0-9])$/],["MN",/^[0-9]{5}(-[0-9]{4})?$/],["NI",/^[1-9]{1}[0-9]{4}$/],["NL",/^([1-9][0-9]{3})(\s?)(?!SA|SD|SS)[A-Z]{2}$/i],["SI",/^([1-9][0-9]{3})$/]]);new Map([...n.O,...s])},19048:(e,o,r)=>{r(89874)},92061:(e,o,r)=>{r(10790),r(27723)},35296:(e,o,r)=>{r(97557),r(10790)},15520:(e,o,r)=>{r.d(o,{A:()=>l});var a=r(92497),c=r(4921),n=r(15703),s=r(10790);const t=e=>{const{prefix:o,suffix:r,thousandSeparator:a,decimalSeparator:c}=e,n=a===c;return n&&console.warn("Thousand separator and decimal separator are the same. This may cause formatting issues."),{thousandSeparator:n?"":a,decimalSeparator:c,fixedDecimalScale:!0,prefix:o,suffix:r,isNumericString:!0}},l=({className:e,value:o,currency:r=n.SITE_CURRENCY,onValueChange:l,displayType:i="text",...d})=>{var u;const m={...n.SITE_CURRENCY,...r},p="string"==typeof o?parseInt(o,10):o;if(!Number.isFinite(p))return null;const h=p/10**m.minorUnit;if(!Number.isFinite(h))return null;const b=(0,c.A)("wc-block-formatted-money-amount","wc-block-components-formatted-money-amount",e),v=null!==(u=d.decimalScale)&&void 0!==u?u:m?.minorUnit,k={...d,...t(m),decimalScale:v,value:void 0,currency:void 0,onValueChange:void 0},g=l?e=>{const o=+e.value*10**m.minorUnit;l(o)}:()=>{};return(0,s.jsx)(a.A,{className:b,displayType:i,...k,value:h,onValueChange:g})}},39946:(e,o,r)=>{r.d(o,{qk:()=>h.A}),r(19048);var a=r(4921),c=r(29491),n=r(86087),s=r(10790);const t=(0,n.forwardRef)((({className:e,label:o,id:r,onChange:n,children:l,hasError:i=!1,checked:d=!1,disabled:u=!1,errorId:m,errorMessage:p,value:h,...b},v)=>{const k=(0,c.useInstanceId)(t),g=r||`checkbox-control-${k}`;return(0,s.jsx)("div",{className:(0,a.A)("wc-block-components-checkbox",{"has-error":i},e),children:(0,s.jsxs)("label",{htmlFor:g,children:[(0,s.jsx)("input",{ref:v,id:g,className:"wc-block-components-checkbox__input",type:"checkbox",onChange:e=>n(e.target.checked),"aria-invalid":!0===i,checked:d,disabled:u,value:h,...b}),(0,s.jsx)("svg",{className:"wc-block-components-checkbox__mark","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 20",children:(0,s.jsx)("path",{d:"M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"})}),o&&(0,s.jsx)("span",{className:"wc-block-components-checkbox__label",children:o}),l]})})})),l=t;var i=r(73993),d=r(47143),u=r(47594),m=r(89446),p=r(85002);(0,n.forwardRef)((({className:e,id:o,"aria-describedby":r,errorId:c,onChange:t,showError:h=!0,errorMessage:b="",checked:v=!1,customValidation:k=()=>!0,customValidityMessage:g,label:w,validateOnMount:x=!0,instanceId:_="",disabled:y=!1,...f},j)=>{const N=(0,n.useRef)(null),E=(0,n.useId)(),A=o||`textinput-${_||E}`,I=c||A,{setValidationErrors:C,clearValidationError:$}=(0,d.useDispatch)(u.validationStore),S=(0,n.useRef)(k);(0,n.useEffect)((()=>{S.current=k}),[k]);const{validationError:V,validationErrorId:M}=(0,d.useSelect)((e=>{const o=e(u.validationStore);return{validationError:o.getValidationError(I),validationErrorId:o.getValidationErrorId(I)}}),[I]),L=(0,n.useCallback)(((e=!0)=>{const o=N.current||null;null!==o&&(o.checkValidity()&&S.current(o)?$(I):C({[I]:{message:(0,p.N2)(w,o,g),hidden:e}}))}),[$,I,C,w,g]);(0,n.useImperativeHandle)(j,(function(){return{focus(){N.current?.focus()},revalidate(){L(!1)}}}),[L]),(0,n.useEffect)((()=>{x&&L(!0)}),[x,L]),(0,n.useEffect)((()=>()=>{$(I)}),[$,I]),""!==b&&(0,i.isObject)(V)&&(V.message=b);const R=V?.message&&!V?.hidden;return(0,s.jsx)(l,{className:(0,a.A)("wc-block-components-validated-checkbox-control",e,{"has-error":R}),"aria-invalid":!0===R,id:A,"aria-errormessage":h&&R&&M?M:void 0,ref:N,onChange:(0,n.useCallback)((e=>{L(!1),t(e)}),[t,L]),"aria-describedby":r,checked:v,title:"",label:w,disabled:y,...f,children:(0,s.jsx)(m.a,{propertyName:I})})})),r(27723),r(92061),r(35296);var h=r(15520),b=r(82362);r(16809);const v=({label:e,secondaryLabel:o,description:r,secondaryDescription:c,id:n,descriptionStackingDirection:t="row"})=>(0,s.jsxs)("div",{className:"wc-block-components-radio-control__option-layout",children:[(0,s.jsxs)("div",{className:"wc-block-components-radio-control__label-group",children:[e&&(0,s.jsx)("span",{id:n&&`${n}__label`,className:"wc-block-components-radio-control__label",children:e}),o&&(0,s.jsx)("span",{id:n&&`${n}__secondary-label`,className:"wc-block-components-radio-control__secondary-label",children:o})]}),(r||c)&&(0,s.jsxs)("div",{className:(0,a.A)("wc-block-components-radio-control__description-group",{"wc-block-components-radio-control__description-group--column":"column"===t}),children:[r&&(0,s.jsx)("span",{id:n&&`${n}__description`,className:"wc-block-components-radio-control__description",children:r}),c&&(0,s.jsx)("span",{id:n&&`${n}__secondary-description`,className:"wc-block-components-radio-control__secondary-description",children:c})]})]}),k=({checked:e,name:o,onChange:r,option:c,disabled:n=!1,highlightChecked:t=!1,descriptionStackingDirection:l})=>{const{value:i,label:d,description:u,secondaryLabel:m,secondaryDescription:p,content:h}=c;return(0,s.jsxs)("label",{className:(0,a.A)("wc-block-components-radio-control__option",{"wc-block-components-radio-control__option-checked":e,"wc-block-components-radio-control__option--checked-option-highlighted":e&&t}),htmlFor:`${o}-${i}`,children:[(0,s.jsx)("input",{id:`${o}-${i}`,className:"wc-block-components-radio-control__input",type:"radio",name:o,value:i,onChange:e=>r(e.target.value),checked:e,"aria-describedby":(0,a.A)({[`${o}-${i}__secondary-label`]:m,[`${o}-${i}__description`]:u,[`${o}-${i}__secondary-description`]:p,[`${o}-${i}__content`]:h}),"aria-disabled":n,onKeyDown:e=>{n&&["ArrowUp","ArrowDown","AllowLeft","ArrowRight"].includes(e.key)&&e.preventDefault()}}),(0,s.jsx)(v,{id:`${o}-${i}`,label:d,secondaryLabel:m,description:u,secondaryDescription:p,descriptionStackingDirection:l})]})};(0,c.withInstanceId)((({className:e,instanceId:o,id:r,selected:c,onChange:t,options:l=[],highlightChecked:i=!1})=>{const d=r||o,u=(0,n.useMemo)((()=>l.findIndex((e=>e.value===c))),[l,c]);return l.length?(0,s.jsx)("div",{className:(0,a.A)("wc-block-components-radio-control",{"wc-block-components-radio-control--highlight-checked":i,"wc-block-components-radio-control--highlight-checked--first-selected":i&&0===u,"wc-block-components-radio-control--highlight-checked--last-selected":i&&u===l.length-1},e),children:l.map((e=>{const o="object"==typeof e&&"content"in e,r=e.value===c,n=`radio-control-${d}`;return(0,s.jsxs)("div",{className:(0,a.A)("wc-block-components-radio-control-accordion-option",{"wc-block-components-radio-control-accordion-option--checked-option-highlighted":r&&i}),children:[(0,s.jsx)(k,{name:n,checked:r,option:e,onChange:o=>{t(o),"function"==typeof e.onChange&&e.onChange(o)}}),o&&r&&(0,s.jsx)("div",{id:`${n}-${e.value}__content`,className:(0,a.A)("wc-block-components-radio-control-accordion-content",{"wc-block-components-radio-control-accordion-content-hide":!r}),children:e.content})]},e.value)}))}):null})),(0,c.withInstanceId)((({className:e,instanceId:o,label:r="",onChange:c,options:n,screenReaderLabel:t,value:l="",readOnly:i=!1})=>{const d=`wc-block-components-sort-select__select-${o}`;return(0,s.jsxs)("div",{className:(0,a.A)("wc-block-sort-select","wc-block-components-sort-select",e),children:[(0,s.jsx)(b.A,{label:r,screenReaderLabel:t,wrapperElement:"label",wrapperProps:{className:"wc-block-sort-select__label wc-block-components-sort-select__label",htmlFor:d}}),(0,s.jsx)("select",{disabled:!!i,id:d,className:"wc-block-sort-select__select wc-block-components-sort-select__select",onChange:c,value:l,children:n&&n.map((e=>(0,s.jsx)("option",{value:e.key,children:e.label},e.key)))})]})})),r(56568),r(79135),r(6293),r(89006),r(97557),r(10493),r(77842)},82362:(e,o,r)=>{r.d(o,{A:()=>t});var a=r(86087),c=r(5269),n=r(4921),s=r(10790);const t=({label:e,screenReaderLabel:o,wrapperElement:r,wrapperProps:t={},allowHTML:l=!1})=>{let i;const d=null!=e,u=null!=o;return!d&&u?(i=r||"span",t={...t,className:(0,n.A)(t.className,"screen-reader-text")},(0,s.jsx)(i,{...t,children:o})):(i=r||a.Fragment,d&&u&&e!==o?(0,s.jsxs)(i,{...t,children:[l?(0,s.jsx)(a.RawHTML,{children:(0,c.p)(e,{tags:["b","em","i","strong","p","br","span"],attr:["style"]})}):(0,s.jsx)("span",{"aria-hidden":"true",children:e}),(0,s.jsx)("span",{className:"screen-reader-text",children:o})]}):(0,s.jsx)(i,{...t,children:e}))}},16809:(e,o,r)=>{r(86087),r(64040),r(10790)},56568:(e,o,r)=>{r(57305),r(10790)},79135:(e,o,r)=>{r(47143),r(47594),r(86087),r(692),r(27723),r(18537),r(56568),r(10790),r(29491),r(57305),r(51609)},89006:(e,o,r)=>{r.d(o,{A:()=>l});var a=r(4921),c=r(86087),n=r(18537),s=r(82362),t=(r(40700),r(10790));const l=(0,c.forwardRef)((({className:e,id:o,type:r="text",ariaLabel:l,ariaDescribedBy:i,label:d,screenReaderLabel:u,disabled:m,help:p,autoCapitalize:h="off",autoComplete:b="off",value:v="",onChange:k,required:g=!1,onBlur:w=()=>{},feedback:x,..._},y)=>{const[f,j]=(0,c.useState)(!1);return(0,t.jsxs)("div",{className:(0,a.A)("wc-block-components-text-input",e,{"is-active":f||v}),children:[(0,t.jsx)("input",{type:r,id:o,value:(0,n.decodeEntities)(v),ref:y,autoCapitalize:h,autoComplete:b,onChange:e=>{k(e.target.value)},onFocus:()=>j(!0),onBlur:e=>{w(e.target.value),j(!1)},"aria-label":l||d,disabled:m,"aria-describedby":p&&!i?o+"__help":i,required:g,..._}),(0,t.jsx)(s.A,{label:d,screenReaderLabel:u||d,wrapperElement:"label",wrapperProps:{htmlFor:o},htmlFor:o}),!!p&&(0,t.jsx)("p",{id:o+"__help",className:"wc-block-components-text-input__help",children:p}),x]})}))},6293:(e,o,r)=>{var a=r(86087),c=r(4921),n=r(73993),s=r(47143),t=r(47594),l=r(89464),i=r(29491),d=r(89006),u=(r(40700),r(89446)),m=r(85002),p=r(10790);const h=(0,a.forwardRef)((({className:e,id:o,type:r="text",ariaDescribedBy:b="",errorId:v,focusOnMount:k=!1,onChange:g,showError:w=!0,errorMessage:x="",value:_="",customValidation:y=()=>!0,customValidityMessage:f,feedback:j=null,customFormatter:N=e=>e,label:E,validateOnMount:A=!0,instanceId:I="",...C},$)=>{const[S,V]=(0,a.useState)(!0),M=(0,l.Z)(_),L=(0,a.useRef)(null),R=(0,i.useInstanceId)(h,"",I),D=void 0!==o?o:"textinput-"+R,F=void 0!==v?v:D,{setValidationErrors:B,hideValidationError:T,clearValidationError:O,showValidationError:P}=(0,s.useDispatch)(t.validationStore),Z=(0,a.useRef)(y);(0,a.useEffect)((()=>{Z.current=y}),[y]);const{validationError:H,validationErrorId:U}=(0,s.useSelect)((e=>{const o=e(t.validationStore);return{validationError:o.getValidationError(F),validationErrorId:o.getValidationErrorId(F)}}),[F]),q=(0,a.useCallback)(((e=!0)=>{const o=L.current||null;if(null===o)return;if(o.value=o.value.trim(),o.setCustomValidity(""),o.checkValidity()&&Z.current(o)&&e)return void O(F);e||P(F);const r=(0,m.N2)(E,o,f);r&&B({[F]:{message:r,hidden:e}})}),[O,F,B,E,f,P]);(0,a.useImperativeHandle)($,(function(){return{focus(){L.current?.focus()},revalidate(){q(!_)},isFocused:()=>L.current?.ownerDocument?.activeElement===L.current,setErrorMessage(e){L.current?.setCustomValidity(e)}}}),[q,_]),(0,a.useEffect)((()=>{if(_!==M&&(_||M)&&L&&null!==L.current&&L.current?.ownerDocument?.activeElement!==L.current){const e=N(L.current.value);e!==_?g(e):q(!0)}}),[q,N,_,M,g]),(0,a.useEffect)((()=>{S&&(V(!1),k&&L.current?.focus(),!A&&k||q(!0))}),[A,k,S,V,q]),(0,a.useEffect)((()=>()=>{O(F)}),[O,F]),""!==x&&(0,n.isObject)(H)&&(H.message=x);const z=H?.message&&!H?.hidden;return(0,p.jsx)(d.A,{className:(0,c.A)(e,{"has-error":z}),"aria-invalid":!0===z,id:D,"aria-errormessage":w&&z&&U?U:void 0,type:r,feedback:w&&z?(0,p.jsx)(u.a,{errorMessage:x,propertyName:F,elementId:F}):j,ref:L,onChange:e=>{T(F),q(!0);const o=N(e);o!==_&&g(o)},onBlur:()=>q(!1),"aria-describedby":b,value:_,title:"",label:E,...C})}))},97557:(e,o,r)=>{r(10790)},77842:(e,o,r)=>{r(86087),r(10790)},10493:(e,o,r)=>{r(86087),r(15520),r(10790),r(27723),r(15703)},89446:(e,o,r)=>{r.d(o,{a:()=>l});var a=r(47143),c=r(47594),n=r(94530),s=r(42098),t=r(10790);const l=({errorMessage:e="",propertyName:o="",elementId:r=""})=>{const{validationError:l,validationErrorId:i}=(0,a.useSelect)((e=>{const a=e(c.validationStore);return{validationError:a.getValidationError(o),validationErrorId:a.getValidationErrorId(r)}}),[o,r]);if(!e||"string"!=typeof e){if(!l?.message||l?.hidden)return null;e=l.message}return(0,t.jsx)("div",{className:"wc-block-components-validation-error",role:"alert",children:(0,t.jsxs)("p",{id:i,children:[(0,t.jsx)(n.A,{icon:s.A}),(0,t.jsx)("span",{children:e})]})})}},40700:()=>{}}]);