"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[8039],{99010:(e,o,s)=>{s.d(o,{O:()=>u,p:()=>h});var t=s(4921),a=s(86087),c=s(94736),n=s(76154),i=s.n(n),r=s(98846),l=s(56427),d=s(39793),m=s(66087);class _ extends a.Component{render(){const{className:e,hasAction:o,hasDate:s,hasSubtitle:a,lines:c}=this.props,n=(0,t.A)("woocommerce-activity-card is-loading",e);return(0,d.jsxs)("div",{className:n,"aria-hidden":!0,children:[(0,d.jsx)("span",{className:"woocommerce-activity-card__icon",children:(0,d.jsx)("span",{className:"is-placeholder"})}),(0,d.jsxs)("div",{className:"woocommerce-activity-card__header",children:[(0,d.jsx)("div",{className:"woocommerce-activity-card__title is-placeholder"}),a&&(0,d.jsx)("div",{className:"woocommerce-activity-card__subtitle is-placeholder"}),s&&(0,d.jsx)("div",{className:"woocommerce-activity-card__date",children:(0,d.jsx)("span",{className:"is-placeholder"})})]}),(0,d.jsx)("div",{className:"woocommerce-activity-card__body",children:(0,m.range)(c).map((e=>(0,d.jsx)("span",{className:"is-placeholder"},e)))}),o&&(0,d.jsx)("div",{className:"woocommerce-activity-card__actions",children:(0,d.jsx)("span",{className:"is-placeholder"})})]})}}_.defaultProps={hasAction:!1,hasDate:!1,hasSubtitle:!1,lines:1};const h=3524==s.j?_:null;class u extends a.Component{getCard(){const{actions:e,className:o,children:s,date:c,icon:n,subtitle:l,title:m,unread:_}=this.props,h=(0,t.A)("woocommerce-activity-card",o),u=Array.isArray(e)?e:[e],x=/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(c)?i().utc(c).fromNow():c;return(0,d.jsxs)("section",{className:h,children:[_&&(0,d.jsx)("span",{className:"woocommerce-activity-card__unread"}),n&&(0,d.jsx)("span",{className:"woocommerce-activity-card__icon","aria-hidden":!0,children:n}),m&&(0,d.jsxs)("header",{className:"woocommerce-activity-card__header",children:[(0,d.jsx)(r.H,{className:"woocommerce-activity-card__title",children:m}),l&&(0,d.jsx)("div",{className:"woocommerce-activity-card__subtitle",children:l}),x&&(0,d.jsx)("span",{className:"woocommerce-activity-card__date",children:x})]}),s&&(0,d.jsx)(r.Section,{className:"woocommerce-activity-card__body",children:s}),e&&(0,d.jsx)("footer",{className:"woocommerce-activity-card__actions",children:u.map(((e,o)=>(0,a.cloneElement)(e,{key:o})))})]})}render(){const{onClick:e}=this.props;return e?(0,d.jsx)(l.Button,{className:"woocommerce-activity-card__button",onClick:e,children:this.getCard()}):this.getCard()}}u.defaultProps={icon:(0,d.jsx)(c.A,{size:48}),unread:!1}},16421:(e,o,s)=>{s.r(o),s.d(o,{InboxPanel:()=>n,default:()=>i});var t=s(41173),a=s(57882),c=s(39793);const n=({hasAbbreviatedNotifications:e,thingsToDoNextCount:o})=>(0,c.jsxs)("div",{className:"woocommerce-notification-panels",children:[e&&(0,c.jsx)(a.sY,{thingsToDoNextCount:o}),(0,c.jsx)(t.A,{showHeader:!1})]}),i=n},41173:(e,o,s)=>{s.d(o,{A:()=>k});var t=s(27723),a=s(86087),c=s(98846),n=s(56427),i=s(40314),r=s(47143),l=s(83306),d=s(14098),m=s(14484),_=s(14908),h=s(76154),u=s.n(h),x=s(99010),p=s(46591),w=s(24060),N=s(39793);const g=({onClose:e})=>{const{createNotice:o}=(0,r.useDispatch)("core/notices"),{batchUpdateNotes:s,removeAllNotes:a}=(0,r.useDispatch)(i.notesStore);return(0,N.jsx)(N.Fragment,{children:(0,N.jsx)(n.Modal,{title:(0,t.__)("Dismiss all messages","woocommerce"),className:"woocommerce-inbox-dismiss-all-modal",onRequestClose:e,children:(0,N.jsxs)("div",{className:"woocommerce-inbox-dismiss-all-modal__wrapper",children:[(0,N.jsx)("div",{className:"woocommerce-usage-modal__message",children:(0,t.__)("Are you sure? Inbox messages will be dismissed forever.","woocommerce")}),(0,N.jsxs)("div",{className:"woocommerce-usage-modal__actions",children:[(0,N.jsx)(n.Button,{onClick:e,children:(0,t.__)("Cancel","woocommerce")}),(0,N.jsx)(n.Button,{isPrimary:!0,onClick:()=>{(async()=>{(0,l.recordEvent)("wcadmin_inbox_action_dismissall",{});try{const e=await a({status:"unactioned"});o("success",(0,t.__)("All messages dismissed","woocommerce"),{actions:[{label:(0,t.__)("Undo","woocommerce"),onClick:()=>{s(e.map((e=>e.id)),{is_deleted:0})}}]})}catch(s){o("error",(0,t.__)("Messages could not be dismissed","woocommerce")),e()}})(),e()},children:(0,t.__)("Yes, dismiss all","woocommerce")})]})]})})})},v={page:1,per_page:5,status:"unactioned",type:i.QUERY_DEFAULTS.noteTypes,orderby:"date",order:"desc",_fields:["id","name","title","content","type","status","actions","date_created","date_created_gmt","layout","image","is_deleted","is_read","locale"]},b=["en_US","en_AU","en_CA","en_GB","en_ZA"],j=u()("2022-01-11","YYYY-MM-DD").valueOf(),y=(e,o)=>{(0,l.recordEvent)("inbox_action_click",{note_name:e.name,note_title:e.title,note_content_inner_link:o})};let C=!1;const A=({hasNotes:e,isBatchUpdating:o,notes:s,onDismiss:a,onNoteActionClick:i,onNoteVisible:r,setShowDismissAllModal:h,showHeader:u=!0,loadMoreNotes:p,allNotesFetched:w,notesHaveResolved:g,unreadNotesCount:b})=>{if(o)return;if(!e)return(0,N.jsx)(x.O,{className:"woocommerce-empty-activity-card",title:(0,t.__)("Your inbox is empty","woocommerce"),icon:!1,children:(0,t.__)("As things begin to happen in your store your inbox will start to fill up. You’ll see things like achievements, new feature announcements, extension recommendations and more!","woocommerce")});C||((0,l.recordEvent)("inbox_panel_view",{total:s.length}),C=!0);const j=Object.keys(s).map((e=>s[e]));return(0,N.jsxs)(n.Card,{size:"large",children:[u&&(0,N.jsxs)(n.CardHeader,{size:"medium",children:[(0,N.jsxs)("div",{className:"woocommerce-inbox-card__header",children:[(0,N.jsx)(_.Text,{size:"20",lineHeight:"28px",variant:"title.small",children:(0,t.__)("Inbox","woocommerce")}),(0,N.jsx)(c.Badge,{count:b})]}),(0,N.jsx)(c.EllipsisMenu,{label:(0,t.__)("Inbox Notes Options","woocommerce"),renderContent:({onToggle:e})=>(0,N.jsx)("div",{className:"woocommerce-inbox-card__section-controls",children:(0,N.jsx)(n.Button,{onClick:()=>{h(!0),e()},children:(0,t.__)("Dismiss all","woocommerce")})})})]}),(0,N.jsx)(d.A,{role:"menu",children:j.map((e=>{const{id:o,is_deleted:s}=e;return s?null:(0,N.jsx)(m.A,{timeout:500,classNames:"woocommerce-inbox-message",children:(0,N.jsx)(_.InboxNoteCard,{note:e,onDismiss:a,onNoteActionClick:i,onBodyLinkClick:y,onNoteVisible:r},o)},o)}))}),w?null:g?(0,N.jsx)(n.CardFooter,{className:"woocommerce-inbox-card__footer",size:"medium",children:(0,N.jsx)(n.Button,{isPrimary:!0,onClick:()=>{p()},children:j.length>v.per_page?(0,t.__)("Show more","woocommerce"):(0,t.__)("Show older","woocommerce")})}):(0,N.jsx)(_.InboxNotePlaceholder,{className:"banner message-is-unread"})]})},k=({showHeader:e=!0})=>{const[o,s]=(0,a.useState)(v.per_page),[n,d]=(0,a.useState)(!1),[m,h]=(0,a.useState)([]),[x,y]=(0,a.useState)({}),{createNotice:C}=(0,r.useDispatch)("core/notices"),{removeNote:k,updateNote:S,triggerNoteAction:f,invalidateResolutionForStoreSelector:D}=(0,r.useDispatch)(i.notesStore),E=(0,w.s9)(),M=(0,a.useMemo)((()=>({...v,per_page:o})),[o]),{isError:B,notes:U,notesHaveResolved:Y,isBatchUpdating:H,unreadNotesCount:R}=(0,r.useSelect)((e=>{const{getNotes:o,getNotesError:s,isNotesRequesting:t,hasFinishedResolution:a}=e(i.notesStore);return{notes:o(M),unreadNotesCount:o({...v,is_read:!1,per_page:-1}).length,isError:Boolean(s("getNotes",[M])),isBatchUpdating:t("batchUpdateNotes"),notesHaveResolved:!t("batchUpdateNotes")&&a("getNotes",[M])}}));(0,a.useEffect)((()=>{Y&&U.length<o&&d(!0),Y&&U.length&&h(U.map((e=>{const o=u()(e.date_created_gmt,"YYYY-MM-DD").valueOf();return b.includes(e.locale)&&o>=j?{...e,content:(0,p.yz)(e.content,320)}:e})))}),[U,Y]);const[T,F]=(0,a.useState)(!1);if(B){const e=(0,t.__)("There was an error getting your inbox. Please try again.","woocommerce"),o=(0,t.__)("Reload","woocommerce"),s=()=>{window.location.reload()};return(0,N.jsx)(c.EmptyContent,{title:e,actionLabel:o,actionURL:null,actionCallback:s})}return Y&&!m.length?null:(0,N.jsxs)(N.Fragment,{children:[T&&(0,N.jsx)(g,{onClose:()=>{F(!1)}}),(0,N.jsxs)("div",{className:"woocommerce-homepage-notes-wrapper",children:[!Y&&!m.length&&(0,N.jsx)(c.Section,{children:(0,N.jsx)(_.InboxNotePlaceholder,{className:"banner message-is-unread"})}),(0,N.jsx)(c.Section,{children:Boolean(m.length)&&A({loadMoreNotes:()=>{(0,l.recordEvent)("inbox_action_load_more",{quantity_shown:m.length}),s(o+10)},hasNotes:(0,p.e8)(m),isBatchUpdating:H,notes:m,onDismiss:async e=>{(0,l.recordEvent)("inbox_action_dismiss",{note_name:e.name,note_title:e.title,note_name_dismiss_all:!1,note_name_dismiss_confirmation:!0,screen:E});const o=e.id;try{await k(o),D("getNotes"),C("success",(0,t.__)("Message dismissed","woocommerce"),{actions:[{label:(0,t.__)("Undo","woocommerce"),onClick:async()=>{await S(o,{is_deleted:0}),D("getNotes")}}]})}catch(e){C("error",(0,t._n)("Message could not be dismissed","Messages could not be dismissed",1,"woocommerce"))}},onNoteActionClick:(e,o)=>{f(e.id,o.id)},onNoteVisible:e=>{x[e.id]||e.is_read||(y({...x,[e.id]:!0}),setTimeout((()=>{S(e.id,{is_read:!0})}),3e3)),(0,l.recordEvent)("inbox_note_view",{note_content:e.content,note_name:e.name,note_title:e.title,note_type:e.type,screen:E})},setShowDismissAllModal:F,showHeader:e,allNotesFetched:n,notesHaveResolved:Y,unreadNotesCount:R})})]})]})}}}]);