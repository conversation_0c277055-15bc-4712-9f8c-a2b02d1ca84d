{"name": "woocommerce/checkout-additional-information-block", "version": "1.0.0", "title": "Additional information", "description": "Render additional fields in the 'Additional information' location.", "category": "woocommerce", "supports": {"align": false, "html": false, "multiple": false, "reusable": false}, "attributes": {"className": {"type": "string", "default": ""}, "lock": {"type": "object", "default": {"remove": true, "move": false}}}, "parent": ["woocommerce/checkout-fields-block"], "textdomain": "woocommerce", "$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3}