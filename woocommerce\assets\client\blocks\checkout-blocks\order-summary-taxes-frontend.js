"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[4e3],{8914:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var s=a(1616),r=a(4656),c=a(910),o=a(5460),l=a(5703),n=a(790);const u={showRateAfterTaxName:{type:"boolean",default:(0,l.getSetting)("displayCartPricesIncludingTax",!1)},lock:{type:"object",default:{remove:!0,move:!0}}},i=(0,s.withFilteredAttributes)(u)((({className:e,showRateAfterTaxName:t})=>{const{cartTotals:a}=(0,o.V)();if((0,l.getSetting)("displayCartPricesIncludingTax",!1)||parseInt(a.total_tax,10)<=0)return null;const s=(0,c.getCurrencyFromPriceResponse)(a);return(0,n.jsx)(r.TotalsWrapper,{className:e,children:(0,n.jsx)(r.TotalsTaxes,{showRateAfterTaxName:t,currency:s,values:a})})}))}}]);