/*! For license information please see command-palette-analytics.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,t,o)=>{var r=o(51609),n=Symbol.for("react.element"),i=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,o){var r,s={},l=null,d=null;for(r in void 0!==o&&(l=""+o),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(d=t.ref),t)i.call(t,r)&&!c.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===s[r]&&(s[r]=t[r]);return{$$typeof:n,type:e,key:l,ref:d,props:s,_owner:a.current}}},39793:(e,t,o)=>{e.exports=o(94931)},51609:e=>{e.exports=window.React}},t={};const o=window.wp.i18n,r=window.wp.primitives;var n=function o(r){var n=t[r];if(void 0!==n)return n.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,o),i.exports}(39793);const i=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{fillRule:"evenodd",d:"M11.25 5h1.5v15h-1.5V5zM6 10h1.5v10H6V10zm12 4h-1.5v6H18v-6z",clipRule:"evenodd"})}),a=window.wp.element,c=window.wp.plugins,s=window.wp.url,l=window.wp.commands,d=window.wp.data,w=window.wc.tracks,p=window.wp.htmlEntities;(0,c.registerPlugin)("woocommerce-analytics-commands-registration",{render:()=>{const{editedPostType:e}=(()=>{const{currentPostType:e}=(0,d.useSelect)((e=>{const t=e("core/editor");if(!t)return{currentPostType:null};const{getCurrentPostType:o}=t;return{currentPostType:o()}})),{editedPostType:t}=(0,d.useSelect)((e=>{const t=e("core/edit-site");if(!t)return{editedPostType:null};const{getEditedPostType:o}=t;return{editedPostType:o()}}));return{editedPostType:t||e}})(),t=e?e+"-editor":null;return(0,a.useEffect)((()=>{window.hasOwnProperty("wcCommandPaletteAnalytics")&&window.wcCommandPaletteAnalytics.hasOwnProperty("reports")&&Array.isArray(window.wcCommandPaletteAnalytics.reports)&&window.wcCommandPaletteAnalytics.reports.forEach((e=>{(({label:e,path:t,origin:r})=>{(({name:e,label:t,icon:o,callback:r,origin:n})=>{(0,d.dispatch)(l.store).registerCommand({name:e,label:(0,p.decodeEntities)(t),icon:o,callback:(...t)=>{(0,w.queueRecordEvent)("woocommerce_command_palette_submit",{name:e,origin:n}),r(...t)}})})({name:`woocommerce${t}`,label:(0,o.sprintf)((0,o.__)("WooCommerce Analytics: %s","woocommerce"),e),icon:i,callback:()=>{document.location=(0,s.addQueryArgs)("admin.php",{page:"wc-admin",path:t})},origin:r})})({label:e.title,path:e.path,origin:t})}))}),[t]),null}}),(window.wc=window.wc||{}).commandPaletteAnalytics={}})();