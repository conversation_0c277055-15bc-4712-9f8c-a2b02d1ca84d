{"name": "woocommerce/cart-express-payment-block", "version": "1.0.0", "title": "Express Checkout", "description": "Allow customers to breeze through with quick payment options.", "category": "woocommerce", "supports": {"align": false, "html": false, "multiple": false, "reusable": false, "inserter": false, "lock": false}, "attributes": {"showButtonStyles": {"type": "boolean", "default": false}, "buttonHeight": {"type": "string", "default": "48"}, "buttonBorderRadius": {"type": "string", "default": "4"}, "lock": {"type": "object", "default": {"remove": true, "move": true}}}, "parent": ["woocommerce/cart-totals-block"], "textdomain": "woocommerce", "$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3}