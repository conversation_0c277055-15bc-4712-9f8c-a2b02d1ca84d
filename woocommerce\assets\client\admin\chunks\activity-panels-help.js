"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[7956],{75655:(e,o,t)=>{t.d(o,{A:()=>u});var c=t(4921),m=t(86087),n=t(14908),r=(t(98846),t(39793));class i extends m.Component{render(){const{className:e,menu:o,subtitle:t,title:m,unreadMessages:i}=this.props,u=(0,c.A)({"woocommerce-layout__inbox-panel-header":t,"woocommerce-layout__activity-panel-header":!t},e),s=i||0;return(0,r.jsxs)("div",{className:u,children:[(0,r.jsxs)("div",{className:"woocommerce-layout__inbox-title",children:[(0,r.jsx)(n.Text,{size:16,weight:600,color:"#23282d",children:m}),(0,r.jsx)(n.Text,{variant:"button",weight:"600",size:"14",lineHeight:"20px",children:s>0&&(0,r.jsx)("span",{className:"woocommerce-layout__inbox-badge",children:i})})]}),(0,r.jsx)("div",{className:"woocommerce-layout__inbox-subtitle",children:t&&(0,r.jsx)(n.Text,{variant:"body.small",size:"14",lineHeight:"20px",children:t})}),o&&(0,r.jsx)("div",{className:"woocommerce-layout__activity-panel-header-menu",children:o})]})}}const u=i},85810:(e,o,t)=>{t.r(o),t.d(o,{HelpPanel:()=>x,SETUP_TASK_HELP_ITEMS_FILTER:()=>y,default:()=>S});var c=t(27723),m=t(14908),n=t(47143),r=t(86087),i=t(52619),u=t(24148),s=t(99669),a=t(45260),p=t(66087),l=t(98846),d=t(40314),_=t(86958),h=t(83306),w=t(75655),g=t(18886),k=t(39793);const y="woocommerce_admin_setup_task_help_items";function f(e,o){const{taskName:t}=e;o&&e.recordEvent("help_panel_click",{task_name:t||"homescreen",link:o.currentTarget.href})}const x=({taskName:e,recordEvent:o=h.recordEvent,...t})=>{(0,r.useEffect)((()=>{o("help_panel_open",{task_name:e||"homescreen"})}),[e,o]);const n=function(e){const o=function(e){const{taskName:o}=e;switch(o){case"products":return[{title:(0,c.__)("Adding and Managing Products","woocommerce"),link:"https://woocommerce.com/document/managing-products/?utm_source=help_panel&utm_medium=product"},{title:(0,c.__)("Import products using the CSV Importer and Exporter","woocommerce"),link:"https://woocommerce.com/document/product-csv-importer-exporter/?utm_source=help_panel&utm_medium=product"},{title:(0,c.__)("Migrate products using Cart2Cart","woocommerce"),link:"https://woocommerce.com/products/cart2cart/?utm_source=help_panel&utm_medium=product"},{title:(0,c.__)("Learn more about setting up products","woocommerce"),link:"https://woocommerce.com/documentation/plugins/woocommerce/getting-started/setup-products/?utm_source=help_panel&utm_medium=product"}];case"appearance":return[{title:(0,c.__)("Showcase your products and tailor your shopping experience using Blocks","woocommerce"),link:"https://woocommerce.com/document/woocommerce-blocks/?utm_source=help_panel&utm_medium=product"},{title:(0,c.__)("Manage Store Notice, Catalog View and Product Images","woocommerce"),link:"https://woocommerce.com/document/woocommerce-customizer/?utm_source=help_panel&utm_medium=product"},{title:(0,c.__)("How to choose and change a theme","woocommerce"),link:"https://woocommerce.com/document/choose-change-theme/?utm_source=help_panel&utm_medium=product"}];case"shipping":return function({activePlugins:e,countryCode:o}){const t="US"===o&&!e.includes("woocommerce-services")&&!e.includes("woocommerce-shipping")&&!e.includes("woocommerce-tax");return[{title:(0,c.__)("Setting up Shipping Zones","woocommerce"),link:"https://woocommerce.com/document/setting-up-shipping-zones/?utm_source=help_panel&utm_medium=product"},{title:(0,c.__)("Core Shipping Options","woocommerce"),link:"https://woocommerce.com/documentation/plugins/woocommerce/getting-started/shipping/core-shipping-options/?utm_source=help_panel&utm_medium=product"},{title:(0,c.__)("Product Shipping Classes","woocommerce"),link:"https://woocommerce.com/document/product-shipping-classes/?utm_source=help_panel&utm_medium=product"},t&&{title:(0,c.__)("WooCommerce Shipping setup and configuration","woocommerce"),link:"https://woocommerce.com/document/woocommerce-shipping-and-tax/?utm_source=help_panel&utm_medium=product#section-3"},{title:(0,c.__)("Learn more about configuring your shipping settings","woocommerce"),link:"https://woocommerce.com/document/plugins/woocommerce/getting-started/shipping/?utm_source=help_panel&utm_medium=product"}].filter(Boolean)}(e);case"tax":return function(e){const{countryCode:o,taskLists:t}=e,m=t.reduce(((e,o)=>[...e,...o.tasks]),[]).find((e=>"tax"===e.id));if(!m)return;const{additionalData:n}=m,{woocommerceTaxCountries:r=[],taxJarActivated:i,woocommerceTaxActivated:u,woocommerceShippingActivated:s}=n,a=!i&&r.includes(o)&&!u&&!s;return[{title:(0,c.__)("Setting up Taxes in WooCommerce","woocommerce"),link:"https://woocommerce.com/document/setting-up-taxes-in-woocommerce/?utm_source=help_panel&utm_medium=product"},a&&{title:(0,c.__)("Automated Tax calculation using WooCommerce Tax","woocommerce"),link:"https://woocommerce.com/document/woocommerce-services/?utm_source=help_panel&utm_medium=product#section-10"}].filter(Boolean)}(e);case"payments":return function(e){const{paymentGatewaySuggestions:o}=e;return[{title:(0,c.__)("Which Payment Option is Right for Me?","woocommerce"),link:"https://woocommerce.com/document/premium-payment-gateway-extensions/?utm_source=help_panel&utm_medium=product"},o.woocommerce_payments&&{title:(0,c.__)("WooPayments Start Up Guide","woocommerce"),link:"https://woocommerce.com/document/payments/?utm_source=help_panel&utm_medium=product"},o.woocommerce_payments&&{title:(0,c.__)("WooPayments FAQs","woocommerce"),link:"https://woocommerce.com/documentation/woocommerce-payments/woocommerce-payments-faqs/?utm_source=help_panel&utm_medium=product"},o.stripe&&{title:(0,c.__)("Stripe Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/stripe/?utm_source=help_panel&utm_medium=product"},o["ppcp-gateway"]&&{title:(0,c.__)("PayPal Checkout Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/2-0/woocommerce-paypal-payments/?utm_medium=product#section-3"},o.square_credit_card&&{title:(0,c.__)("Square - Get started","woocommerce"),link:"https://woocommerce.com/document/woocommerce-square/?utm_source=help_panel&utm_medium=product"},o.kco&&{title:(0,c.__)("Klarna - Introduction","woocommerce"),link:"https://woocommerce.com/document/klarna-checkout/?utm_source=help_panel&utm_medium=product"},o.klarna_payments&&{title:(0,c.__)("Klarna - Introduction","woocommerce"),link:"https://woocommerce.com/document/klarna-payments/?utm_source=help_panel&utm_medium=product"},o.payfast&&{title:(0,c.__)("Payfast Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/payfast-payment-gateway/?utm_source=help_panel&utm_medium=product"},o.eway&&{title:(0,c.__)("Eway Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/eway/?utm_source=help_panel&utm_medium=product"},{title:(0,c.__)("Direct Bank Transfer (BACS)","woocommerce"),link:"https://woocommerce.com/document/bacs/?utm_source=help_panel&utm_medium=product"},{title:(0,c.__)("Cash on Delivery","woocommerce"),link:"https://woocommerce.com/document/cash-on-delivery/?utm_source=help_panel&utm_medium=product"}].filter(Boolean)}(e);case"marketing":return function(e){const{activePlugins:o}=e;return[o.includes("mailpoet")&&{title:(0,c.__)("Get started with Mailpoet","woocommerce"),link:"https://kb.mailpoet.com/category/114-getting-started"},o.includes("google-listings-and-ads")&&{title:(0,c.__)("Set up Google for WooCommerce","woocommerce"),link:"https://woocommerce.com/document/google-listings-and-ads/?utm_medium=product#get-started"},o.includes("pinterest-for-woocommerce")&&{title:(0,c.__)("Set up Pinterest for WooCommerce","woocommerce"),link:"https://woocommerce.com/products/pinterest-for-woocommerce/"},o.includes("mailchimp-for-woocommerce")&&{title:(0,c.__)("Connect Mailchimp for WooCommerce","woocommerce"),link:"https://mailchimp.com/help/connect-or-disconnect-mailchimp-for-woocommerce/"},o.includes("creative-mail-by-constant-contact")&&{title:(0,c.__)("Set up Creative Mail for WooCommerce","woocommerce"),link:"https://app.creativemail.com/kb/help/WooCommerce"}].filter(Boolean)}(e);default:return[{title:(0,c.__)("Get Support","woocommerce"),link:"https://woocommerce.com/my-account/create-a-ticket/?utm_medium=product"},{title:(0,c.__)("Home Screen","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product"},{title:(0,c.__)("Inbox","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product#section-4"},{title:(0,c.__)("Stats Overview","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product#section-5"},{title:(0,c.__)("Store Management","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product#section-10"},{title:(0,c.__)("Store Setup Checklist","woocommerce"),link:"https://woocommerce.com/document/woocommerce-setup-wizard?utm_medium=product#store-setup-checklist"}]}}(e),t={title:(0,c.__)("WooCommerce Docs","woocommerce"),link:"https://woocommerce.com/documentation/?utm_source=help_panel&utm_medium=product"};o.push(t);const n=(0,i.applyFilters)(y,o,e.taskName,e);let r=Array.isArray(n)?n.filter((e=>e instanceof Object&&e.title&&e.link)):[];r.length||(r=[t]);const l=(0,p.partial)(f,e);return r.map((e=>{var o,t;return{title:(0,k.jsx)(m.Text,{as:"div",variant:"button",weight:"600",size:"14",lineHeight:"20px",children:e.title}),before:(0,k.jsx)(u.A,{icon:s.A}),after:(0,k.jsx)(u.A,{icon:a.A}),linkType:null!==(o=e.linkType)&&void 0!==o?o:"external",target:null!==(t=e.target)&&void 0!==t?t:"_blank",href:e.link,onClick:l}}))}({taskName:e,recordEvent:o,...t});return(0,k.jsxs)(r.Fragment,{children:[(0,k.jsx)(w.A,{title:(0,c.__)("Documentation","woocommerce")}),(0,k.jsx)(l.Section,{children:(0,k.jsx)(l.List,{items:n,className:"woocommerce-quick-links__list"})})]})},S=(0,_.Zz)((0,n.withSelect)((e=>{const{getSettings:o}=e(d.settingsStore),{getActivePlugins:t}=e(d.pluginsStore),{general:c={}}=o("general"),m=t(),n=e(d.onboardingStore).getPaymentGatewaySuggestions().reduce(((e,o)=>{const{id:t}=o;return e[t]=!0,e}),{}),r=e(d.onboardingStore).getTaskLists();return{activePlugins:m,countryCode:(0,g.gI)(c.woocommerce_default_country),paymentGatewaySuggestions:n,taskLists:r}})))(x)},18886:(e,o,t)=>{function c(e=""){return e?e.split(":")[0]:null}function m(e,o,t=!1,c){return function(e,o=!1,t,c){const m=[];return c?((e.product_types||[]).forEach((e=>{c[e]&&c[e].product&&(o||!t.includes(c[e].slug))&&m.push(c[e])})),m):m}(o,t,c,e).map((e=>e.id||e.product))}t.d(o,{Dr:()=>m,gI:()=>c}),t(18537)}}]);