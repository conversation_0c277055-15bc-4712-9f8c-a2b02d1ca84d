"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.Edit=Edit;const block_templates_1=require("@woocommerce/block-templates"),data_1=require("@woocommerce/data"),core_data_1=require("@wordpress/core-data"),data_2=require("@wordpress/data"),element_1=require("@wordpress/element"),i18n_1=require("@wordpress/i18n"),shipping_dimensions_image_1=require("../../../components/shipping-dimensions-image"),validation_context_1=require("../../../contexts/validation-context"),number_control_1=require("../../../components/number-control"),SHIPPING_AND_WEIGHT_MIN_VALUE=0,SHIPPING_AND_WEIGHT_MAX_VALUE=1e14;function Edit({attributes:e,clientId:t,context:n}){const o=(0,block_templates_1.useWooBlockProps)(e),[i,r]=(0,core_data_1.useEntityProp)("postType",n.postType,"dimensions"),[a,l]=(0,core_data_1.useEntityProp)("postType",n.postType,"weight"),[_]=(0,core_data_1.useEntityProp)("postType",n.postType,"virtual"),[m,s]=(0,element_1.useState)(),{dimensionUnit:c,weightUnit:d}=(0,data_2.useSelect)((e=>{const{getOption:t}=e(data_1.optionsStore);return{dimensionUnit:t("woocommerce_dimension_unit"),weightUnit:t("woocommerce_weight_unit")}}),[]);function u(t,n){return{name:`dimensions.${t}`,value:(i&&i[t])??"",onChange:e=>r({...i??{},[t]:e}),onFocus:()=>s(n),onBlur:()=>s(void 0),suffix:c,disabled:e.disabled||_,min:SHIPPING_AND_WEIGHT_MIN_VALUE,max:SHIPPING_AND_WEIGHT_MAX_VALUE}}const h=`dimensions_width-${t}`,{ref:p,error:g,validate:E}=(0,validation_context_1.useValidation)(h,(async function(){if(i?.width&&+i.width<=0)return{message:(0,i18n_1.__)("Width must be greater than zero.","woocommerce")}}),[i?.width]),w=`dimensions_length-${t}`,{ref:b,error:v,validate:I}=(0,validation_context_1.useValidation)(w,(async function(){if(i?.length&&+i.length<=0)return{message:(0,i18n_1.__)("Length must be greater than zero.","woocommerce")}}),[i?.length]),f=`dimensions_height-${t}`,{ref:N,error:A,validate:P}=(0,validation_context_1.useValidation)(f,(async function(){if(i?.height&&+i.height<=0)return{message:(0,i18n_1.__)("Height must be greater than zero.","woocommerce")}}),[i?.height]),S=`weight-${t}`,{ref:x,error:y,validate:H}=(0,validation_context_1.useValidation)(S,(async function(){if(a&&+a<=0)return{message:(0,i18n_1.__)("Weight must be greater than zero.","woocommerce")}}),[a]),G={...u("width","A"),ref:p,onBlur:E,id:h},T={...u("length","B"),ref:b,onBlur:I,id:w},W={...u("height","C"),ref:N,onBlur:P,id:f},U={id:S,name:"weight",value:a??"",onChange:l,suffix:d,ref:x,onBlur:H,disabled:e.disabled||_,min:SHIPPING_AND_WEIGHT_MIN_VALUE,max:SHIPPING_AND_WEIGHT_MAX_VALUE};return(0,element_1.createElement)("div",{...o},(0,element_1.createElement)("h4",null,(0,i18n_1.__)("Dimensions","woocommerce")),(0,element_1.createElement)("div",{className:"wp-block-columns"},(0,element_1.createElement)("div",{className:"wp-block-column"},(0,element_1.createElement)(number_control_1.NumberControl,{label:(0,element_1.createInterpolateElement)((0,i18n_1.__)("Width <Side />","woocommerce"),{Side:(0,element_1.createElement)("span",null,"A")}),error:g,...G}),(0,element_1.createElement)(number_control_1.NumberControl,{label:(0,element_1.createInterpolateElement)((0,i18n_1.__)("Length <Side />","woocommerce"),{Side:(0,element_1.createElement)("span",null,"B")}),error:v,...T}),(0,element_1.createElement)(number_control_1.NumberControl,{label:(0,element_1.createInterpolateElement)((0,i18n_1.__)("Height <Side />","woocommerce"),{Side:(0,element_1.createElement)("span",null,"C")}),error:A,...W}),(0,element_1.createElement)(number_control_1.NumberControl,{label:(0,i18n_1.__)("Weight","woocommerce"),error:y,...U})),(0,element_1.createElement)("div",{className:"wp-block-column"},(0,element_1.createElement)(shipping_dimensions_image_1.ShippingDimensionsImage,{highlight:m,className:"wp-block-woocommerce-product-shipping-dimensions-fields__dimensions-image",labels:{A:G.value?.length?G.value:void 0,B:T.value?.length?T.value:void 0,C:W.value?.length?W.value:void 0}}))))}