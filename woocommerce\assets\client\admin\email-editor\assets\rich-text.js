(()=>{var e={9347:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.applyFormat=function(e,t,r=e.start,a=e.end){const{formats:s,activeFormats:i}=e,c=s.slice();if(r===a){const e=c[r]?.find((({type:e})=>e===t.type));if(e){const n=c[r].indexOf(e);for(;c[r]&&c[r][n]===e;)c[r]=o(c[r],n,t),r--;for(a++;c[a]&&c[a][n]===e;)c[a]=o(c[a],n,t),a++}}else{let e=1/0;for(let n=r;n<a;n++)if(c[n]){c[n]=c[n].filter((({type:e})=>e!==t.type));const r=c[n].length;r<e&&(e=r)}else c[n]=[],e=0;for(let n=r;n<a;n++)c[n].splice(e,0,t)}return(0,n.normaliseFormats)({...e,formats:c,activeFormats:[...i?.filter((({type:e})=>e!==t.type))||[],t]})};var n=r(6788);function o(e,t,r){return(e=e.slice())[t]=r,e}},9354:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(3334),o=r(7751),a=r(9589),s=r(9211);t.default=e=>t=>{function r(r){const{record:i}=e.current,{ownerDocument:c}=t;if((0,o.isCollapsed)(i.current)||!t.contains(c.activeElement))return;const u=(0,a.slice)(i.current),l=(0,s.getTextContent)(u),d=(0,n.toHTMLString)({value:u});r.clipboardData.setData("text/plain",l),r.clipboardData.setData("text/html",d),r.clipboardData.setData("rich-text","true"),r.preventDefault(),"cut"===r.type&&c.execCommand("delete")}const{defaultView:i}=t.ownerDocument;return i.addEventListener("copy",r),i.addEventListener("cut",r),()=>{i.removeEventListener("copy",r),i.removeEventListener("cut",r)}}},4057:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(8558),o=r(8439);t.default=e=>t=>{function r(t){const{keyCode:r}=t,{createRecord:a,handleChange:s}=e.current;if(t.defaultPrevented)return;if(r!==n.DELETE&&r!==n.BACKSPACE)return;const i=a(),{start:c,end:u,text:l}=i;0===c&&0!==u&&u===l.length&&(s((0,o.remove)(i)),t.preventDefault())}return t.addEventListener("keydown",r),()=>{t.removeEventListener("keydown",r)}}},1248:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(8558),o=r(7751);const a=[];t.default=e=>t=>{function r(r){const{keyCode:s,shiftKey:i,altKey:c,metaKey:u,ctrlKey:l}=r;if(i||c||u||l||s!==n.LEFT&&s!==n.RIGHT)return;const{record:d,applyRecord:f,forceRender:m}=e.current,{text:p,formats:g,start:v,end:y,activeFormats:b=[]}=d.current,h=(0,o.isCollapsed)(d.current),{ownerDocument:E}=t,{defaultView:T}=E,{direction:_}=T.getComputedStyle(t),x="rtl"===_?n.RIGHT:n.LEFT,O=r.keyCode===x;if(h&&0===b.length){if(0===v&&O)return;if(y===p.length&&!O)return}if(!h)return;const w=g[v-1]||a,P=g[v]||a,C=O?w:P,M=b.every(((e,t)=>e===C[t]));let A=b.length;if(M?A<C.length&&A++:A--,A===b.length)return void(d.current._newActiveFormats=C);r.preventDefault();const j=(M?C:O?P:w).slice(0,A),F={...d.current,activeFormats:j};d.current=F,f(F),m()}return t.addEventListener("keydown",r),()=>{t.removeEventListener("keydown",r)}}},1260:(e,t,r)=>{"use strict";var n=r(9370);Object.defineProperty(t,"__esModule",{value:!0}),t.useEventListeners=function(e){const t=(0,o.useRef)(e);(0,o.useInsertionEffect)((()=>{t.current=e}));const r=(0,o.useMemo)((()=>m.map((e=>e(t)))),[t]);return(0,a.useRefEffect)((e=>{const t=r.map((t=>t(e)));return()=>{t.forEach((e=>e()))}}),[r])};var o=r(6087),a=r(9491),s=n(r(9354)),i=n(r(5408)),c=n(r(1248)),u=n(r(4057)),l=n(r(9823)),d=n(r(9414)),f=r(7656);const m=[s.default,i.default,c.default,u.default,l.default,d.default,f.preventFocusCapture]},9823:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(6807),o=r(7779);const a=new Set(["insertParagraph","insertOrderedList","insertUnorderedList","insertHorizontalRule","insertLink"]),s=[],i="data-rich-text-placeholder";t.default=e=>t=>{const{ownerDocument:r}=t,{defaultView:c}=r;let u=!1;function l(t){if(u)return;let r;t&&(r=t.inputType);const{record:n,applyRecord:s,createRecord:i,handleChange:c}=e.current;if(r&&(0===r.indexOf("format")||a.has(r)))return void s(n.current);const l=i(),{start:d,activeFormats:f=[]}=n.current;c((0,o.updateFormats)({value:l,start:d,end:l.start,formats:f}))}function d(){const{record:o,applyRecord:a,createRecord:f,onSelectionChange:m}=e.current;if("true"!==t.contentEditable)return;if(r.activeElement!==t)return void r.removeEventListener("selectionchange",d);if(u)return;const{start:p,end:g,text:v}=f(),y=o.current;if(v!==y.text)return void l();if(p===y.start&&g===y.end)return void(0===y.text.length&&0===p&&function(e){const t=e.getSelection(),{anchorNode:r,anchorOffset:n}=t;if(r.nodeType!==r.ELEMENT_NODE)return;const o=r.childNodes[n];o&&o.nodeType===o.ELEMENT_NODE&&o.hasAttribute(i)&&t.collapseToStart()}(c));const b={...y,start:p,end:g,activeFormats:y._newActiveFormats,_newActiveFormats:void 0},h=(0,n.getActiveFormats)(b,s);b.activeFormats=h,o.current=b,a(b,{domOnly:!0}),m(p,g)}function f(){u=!0,r.removeEventListener("selectionchange",d),t.querySelector(`[${i}]`)?.remove()}function m(){u=!1,l({inputType:"insertText"}),r.addEventListener("selectionchange",d)}function p(){const{record:n,isSelected:o,onSelectionChange:a,applyRecord:i}=e.current;if(!t.parentElement.closest('[contenteditable="true"]')){if(o)i(n.current,{domOnly:!0});else{const e=void 0;n.current={...n.current,start:e,end:e,activeFormats:s}}a(n.current.start,n.current.end),window.queueMicrotask(d),r.addEventListener("selectionchange",d)}}return t.addEventListener("input",l),t.addEventListener("compositionstart",f),t.addEventListener("compositionend",m),t.addEventListener("focus",p),()=>{t.removeEventListener("input",l),t.removeEventListener("compositionstart",f),t.removeEventListener("compositionend",m),t.removeEventListener("focus",p)}}},7656:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.preventFocusCapture=function(){return e=>{const{ownerDocument:t}=e,{defaultView:r}=t;let n=null;function o(t){t.defaultPrevented||t.target!==e&&t.target.contains(e)&&(n=e.getAttribute("contenteditable"),e.setAttribute("contenteditable","false"),r.getSelection().removeAllRanges())}function a(){null!==n&&(e.setAttribute("contenteditable",n),n=null)}return r.addEventListener("pointerdown",o),r.addEventListener("pointerup",a),()=>{r.removeEventListener("pointerdown",o),r.removeEventListener("pointerup",a)}}}},5408:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=()=>e=>{function t(t){const{target:r}=t;if(r===e||r.textContent&&r.isContentEditable)return;const{ownerDocument:n}=r,{defaultView:o}=n,a=o.getSelection();if(a.containsNode(r))return;const s=n.createRange(),i=r.isContentEditable?r:r.closest("[contenteditable]");s.selectNode(i),a.removeAllRanges(),a.addRange(s),t.preventDefault()}function r(r){r.relatedTarget&&!e.contains(r.relatedTarget)&&"A"===r.relatedTarget.tagName&&t(r)}return e.addEventListener("click",t),e.addEventListener("focusin",r),()=>{e.removeEventListener("click",t),e.removeEventListener("focusin",r)}}},9414:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(9058);t.default=()=>e=>{const{ownerDocument:t}=e,{defaultView:r}=t,o=r?.getSelection();let a;function s(){return o.rangeCount?o.getRangeAt(0):null}function i(e){const r="keydown"===e.type?"keyup":"pointerup";function o(){t.removeEventListener(r,i),t.removeEventListener("selectionchange",o),t.removeEventListener("input",o)}function i(){o(),(0,n.isRangeEqual)(a,s())||t.dispatchEvent(new Event("selectionchange"))}t.addEventListener(r,i),t.addEventListener("selectionchange",o),t.addEventListener("input",o),a=s()}return e.addEventListener("pointerdown",i),e.addEventListener("keydown",i),()=>{e.removeEventListener("pointerdown",i),e.removeEventListener("keydown",i)}}},2283:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){},t.useRichText=function({value:e="",selectionStart:t,selectionEnd:r,placeholder:f,onSelectionChange:m,preserveWhiteSpace:p,onChange:g,__unstableDisableFormats:v,__unstableIsSelected:y,__unstableDependencies:b=[],__unstableAfterParse:h,__unstableBeforeSerialize:E,__unstableAddInvisibleFormats:T}){const _=(0,a.useRegistry)(),[,x]=(0,n.useReducer)((()=>({}))),O=(0,n.useRef)();function w(e,{domOnly:t}={}){(0,i.apply)({value:e,current:O.current,prepareEditableTree:T,__unstableDomOnly:t,placeholder:f})}const P=(0,n.useRef)(e),C=(0,n.useRef)();function M(){P.current=e,C.current=e,e instanceof s.RichTextData||(C.current=e?s.RichTextData.fromHTMLString(e,{preserveWhiteSpace:p}):s.RichTextData.empty()),C.current={text:C.current.text,formats:C.current.formats,replacements:C.current.replacements},v&&(C.current.formats=Array(e.length),C.current.replacements=Array(e.length)),h&&(C.current.formats=h(C.current)),C.current.start=t,C.current.end=r}const A=(0,n.useRef)(!1);function j(t){if(C.current=t,w(t),v)P.current=t.text;else{const r=E?E(t):t.formats;t={...t,formats:r},P.current="string"==typeof e?(0,c.toHTMLString)({value:t,preserveWhiteSpace:p}):new s.RichTextData(t)}const{start:r,end:n,formats:o,text:a}=C.current;_.batch((()=>{m(r,n),g(P.current,{__unstableFormats:o,__unstableText:a})})),x()}function F(){M(),w(C.current)}C.current?t===C.current.start&&r===C.current.end||(A.current=y,C.current={...C.current,start:t,end:r,activeFormats:void 0}):(A.current=y,M());const N=(0,n.useRef)(!1);(0,n.useLayoutEffect)((()=>{N.current&&e!==P.current&&(F(),x())}),[e]),(0,n.useLayoutEffect)((()=>{A.current&&(O.current.ownerDocument.activeElement!==O.current&&O.current.focus(),w(C.current),A.current=!1)}),[A.current]);const L=(0,o.useMergeRefs)([O,(0,u.useDefaultStyle)(),(0,l.useBoundaryStyle)({record:C}),(0,d.useEventListeners)({record:C,handleChange:j,applyRecord:w,createRecord:function(){const{ownerDocument:{defaultView:e}}=O.current,t=e.getSelection(),r=t.rangeCount>0?t.getRangeAt(0):null;return(0,s.create)({element:O.current,range:r,__unstableIsEditableTree:!0})},isSelected:y,onSelectionChange:m,forceRender:x}),(0,o.useRefEffect)((()=>{F(),N.current=!0}),[f,...b])]);return{value:C.current,getValue:()=>C.current,onChange:j,ref:L}};var n=r(6087),o=r(9491),a=r(7143),s=r(1451),i=r(3599),c=r(3334),u=r(8592),l=r(3383),d=r(1260)},6024:(e,t,r)=>{"use strict";var n=r(9370);Object.defineProperty(t,"__esModule",{value:!0}),t.useAnchorRef=function({ref:e,value:t,settings:r={}}){(0,a.default)("`useAnchorRef` hook",{since:"6.1",alternative:"`useAnchor` hook"});const{tagName:n,className:i,name:c}=r,u=c?(0,s.getActiveFormat)(t,c):void 0;return(0,o.useMemo)((()=>{if(!e.current)return;const{ownerDocument:{defaultView:t}}=e.current,r=t.getSelection();if(!r.rangeCount)return;const o=r.getRangeAt(0);if(!u)return o;let a=o.startContainer;for(a=a.nextElementSibling||a;a.nodeType!==a.ELEMENT_NODE;)a=a.parentNode;return a.closest(n+(i?"."+i:""))}),[u,t.start,t.end,n,i])};var o=r(6087),a=n(r(4040)),s=r(9400)},9024:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useAnchor=function({editableContentElement:e,settings:t={}}){const{tagName:r,className:s,isActive:i}=t,[c,u]=(0,o.useState)((()=>a(e,r,s))),l=(0,n.usePrevious)(i);return(0,o.useLayoutEffect)((()=>{if(!e)return;function t(){u(a(e,r,s))}function n(){c.addEventListener("selectionchange",t)}function o(){c.removeEventListener("selectionchange",t)}const{ownerDocument:c}=e;return(e===c.activeElement||!l&&i||l&&!i)&&(u(a(e,r,s)),n()),e.addEventListener("focusin",n),e.addEventListener("focusout",o),()=>{o(),e.removeEventListener("focusin",n),e.removeEventListener("focusout",o)}}),[e,r,s,i,l]),c};var n=r(9491),o=r(6087);function a(e,t,r){if(!e)return;const{ownerDocument:n}=e,{defaultView:o}=n,a=o.getSelection();if(!a)return;if(!a.rangeCount)return;const s=a.getRangeAt(0);if(!s||!s.startContainer)return;const i=function(e,t,r,n){let o=e.startContainer;if(o.nodeType===o.TEXT_NODE&&e.startOffset===o.length&&o.nextSibling)for(o=o.nextSibling;o.firstChild;)o=o.firstChild;if(o.nodeType!==o.ELEMENT_NODE&&(o=o.parentElement),!o)return;if(o===t)return;if(!t.contains(o))return;const a=r+(n?"."+n:"");for(;o!==t;){if(o.matches(a))return o;o=o.parentElement}}(s,e,t,r);return i||function(e,t){return{contextElement:t,getBoundingClientRect:()=>t.contains(e.startContainer)?e.getBoundingClientRect():t.getBoundingClientRect()}}(s,e)}},3383:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useBoundaryStyle=function({record:e}){const t=(0,n.useRef)(),{activeFormats:r=[],replacements:o,start:a}=e.current,s=o[a];return(0,n.useEffect)((()=>{if(!(r&&r.length||s))return;const e="*[data-rich-text-format-boundary]",n=t.current.querySelector(e);if(!n)return;const{ownerDocument:o}=n,{defaultView:a}=o,i=`.rich-text:focus ${e} {background-color: ${a.getComputedStyle(n).color.replace(")",", 0.2)").replace("rgb","rgba")}}`,c="rich-text-boundary-style";let u=o.getElementById(c);u||(u=o.createElement("style"),u.id=c,o.head.appendChild(u)),u.innerHTML!==i&&(u.innerHTML=i)}),[r,s]),t};var n=r(6087)},8592:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useDefaultStyle=function(){return(0,n.useCallback)((e=>{e&&(e.style.whiteSpace=o,e.style.minWidth=a)}),[])};var n=r(6087);const o="pre-wrap",a="1px"},2647:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concat=function(...e){return(0,n.normaliseFormats)(e.reduce(a,(0,o.create)()))},t.mergePair=a;var n=r(6788),o=r(1451);function a(e,t){return e.formats=e.formats.concat(t.formats),e.replacements=e.replacements.concat(t.replacements),e.text+=t.text,e}},3038:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createElement=function e({implementation:t},r){return e.body||(e.body=t.createHTMLDocument("").body),e.body.innerHTML=r,e.body}},1451:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RichTextData=void 0,t.create=f,t.removeReservedCharacters=y;var n=r(7143),o=r(625),a=r(3038),s=r(2647),i=r(2773),c=r(3334),u=r(9211);function l({tagName:e,attributes:t}){let r;if(t&&t.class&&(r=(0,n.select)(o.store).getFormatTypeForClassName(t.class),r&&(t.class=` ${t.class} `.replace(` ${r.className} `," ").trim(),t.class||delete t.class)),r||(r=(0,n.select)(o.store).getFormatTypeForBareElement(e)),!r)return t?{type:e,attributes:t}:{type:e};if(r.__experimentalCreatePrepareEditableTree&&!r.__experimentalCreateOnChangeEditableValue)return null;if(!t)return{formatType:r,type:r.name,tagName:e};const a={},s={},i={...t};for(const e in r.attributes){const t=r.attributes[e];a[e]=i[t],delete i[t],void 0===a[e]&&delete a[e]}for(const e in i)s[e]=t[e];return!1===r.contentEditable&&delete s.contenteditable,{formatType:r,type:r.name,tagName:e,attributes:a,unregisteredAttributes:s}}class d{#e;static empty(){return new d}static fromPlainText(e){return new d(f({text:e}))}static fromHTMLString(e){return new d(f({html:e}))}static fromHTMLElement(e,t={}){const{preserveWhiteSpace:r=!1}=t,n=r?e:g(e),o=new d(f({element:n}));return Object.defineProperty(o,"originalHTML",{value:e.innerHTML}),o}constructor(e={formats:[],replacements:[],text:""}){this.#e=e}toPlainText(){return(0,u.getTextContent)(this.#e)}toHTMLString({preserveWhiteSpace:e}={}){return this.originalHTML||(0,c.toHTMLString)({value:this.#e,preserveWhiteSpace:e})}valueOf(){return this.toHTMLString()}toString(){return this.toHTMLString()}toJSON(){return this.toHTMLString()}get length(){return this.text.length}get formats(){return this.#e.formats}get replacements(){return this.#e.replacements}get text(){return this.#e.text}}t.RichTextData=d;for(const e of Object.getOwnPropertyNames(String.prototype))d.prototype.hasOwnProperty(e)||Object.defineProperty(d.prototype,e,{value(...t){return this.toHTMLString()[e](...t)}});function f({element:e,text:t,html:r,range:n,__unstableIsEditableTree:o}={}){return r instanceof d?{text:r.text,formats:r.formats,replacements:r.replacements}:"string"==typeof t&&t.length>0?{formats:Array(t.length),replacements:Array(t.length),text:t}:("string"==typeof r&&r.length>0&&(e=(0,a.createElement)(document,r)),"object"!=typeof e?{formats:[],replacements:[],text:""}:b({element:e,range:n,isEditableTree:o}))}function m(e,t,r,n){if(!r)return;const{parentNode:o}=t,{startContainer:a,startOffset:s,endContainer:i,endOffset:c}=r,u=e.text.length;void 0!==n.start?e.start=u+n.start:t===a&&t.nodeType===t.TEXT_NODE?e.start=u+s:o===a&&t===a.childNodes[s]?e.start=u:o===a&&t===a.childNodes[s-1]?e.start=u+n.text.length:t===a&&(e.start=u),void 0!==n.end?e.end=u+n.end:t===i&&t.nodeType===t.TEXT_NODE?e.end=u+c:o===i&&t===i.childNodes[c-1]?e.end=u+n.text.length:o===i&&t===i.childNodes[c]?e.end=u:t===i&&(e.end=u+c)}function p(e,t,r){if(!t)return;const{startContainer:n,endContainer:o}=t;let{startOffset:a,endOffset:s}=t;return e===n&&(a=r(e.nodeValue.slice(0,a)).length),e===o&&(s=r(e.nodeValue.slice(0,s)).length),{startContainer:n,startOffset:a,endContainer:o,endOffset:s}}function g(e,t=!0){const r=e.cloneNode(!0);return r.normalize(),Array.from(r.childNodes).forEach(((e,r,n)=>{if(e.nodeType===e.TEXT_NODE){let o=e.nodeValue;/[\n\t\r\f]/.test(o)&&(o=o.replace(/[\n\t\r\f]+/g," ")),-1!==o.indexOf("  ")&&(o=o.replace(/ {2,}/g," ")),0===r&&o.startsWith(" ")?o=o.slice(1):t&&r===n.length-1&&o.endsWith(" ")&&(o=o.slice(0,-1)),e.nodeValue=o}else e.nodeType===e.ELEMENT_NODE&&g(e,!1)})),r}const v="\r";function y(e){return e.replace(new RegExp(`[${i.ZWNBSP}${i.OBJECT_REPLACEMENT_CHARACTER}${v}]`,"gu"),"")}function b({element:e,range:t,isEditableTree:r}){const n={formats:[],replacements:[],text:""};if(!e)return n;if(!e.hasChildNodes())return m(n,e,t,{formats:[],replacements:[],text:""}),n;const o=e.childNodes.length;for(let a=0;a<o;a++){const c=e.childNodes[a],u=c.nodeName.toLowerCase();if(c.nodeType===c.TEXT_NODE){const v=y(c.nodeValue);m(n,c,t=p(c,t,y),{text:v}),n.formats.length+=v.length,n.replacements.length+=v.length,n.text+=v;continue}if(c.nodeType===c.COMMENT_NODE||c.nodeType===c.ELEMENT_NODE&&"SPAN"===c.tagName&&c.hasAttribute("data-rich-text-comment")){const E={formats:[,],replacements:[{type:"#comment",attributes:{"data-rich-text-comment":c.nodeType===c.COMMENT_NODE?c.nodeValue:c.getAttribute("data-rich-text-comment")}}],text:i.OBJECT_REPLACEMENT_CHARACTER};m(n,c,t,E),(0,s.mergePair)(n,E);continue}if(c.nodeType!==c.ELEMENT_NODE)continue;if(r&&"br"===u&&!c.getAttribute("data-rich-text-line-break")){m(n,c,t,{formats:[],replacements:[],text:""});continue}if("script"===u){const T={formats:[,],replacements:[{type:u,attributes:{"data-rich-text-script":c.getAttribute("data-rich-text-script")||encodeURIComponent(c.innerHTML)}}],text:i.OBJECT_REPLACEMENT_CHARACTER};m(n,c,t,T),(0,s.mergePair)(n,T);continue}if("br"===u){m(n,c,t,{formats:[],replacements:[],text:""}),(0,s.mergePair)(n,f({text:"\n"}));continue}const d=l({tagName:u,attributes:h({element:c})});if(!1===d?.formatType?.contentEditable){delete d.formatType,m(n,c,t,{formats:[],replacements:[],text:""}),(0,s.mergePair)(n,{formats:[,],replacements:[{...d,innerHTML:c.innerHTML}],text:i.OBJECT_REPLACEMENT_CHARACTER});continue}d&&delete d.formatType;const g=b({element:c,range:t,isEditableTree:r});if(m(n,c,t,g),!d||c.getAttribute("data-rich-text-placeholder"))(0,s.mergePair)(n,g);else if(0===g.text.length)d.attributes&&(0,s.mergePair)(n,{formats:[,],replacements:[d],text:i.OBJECT_REPLACEMENT_CHARACTER});else{function _(e){if(_.formats===e)return _.newFormats;const t=e?[d,...e]:[d];return _.formats=e,_.newFormats=t,t}_.newFormats=[d],(0,s.mergePair)(n,{...g,formats:Array.from(g.formats,_)})}}return n}function h({element:e}){if(!e.hasAttributes())return;const t=e.attributes.length;let r;for(let n=0;n<t;n++){const{name:t,value:o}=e.attributes[n];0!==t.indexOf("data-rich-text-")&&(r=r||{},r[/^on/i.test(t)?"data-disable-rich-text-"+t:t]=o)}return r}},9400:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getActiveFormat=function(e,t){return(0,n.getActiveFormats)(e).find((({type:e})=>e===t))};var n=r(6807)},6807:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getActiveFormats=function(e,t=[]){const{formats:r,start:o,end:a,activeFormats:s}=e;if(void 0===o)return t;if(o===a){if(s)return s;const e=r[o-1]||t,n=r[o]||t;return e.length<n.length?e:n}if(!r[o])return t;const i=r.slice(o,a),c=[...i[0]];let u=i.length;for(;u--;){const e=i[u];if(!e)return t;let r=c.length;for(;r--;){const t=c[r];e.find((e=>(0,n.isFormatEqual)(t,e)))||c.splice(r,1)}if(0===c.length)return t}return c||t};var n=r(1806)},1836:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getActiveObject=function({start:e,end:t,replacements:r,text:o}){if(e+1===t&&o[e]===n.OBJECT_REPLACEMENT_CHARACTER)return r[e]};var n=r(2773)},8136:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getFormatType=function(e){return(0,n.select)(o.store).getFormatType(e)};var n=r(7143),o=r(625)},9211:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getTextContent=function({text:e}){return e.replace(n.OBJECT_REPLACEMENT_CHARACTER,"")};var n=r(2773)},4004:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.insertObject=function(e,t,r,a){const s={formats:[,],replacements:[t],text:o.OBJECT_REPLACEMENT_CHARACTER};return(0,n.insert)(e,s,r,a)};var n=r(3382),o=r(2773)},3382:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.insert=function(e,t,r=e.start,a=e.end){const{formats:s,replacements:i,text:c}=e;"string"==typeof t&&(t=(0,n.create)({text:t}));const u=r+t.text.length;return(0,o.normaliseFormats)({formats:s.slice(0,r).concat(t.formats,s.slice(a)),replacements:i.slice(0,r).concat(t.replacements,i.slice(a)),text:c.slice(0,r)+t.text+c.slice(a),start:u,end:u})};var n=r(1451),o=r(6788)},7751:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isCollapsed=function({start:e,end:t}){if(void 0!==e&&void 0!==t)return e===t}},4091:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isEmpty=function({text:e}){return 0===e.length}},1806:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isFormatEqual=function(e,t){if(e===t)return!0;if(!e||!t)return!1;if(e.type!==t.type)return!1;const r=e.attributes,n=t.attributes;if(r===n)return!0;if(!r||!n)return!1;const o=Object.keys(r),a=Object.keys(n);if(o.length!==a.length)return!1;const s=o.length;for(let e=0;e<s;e++){const t=o[e];if(r[t]!==n[t])return!1}return!0}},9058:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isRangeEqual=function(e,t){return e===t||e&&t&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset}},6039:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.join=function(e,t=""){return"string"==typeof t&&(t=(0,n.create)({text:t})),(0,o.normaliseFormats)(e.reduce(((e,{formats:r,replacements:n,text:o})=>({formats:e.formats.concat(t.formats,r),replacements:e.replacements.concat(t.replacements,n),text:e.text+t.text+o}))))};var n=r(1451),o=r(6788)},6788:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.normaliseFormats=function(e){const t=e.formats.slice();return t.forEach(((e,r)=>{const o=t[r-1];if(o){const a=e.slice();a.forEach(((e,t)=>{const r=o[t];(0,n.isFormatEqual)(e,r)&&(a[t]=r)})),t[r]=a}})),{...e,formats:t}};var n=r(1806)},1257:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.registerFormatType=function(e,t){if("string"==typeof(t={name:e,...t}).name)if(/^[a-z][a-z0-9-]*\/[a-z][a-z0-9-]*$/.test(t.name))if((0,n.select)(o.store).getFormatType(t.name))window.console.error('Format "'+t.name+'" is already registered.');else if("string"==typeof t.tagName&&""!==t.tagName)if("string"==typeof t.className&&""!==t.className||null===t.className)if(/^[_a-zA-Z]+[a-zA-Z0-9_-]*$/.test(t.className)){if(null===t.className){const e=(0,n.select)(o.store).getFormatTypeForBareElement(t.tagName);if(e&&"core/unknown"!==e.name)return void window.console.error(`Format "${e.name}" is already registered to handle bare tag name "${t.tagName}".`)}else{const e=(0,n.select)(o.store).getFormatTypeForClassName(t.className);if(e)return void window.console.error(`Format "${e.name}" is already registered to handle class name "${t.className}".`)}if("title"in t&&""!==t.title)if("keywords"in t&&t.keywords.length>3)window.console.error('The format "'+t.name+'" can have a maximum of 3 keywords.');else{if("string"==typeof t.title)return(0,n.dispatch)(o.store).addFormatTypes(t),t;window.console.error("Format titles must be strings.")}else window.console.error('The format "'+t.name+'" must have a title.')}else window.console.error("A class name must begin with a letter, followed by any number of hyphens, underscores, letters, or numbers.");else window.console.error("Format class names must be a string, or null to handle bare elements.");else window.console.error("Format tag names must be a string.");else window.console.error("Format names must contain a namespace prefix, include only lowercase alphanumeric characters or dashes, and start with a letter. Example: my-plugin/my-custom-format");else window.console.error("Format names must be strings.")};var n=r(7143),o=r(625)},3927:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removeFormat=function(e,t,r=e.start,a=e.end){const{formats:s,activeFormats:i}=e,c=s.slice();if(r===a){const e=c[r]?.find((({type:e})=>e===t));if(e){for(;c[r]?.find((t=>t===e));)o(c,r,t),r--;for(a++;c[a]?.find((t=>t===e));)o(c,a,t),a++}}else for(let e=r;e<a;e++)c[e]&&o(c,e,t);return(0,n.normaliseFormats)({...e,formats:c,activeFormats:i?.filter((({type:e})=>e!==t))||[]})};var n=r(6788);function o(e,t,r){const n=e[t].filter((({type:e})=>e!==r));n.length?e[t]=n:delete e[t]}},8439:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.remove=function(e,t,r){return(0,n.insert)(e,(0,o.create)(),t,r)};var n=r(3382),o=r(1451)},847:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.replace=function({formats:e,replacements:t,text:r,start:o,end:a},s,i){return r=r.replace(s,((r,...n)=>{const s=n[n.length-2];let c,u,l=i;return"function"==typeof l&&(l=i(r,...n)),"object"==typeof l?(c=l.formats,u=l.replacements,l=l.text):(c=Array(l.length),u=Array(l.length),e[s]&&(c=c.fill(e[s]))),e=e.slice(0,s).concat(c,e.slice(s+r.length)),t=t.slice(0,s).concat(u,t.slice(s+r.length)),o&&(o=a=s+l.length),l})),(0,n.normaliseFormats)({formats:e,replacements:t,text:r,start:o,end:a})};var n=r(6788)},9589:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.slice=function(e,t=e.start,r=e.end){const{formats:n,replacements:o,text:a}=e;return void 0===t||void 0===r?{...e}:{formats:n.slice(t,r),replacements:o.slice(t,r),text:a.slice(t,r)}}},2773:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ZWNBSP=t.OBJECT_REPLACEMENT_CHARACTER=void 0,t.OBJECT_REPLACEMENT_CHARACTER="￼",t.ZWNBSP="\ufeff"},5247:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.split=function({formats:e,replacements:t,text:r,start:n,end:o},a){if("string"!=typeof a)return function({formats:e,replacements:t,text:r,start:n,end:o},a=n,s=o){if(void 0!==n&&void 0!==o)return[{formats:e.slice(0,a),replacements:t.slice(0,a),text:r.slice(0,a)},{formats:e.slice(s),replacements:t.slice(s),text:r.slice(s),start:0,end:0}]}(...arguments);let s=0;return r.split(a).map((r=>{const i=s,c={formats:e.slice(i,i+r.length),replacements:t.slice(i,i+r.length),text:r};return s+=a.length+r.length,void 0!==n&&void 0!==o&&(n>=i&&n<s?c.start=n-i:n<i&&o>i&&(c.start=0),o>=i&&o<s?c.end=o-i:n<s&&o>s&&(c.end=r.length)),c}))}},3346:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addFormatTypes=function(e){return{type:"ADD_FORMAT_TYPES",formatTypes:Array.isArray(e)?e:[e]}},t.removeFormatTypes=function(e){return{type:"REMOVE_FORMAT_TYPES",names:Array.isArray(e)?e:[e]}}},625:(e,t,r)=>{"use strict";var n=r(9370);Object.defineProperty(t,"__esModule",{value:!0}),t.store=void 0;var o=r(7143),a=n(r(8641)),s=u(r(2723)),i=u(r(3346));function c(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(c=function(e){return e?r:t})(e)}function u(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=c(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}const l=t.store=(0,o.createReduxStore)("core/rich-text",{reducer:a.default,selectors:s,actions:i});(0,o.register)(l)},8641:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.formatTypes=o;var n=r(7143);function o(e={},t){switch(t.type){case"ADD_FORMAT_TYPES":return{...e,...t.formatTypes.reduce(((e,t)=>({...e,[t.name]:t})),{})};case"REMOVE_FORMAT_TYPES":return Object.fromEntries(Object.entries(e).filter((([e])=>!t.names.includes(e))))}return e}t.default=(0,n.combineReducers)({formatTypes:o})},2723:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getFormatType=function(e,t){return e.formatTypes[t]},t.getFormatTypeForBareElement=function(e,t){const r=o(e);return r.find((({className:e,tagName:r})=>null===e&&t===r))||r.find((({className:e,tagName:t})=>null===e&&"*"===t))},t.getFormatTypeForClassName=function(e,t){return o(e).find((({className:e})=>null!==e&&` ${t} `.indexOf(` ${e} `)>=0))},t.getFormatTypes=void 0;var n=r(7143);const o=t.getFormatTypes=(0,n.createSelector)((e=>Object.values(e.formatTypes)),(e=>[e.formatTypes]))},3599:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.apply=function({value:e,current:t,prepareEditableTree:r,__unstableDomOnly:n,placeholder:o}){const{body:a,selection:s}=g({value:e,prepareEditableTree:r,placeholder:o,doc:t.ownerDocument});v(a,t),void 0===e.start||n||y(s,t)},t.applySelection=y,t.applyValue=v,t.toDom=g;var n=r(5379),o=r(3038),a=r(9058);function s(e,t,r){const n=e.parentNode;let o=0;for(;e=e.previousSibling;)o++;return r=[o,...r],n!==t&&(r=s(n,t,r)),r}function i(e,t){for(t=[...t];e&&t.length>1;)e=e.childNodes[t.shift()];return{node:e,offset:t[0]}}function c(e,t){if(void 0!==t.html)return e.innerHTML+=t.html;"string"==typeof t&&(t=e.ownerDocument.createTextNode(t));const{type:r,attributes:n}=t;if(r)if("#comment"===r)t=e.ownerDocument.createComment(n["data-rich-text-comment"]);else{t=e.ownerDocument.createElement(r);for(const e in n)t.setAttribute(e,n[e])}return e.appendChild(t)}function u(e,t){e.appendData(t)}function l({lastChild:e}){return e}function d({parentNode:e}){return e}function f(e){return e.nodeType===e.TEXT_NODE}function m({nodeValue:e}){return e}function p(e){return e.parentNode.removeChild(e)}function g({value:e,prepareEditableTree:t,isEditableTree:r=!0,placeholder:a,doc:i=document}){let g=[],v=[];return t&&(e={...e,formats:t(e)}),{body:(0,n.toTree)({value:e,createEmpty:()=>(0,o.createElement)(i,""),append:c,getLastChild:l,getParent:d,isText:f,getText:m,remove:p,appendText:u,onStartIndex(e,t){g=s(t,e,[t.nodeValue.length])},onEndIndex(e,t){v=s(t,e,[t.nodeValue.length])},isEditableTree:r,placeholder:a}),selection:{startPath:g,endPath:v}}}function v(e,t){let r,n=0;for(;r=e.firstChild;){const o=t.childNodes[n];if(o)if(o.isEqualNode(r))e.removeChild(r);else if(o.nodeName!==r.nodeName||o.nodeType===o.TEXT_NODE&&o.data!==r.data)t.replaceChild(r,o);else{const t=o.attributes,n=r.attributes;if(t){let e=t.length;for(;e--;){const{name:n}=t[e];r.getAttribute(n)||o.removeAttribute(n)}}if(n)for(let e=0;e<n.length;e++){const{name:t,value:r}=n[e];o.getAttribute(t)!==r&&o.setAttribute(t,r)}v(r,o),e.removeChild(r)}else t.appendChild(r);n++}for(;t.childNodes[n];)t.removeChild(t.childNodes[n])}function y({startPath:e,endPath:t},r){const{node:n,offset:o}=i(r,e),{node:s,offset:c}=i(r,t),{ownerDocument:u}=r,{defaultView:l}=u,d=l.getSelection(),f=u.createRange();f.setStart(n,o),f.setEnd(s,c);const{activeElement:m}=u;if(d.rangeCount>0){if((0,a.isRangeEqual)(f,d.getRangeAt(0)))return;d.removeAllRanges()}d.addRange(f),m!==u.activeElement&&m instanceof l.HTMLElement&&m.focus()}},3334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toHTMLString=function({value:e,preserveWhiteSpace:t}){return m((0,o.toTree)({value:e,preserveWhiteSpace:t,createEmpty:a,append:i,getLastChild:s,getParent:u,isText:l,getText:d,remove:f,appendText:c}).children)};var n=r(9877),o=r(5379);function a(){return{}}function s({children:e}){return e&&e[e.length-1]}function i(e,t){return"string"==typeof t&&(t={text:t}),t.parent=e,e.children=e.children||[],e.children.push(t),t}function c(e,t){e.text+=t}function u({parent:e}){return e}function l({text:e}){return"string"==typeof e}function d({text:e}){return e}function f(e){const t=e.parent.children.indexOf(e);return-1!==t&&e.parent.children.splice(t,1),e}function m(e=[]){return e.map((e=>void 0!==e.html?e.html:void 0===e.text?function({type:e,attributes:t,object:r,children:o}){if("#comment"===e)return`\x3c!--${t["data-rich-text-comment"]}--\x3e`;let a="";for(const e in t)(0,n.isValidAttributeName)(e)&&(a+=` ${e}="${(0,n.escapeAttribute)(t[e])}"`);return r?`<${e}${a}>`:`<${e}${a}>${m(o)}</${e}>`}(e):(0,n.escapeEditableHTML)(e.text))).join("")}},5379:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toTree=function({value:e,preserveWhiteSpace:t,createEmpty:r,append:s,getLastChild:u,getParent:l,isText:d,getText:f,remove:m,appendText:p,onStartIndex:g,onEndIndex:v,isEditableTree:y,placeholder:b}){const{formats:h,replacements:E,text:T,start:_,end:x}=e,O=h.length+1,w=r(),P=(0,n.getActiveFormats)(e),C=P[P.length-1];let M,A;s(w,"");for(let e=0;e<O;e++){const r=T.charAt(e),n=y&&(!A||"\n"===A),O=h[e];let P=u(w);if(O&&O.forEach(((e,t)=>{if(P&&M&&c(O,M,t))return void(P=u(P));const{type:r,tagName:n,attributes:o,unregisteredAttributes:a}=e,p=y&&e===C,g=l(P),v=s(g,i({type:r,tagName:n,attributes:o,unregisteredAttributes:a,boundaryClass:p,isEditableTree:y}));d(P)&&0===f(P).length&&m(P),P=s(v,"")})),0===e&&(g&&0===_&&g(w,P),v&&0===x&&v(w,P)),r===a.OBJECT_REPLACEMENT_CHARACTER){const t=E[e];if(!t)continue;const{type:r,attributes:n,innerHTML:a}=t,c=(0,o.getFormatType)(r);y&&"#comment"===r?(P=s(l(P),{type:"span",attributes:{contenteditable:"false","data-rich-text-comment":n["data-rich-text-comment"]}}),s(s(P,{type:"span"}),n["data-rich-text-comment"].trim())):y||"script"!==r?!1===c?.contentEditable?(P=s(l(P),i({...t,isEditableTree:y,boundaryClass:_===e&&x===e+1})),a&&s(P,{html:a})):P=s(l(P),i({...t,object:!0,isEditableTree:y})):(P=s(l(P),i({type:"script",isEditableTree:y})),s(P,{html:decodeURIComponent(n["data-rich-text-script"])})),P=s(l(P),"")}else t||"\n"!==r?d(P)?p(P,r):P=s(l(P),r):(P=s(l(P),{type:"br",attributes:y?{"data-rich-text-line-break":"true"}:void 0,object:!0}),P=s(l(P),""));g&&_===e+1&&g(w,P),v&&x===e+1&&v(w,P),n&&e===T.length&&(s(l(P),a.ZWNBSP),b&&0===T.length&&s(l(P),{type:"span",attributes:{"data-rich-text-placeholder":b,style:"pointer-events:none;user-select:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;"}})),M=O,A=r}return w};var n=r(6807),o=r(8136),a=r(2773);function s(e,t){if(t)return e;const r={};for(const t in e){let n=t;t.startsWith("data-disable-rich-text-")&&(n=t.slice(23)),r[n]=e[t]}return r}function i({type:e,tagName:t,attributes:r,unregisteredAttributes:n,object:a,boundaryClass:i,isEditableTree:c}){const u=(0,o.getFormatType)(e);let l={};if(i&&c&&(l["data-rich-text-format-boundary"]="true"),!u)return r&&(l={...r,...l}),{type:e,attributes:s(l,c),object:a};l={...n,...l};for(const e in r){const t=!!u.attributes&&u.attributes[e];t?l[t]=r[e]:l[e]=r[e]}return u.className&&(l.class?l.class=`${u.className} ${l.class}`:l.class=u.className),c&&!1===u.contentEditable&&(l.contenteditable="false"),{type:t||u.tagName,object:u.object,attributes:s(l,c)}}function c(e,t,r){do{if(e[r]!==t[r])return!1}while(r--);return!0}},5563:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toggleFormat=function(e,t){return(0,a.getActiveFormat)(e,t.type)?(t.title&&(0,n.speak)((0,o.sprintf)((0,o.__)("%s removed."),t.title),"assertive"),(0,s.removeFormat)(e,t.type)):(t.title&&(0,n.speak)((0,o.sprintf)((0,o.__)("%s applied."),t.title),"assertive"),(0,i.applyFormat)(e,t))};var n=r(195),o=r(7723),a=r(9400),s=r(3927),i=r(9347)},2114:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterFormatType=function(e){const t=(0,n.select)(o.store).getFormatType(e);if(t)return(0,n.dispatch)(o.store).removeFormatTypes(e),t;window.console.error(`Format ${e} is not registered.`)};var n=r(7143),o=r(625)},7779:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.updateFormats=function({value:e,start:t,end:r,formats:o}){const a=Math.min(t,r),s=Math.max(t,r),i=e.formats[a-1]||[],c=e.formats[s]||[];for(e.activeFormats=o.map(((e,t)=>{if(i[t]){if((0,n.isFormatEqual)(e,i[t]))return i[t]}else if(c[t]&&(0,n.isFormatEqual)(e,c[t]))return c[t];return e}));--r>=t;)e.activeFormats.length>0?e.formats[r]=e.activeFormats:delete e.formats[r];return e};var n=r(1806)},195:e=>{"use strict";e.exports=window.wp.a11y},9491:e=>{"use strict";e.exports=window.wp.compose},7143:e=>{"use strict";e.exports=window.wp.data},4040:e=>{"use strict";e.exports=window.wp.deprecated},6087:e=>{"use strict";e.exports=window.wp.element},9877:e=>{"use strict";e.exports=window.wp.escapeHtml},7723:e=>{"use strict";e.exports=window.wp.i18n},8558:e=>{"use strict";e.exports=window.wp.keycodes},9370:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}var n={};(()=>{"use strict";var e=n;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"RichTextData",{enumerable:!0,get:function(){return s.RichTextData}}),Object.defineProperty(e,"__experimentalRichText",{enumerable:!0,get:function(){return A.default}}),Object.defineProperty(e,"__unstableCreateElement",{enumerable:!0,get:function(){return P.createElement}}),Object.defineProperty(e,"__unstableToDom",{enumerable:!0,get:function(){return _.toDom}}),Object.defineProperty(e,"__unstableUseRichText",{enumerable:!0,get:function(){return A.useRichText}}),Object.defineProperty(e,"applyFormat",{enumerable:!0,get:function(){return o.applyFormat}}),Object.defineProperty(e,"concat",{enumerable:!0,get:function(){return a.concat}}),Object.defineProperty(e,"create",{enumerable:!0,get:function(){return s.create}}),Object.defineProperty(e,"getActiveFormat",{enumerable:!0,get:function(){return i.getActiveFormat}}),Object.defineProperty(e,"getActiveFormats",{enumerable:!0,get:function(){return c.getActiveFormats}}),Object.defineProperty(e,"getActiveObject",{enumerable:!0,get:function(){return u.getActiveObject}}),Object.defineProperty(e,"getTextContent",{enumerable:!0,get:function(){return l.getTextContent}}),Object.defineProperty(e,"insert",{enumerable:!0,get:function(){return b.insert}}),Object.defineProperty(e,"insertObject",{enumerable:!0,get:function(){return h.insertObject}}),Object.defineProperty(e,"isCollapsed",{enumerable:!0,get:function(){return d.isCollapsed}}),Object.defineProperty(e,"isEmpty",{enumerable:!0,get:function(){return f.isEmpty}}),Object.defineProperty(e,"join",{enumerable:!0,get:function(){return m.join}}),Object.defineProperty(e,"registerFormatType",{enumerable:!0,get:function(){return p.registerFormatType}}),Object.defineProperty(e,"remove",{enumerable:!0,get:function(){return v.remove}}),Object.defineProperty(e,"removeFormat",{enumerable:!0,get:function(){return g.removeFormat}}),Object.defineProperty(e,"replace",{enumerable:!0,get:function(){return y.replace}}),Object.defineProperty(e,"slice",{enumerable:!0,get:function(){return E.slice}}),Object.defineProperty(e,"split",{enumerable:!0,get:function(){return T.split}}),Object.defineProperty(e,"store",{enumerable:!0,get:function(){return t.store}}),Object.defineProperty(e,"toHTMLString",{enumerable:!0,get:function(){return x.toHTMLString}}),Object.defineProperty(e,"toggleFormat",{enumerable:!0,get:function(){return O.toggleFormat}}),Object.defineProperty(e,"unregisterFormatType",{enumerable:!0,get:function(){return w.unregisterFormatType}}),Object.defineProperty(e,"useAnchor",{enumerable:!0,get:function(){return M.useAnchor}}),Object.defineProperty(e,"useAnchorRef",{enumerable:!0,get:function(){return C.useAnchorRef}});var t=r(625),o=r(9347),a=r(2647),s=r(1451),i=r(9400),c=r(6807),u=r(1836),l=r(9211),d=r(7751),f=r(4091),m=r(6039),p=r(1257),g=r(3927),v=r(8439),y=r(847),b=r(3382),h=r(4004),E=r(9589),T=r(5247),_=r(3599),x=r(3334),O=r(5563),w=r(2114),P=r(3038),C=r(6024),M=r(9024),A=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=j(void 0);if(t&&t.has(e))return t.get(e);var r={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var a=n?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,t&&t.set(e,r),r}(r(2283));function j(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(j=function(e){return e?r:t})(e)}})(),(window.wp=window.wp||{}).richText=n})();