"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[4317],{68191:(e,t,o)=>{o.d(t,{A:()=>d});var s=o(27723),n=o(56427),i=o(86087),a=o(29491),r=o(47143),c=o(40314),l=o(39793);class m extends i.Component{constructor(e){super(e),this.state={isAwaitingRedirect:!1,isRedirecting:!1},this.connectJetpack=this.connectJetpack.bind(this),e.setIsPending(!1)}componentDidUpdate(e){const{createNotice:t,error:o,onError:s,isRequesting:n}=this.props;o&&o!==e.error&&(s&&s(),t("error",o)),!this.state.isAwaitingRedirect||this.state.isRedirecting||n||o||this.setState({isRedirecting:!0},(()=>{window.location=this.props.jetpackAuthUrl}))}connectJetpack(){const{onConnect:e}=this.props;e&&e(),this.setState({isAwaitingRedirect:!0})}render(){const{error:e,onSkip:t,skipText:o,onAbort:a,abortText:r}=this.props;return(0,l.jsxs)(i.Fragment,{children:[e?(0,l.jsx)(n.Button,{isPrimary:!0,onClick:()=>window.location.reload(),children:(0,s.__)("Retry","woocommerce")}):(0,l.jsx)(n.Button,{isBusy:this.state.isAwaitingRedirect,isPrimary:!0,onClick:this.connectJetpack,children:(0,s.__)("Connect","woocommerce")}),t&&(0,l.jsx)(n.Button,{onClick:t,children:o||(0,s.__)("No thanks","woocommerce")}),a&&(0,l.jsx)(n.Button,{onClick:a,children:r||(0,s.__)("Abort","woocommerce")})]})}}m.defaultProps={setIsPending:()=>{},from:"woocommerce-services"};const d=(0,a.compose)((0,r.withSelect)(((e,t)=>{const{getJetpackAuthUrl:o,isResolving:n}=e(c.onboardingStore),i={redirectUrl:t.redirectUrl||window.location.href,from:t.from},a=o(i),r=n("getJetpackAuthUrl",[i]);let l;return n||a||(l=(0,s.__)("Error requesting connection URL.","woocommerce")),a?.errors?.length&&(l=a?.errors[0]),{error:l,isRequesting:r,jetpackAuthUrl:a.url}})),(0,r.withDispatch)((e=>{const{createNotice:t}=e("core/notices");return{createNotice:t}})))(m)},18886:(e,t,o)=>{function s(e=""){return e?e.split(":")[0]:null}function n(e,t,o=!1,s){return function(e,t=!1,o,s){const n=[];return s?((e.product_types||[]).forEach((e=>{s[e]&&s[e].product&&(t||!o.includes(s[e].slug))&&n.push(s[e])})),n):n}(t,o,s,e).map((e=>e.id||e.product))}o.d(t,{Dr:()=>n,gI:()=>s}),o(18537)},7905:(e,t,o)=>{o.d(t,{r:()=>a});var s=o(93832),n=o(15703),i=o(56109);const a=(e,t={})=>{const{pathname:o,search:a}=window.location,r=(0,n.getSetting)("connectNonce","");return t={"wccom-site":(0,i.Qk)("siteUrl"),"wccom-back":o+a,"wccom-woo-version":(0,n.getSetting)("wcVersion"),"wccom-connect-nonce":r,...t},(0,s.addQueryArgs)(e,t)}},98642:(e,t,o)=>{o.d(t,{U:()=>c});var s=o(14908),n=o(36849),i=o(27723),a=o(98846),r=o(39793);const c=({buttonText:e})=>(0,r.jsx)(s.Text,{variant:"caption",className:"woocommerce-task__caption is-tos",size:"12",lineHeight:"16px",style:{display:"block"},children:(0,n.A)({mixedString:(0,i.sprintf)((0,i.__)('By clicking "%s," you agree to our {{tosLink}}Terms of Service{{/tosLink}} and have read our {{privacyPolicyLink}}Privacy Policy{{/privacyPolicyLink}}.',"woocommerce"),e),components:{tosLink:(0,r.jsx)(a.Link,{href:"https://wordpress.com/tos/",target:"_blank",type:"external",children:(0,r.jsx)(r.Fragment,{})}),privacyPolicyLink:(0,r.jsx)(a.Link,{href:"https://automattic.com/privacy/",target:"_blank",type:"external",children:(0,r.jsx)(r.Fragment,{})})}})})},41040:(e,t,o)=>{o.d(t,{y:()=>i});var s=o(4921),n=o(39793);const i=({logo:e,description:t,layout:o="single",features:i,children:a})=>(0,n.jsxs)("div",{className:(0,s.A)("woocommerce-task-shipping-recommendation__plugins-install",o),children:[e&&(0,n.jsx)("div",{className:"plugins-install__plugin-banner-image",children:(0,n.jsx)("img",{src:e.image,alt:e?.alt})}),t&&(0,n.jsx)("p",{children:t}),(0,n.jsx)("div",{className:"plugins-install__list",children:i.map(((e,t)=>(0,n.jsxs)("div",{className:"plugins-install__list-item",children:[(0,n.jsx)("div",{className:"plugins-install__list-icon",children:(0,n.jsx)("img",{src:e.icon,alt:""})}),(0,n.jsxs)("div",{children:[e.title&&(0,n.jsx)("div",{children:(0,n.jsx)("strong",{children:e.title})}),(0,n.jsx)("div",{children:e.description})]})]},t)))}),a]})},62513:(e,t,o)=>{o.d(t,{A:()=>x,a:()=>_});var s=o(27723),n=o(56427),i=o(40314),a=o(86087),r=o(98846),c=o(47143),l=o(18537),m=o(66087),d=o(39793);const u=["addressLine1","addressLine2","city","countryState","postCode"],p=e=>e.replace(/\s/g,"").toLowerCase(),g=(e,t)=>o=>{const s=e?o.key.split(":"):o.label.split("—");if(s.length<=1)return!1;const n=s[1];if(n.includes("/")){const e=n.split("/");return p(e[0])===t||p(e[1])===t}if(n.includes("(")&&n.includes(")")){const e=n.replace(")","").split("(");return p(e[0])===t||p(e[1])===t}return p(n)===t};function h({getInputProps:e,getSelectControlProps:t,setValue:o}){const h=e("countryState").value,{locale:_,hasFinishedResolution:x,countries:w,loadingCountries:j}=(0,c.useSelect)((e=>{const{getLocale:t,getCountries:o,hasFinishedResolution:s}=e(i.countriesStore);return{locale:t(h),countries:o(),loadingCountries:!s("getCountries",void 0),hasFinishedResolution:s("getLocales",void 0)}}),[h]),k=(0,a.useMemo)((()=>function(e){return e.reduce(((e,t)=>{if(!t.states.length)return e.push({key:t.code,label:(0,l.decodeEntities)(t.name)}),e;const o=t.states.map((e=>({key:t.code+":"+e.code,label:(0,l.decodeEntities)(t.name)+" — "+(0,l.decodeEntities)(e.name)})));return e.push(...o),e}),[])}(w)),[w]),y=function(e,t,o){const[s,n]=(0,a.useState)(""),[i,r]=(0,a.useState)(""),c=(0,a.useRef)();return(0,a.useEffect)((()=>{if(!c.current){const o=e.find((e=>e.key===t)),a=o?o.label.split(/\u2013|\u2014|\-/):[],c=(a[0]||"").trim(),l=(a[1]||"").trim();c===s&&l===i||(n(c),r(l))}c.current=!1}),[t,e]),(0,a.useEffect)((()=>{if(void 0===c.current)return;if(!s&&!i&&t)return c.current=!0,void o("countryState","");const n=new RegExp((0,m.escapeRegExp)(s),"i"),a=s.length<3,r=i.length<3&&!!i.match(/^[\w]+$/);let l=[];s.length&&i.length?(l=e.filter((e=>n.test(a?e.key:e.label))),l.length||(l=[...e]),l.length>1&&(l=l.filter(g(r,p(i))))):s.length?l=e.filter((e=>n.test(a?e.key:e.label))):i.length&&(l=e.filter(g(r,p(i)))),1===l.length&&t!==l[0].key&&(c.current=!0,o("countryState",l[0].key))}),[s,i,e,o]),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("input",{onChange:e=>n(e.target.value),value:s,name:"country",type:"text",className:"woocommerce-select-control__autofill-input",tabIndex:-1,autoComplete:"country"}),(0,d.jsx)("input",{onChange:e=>r(e.target.value),value:i,name:"state",type:"text",className:"woocommerce-select-control__autofill-input",tabIndex:-1,autoComplete:"address-level1"})]})}(k,h,o);if((0,a.useEffect)((()=>{_&&u.forEach((t=>{const s=t.replace(/(address)Line([0-9])/,"$1$2").toLowerCase(),n=e(t);var i;i=s,_.hasOwnProperty(i)&&_[s]?.hidden&&n.value?.length>0&&o(t,"")}))}),[h,_]),!x||j)return(0,d.jsx)(n.Spinner,{});const{onChange:S,...N}=t("countryState");return(0,d.jsxs)("div",{className:"woocommerce-store-address-fields",children:[(0,d.jsx)(r.SelectControl,{label:(0,s.__)("Country / Region","woocommerce")+" *",autoComplete:"new-password",getSearchExpression:e=>new RegExp("(^"+e+"| — ("+e+"))","i"),options:k,excludeSelectedOptions:!1,showAllOnFocus:!0,isSearchable:!0,...N,onChange:e=>{S(e)},controlClassName:e("countryState").className,virtualScroll:!0,virtualItemHeight:56,virtualListHeight:336,children:y}),!_?.address_1?.hidden&&(0,d.jsx)(r.TextControl,{__nextHasNoMarginBottom:!0,id:"woocommerce-store-address-form-address_1",label:_?.address_1?.label||(0,s.__)("Address","woocommerce"),autoComplete:"address-line1",...e("addressLine1")}),!_?.postcode?.hidden&&(0,d.jsx)(r.TextControl,{__nextHasNoMarginBottom:!0,id:"woocommerce-store-address-form-postcode",label:_?.postcode?.label||(0,s.__)("Post code","woocommerce"),autoComplete:"postal-code",...e("postCode")}),!_?.city?.hidden&&(0,d.jsx)(r.TextControl,{__nextHasNoMarginBottom:!0,id:"woocommerce-store-address-form-city",label:_?.city?.label||(0,s.__)("City","woocommerce"),...e("city"),autoComplete:"address-level2"})]})}const _=e=>(e=>{const t={};return e.countryState.trim().length||(t.countryState=(0,s.__)("Please select a country / region","woocommerce")),t})(e),x=({onComplete:e,createNotice:t,isSettingsRequesting:o,updateAndPersistSettingsForGroup:l,settings:m,buttonText:u=(0,s.__)("Continue","woocommerce"),validate:p=_})=>{const{hasFinishedResolution:g}=(0,c.useSelect)((e=>{const t=e(i.countriesStore);return t.getCountries(),{getLocale:t.getLocale,locales:t.getLocales(),hasFinishedResolution:t.hasFinishedResolution("getLocales",void 0)&&t.hasFinishedResolution("getCountries",void 0)}}),[]),[x,w]=(0,a.useState)(!1);return o||!g?(0,d.jsx)(r.Spinner,{}):(0,d.jsx)(r.Form,{initialValues:{addressLine1:m?.woocommerce_store_address||"",addressLine2:m?.woocommerce_store_address_2||"",city:m?.woocommerce_store_city||"",countryState:m?.woocommerce_default_country||"",postCode:m?.woocommerce_store_postcode||""},onSubmit:async o=>{w(!0);try{await l("general",{general:{...m,woocommerce_store_address:o.addressLine1,woocommerce_store_address_2:o.addressLine2,woocommerce_default_country:o.countryState,woocommerce_store_city:o.city,woocommerce_store_postcode:o.postCode}}),w(!1),e(o)}catch(e){w(!1),t("error",(0,s.__)("There was a problem saving your store location","woocommerce"))}},validate:p,children:({getInputProps:e,getSelectControlProps:t,handleSubmit:o,setValue:s})=>(0,d.jsxs)(a.Fragment,{children:[(0,d.jsx)(h,{getInputProps:e,getSelectControlProps:t,setValue:s}),(0,d.jsx)(n.Button,{variant:"primary",onClick:o,isBusy:x,children:u})]})})}},41418:(e,t,o)=>{o.d(t,{Bo:()=>n,El:()=>a,Ud:()=>i});var s=o(15703);const n=["woocommerce-services"],i=(e,t=!0)=>{const{woocommerce_store_address:o,woocommerce_default_country:s,woocommerce_store_postcode:n}=e;return t?Boolean(o&&s&&n):Boolean(o&&s)},a=()=>{window.location.href=(0,s.getAdminLink)("admin.php?page=wc-settings&tab=tax&section=standard&wc_onboarding_active_task=tax")}},87979:(e,t,o)=>{o.r(t),o.d(t,{ProgressTitle:()=>It.i,TaskLists:()=>St,TasksPlaceholder:()=>jt.W,TasksReminderBar:()=>Nt.O});var s=o(56109);const n=(0,s.Qk)("onboarding"),i=()=>window?.wcAdminFeatures?.["import-products-task"]&&n?.profile?.business_choice&&"im_already_selling"===n?.profile?.business_choice;var a=o(27723),r=o(47143),c=o(40314),l=o(83306),m=o(86087),d=o(92279),u=o(85816),p=o(96476),g=o(15703),h=o(56427),_=o(4921),x=o(98846),w=o(14908),j=o(24060),k=o(39793);const y=({hasSetup:e=!1,needsSetup:t=!0,id:o,isEnabled:s=!1,isLoading:n=!1,isInstalled:i=!1,isRecommended:r=!1,hasPlugins:c,manageUrl:d=null,markConfigured:u=()=>{},onSetUp:g=()=>{},onSetupCallback:_,setupButtonText:x=(0,a.__)("Get started","woocommerce"),externalLink:w=null})=>{const[y,S]=(0,m.useState)(!1),N="woocommerce-task-payment__action";if(n)return(0,k.jsx)(h.Spinner,{});const I=async()=>{if(g(o),(0,l.recordEvent)("tasklist_payment_setup",{selected:(0,j.CZ)(o)}),c||!w)return _?(S(!0),void await new Promise(_).then((()=>{S(!1)})).catch((()=>{S(!1)}))):void(0,p.updateQueryString)({id:o});window.location.href=w},C=()=>(0,k.jsx)(h.Button,{className:N,isSecondary:!0,role:"button",href:d,onClick:()=>(0,l.recordEvent)("tasklist_payment_manage",{id:o}),children:(0,a.__)("Manage","woocommerce")}),M=()=>(0,k.jsx)(h.Button,{className:N,isPrimary:r,isSecondary:!r,isBusy:y,disabled:y,onClick:()=>I(),children:x}),b=()=>(0,k.jsx)(h.Button,{className:N,isSecondary:!0,onClick:()=>u(o),children:(0,a.__)("Enable","woocommerce")});return e?c?t?i&&c?(0,k.jsx)(h.Button,{className:N,isPrimary:r,isSecondary:!r,isBusy:y,disabled:y,onClick:()=>I(),children:(0,a.__)("Finish setup","woocommerce")}):(0,k.jsx)(M,{}):s?(0,k.jsx)(C,{}):(0,k.jsx)(b,{}):s?(0,k.jsx)(C,{}):(0,k.jsx)(M,{}):s?(0,k.jsx)(C,{}):(0,k.jsx)(b,{})},S=({isRecommended:e,markConfigured:t,paymentGateway:o})=>{const{image_72x72:s,content:n,id:i,plugins:r=[],title:c,loading:l,enabled:d=!1,installed:p=!1,needsSetup:g=!0,requiredSettings:j,settingsUrl:S,is_local_partner:N,external_link:I,transaction_processors:C}=o,M=(0,w.useSlot)(`woocommerce_payment_gateway_configure_${i}`),b=(0,w.useSlot)(`woocommerce_payment_gateway_setup_${i}`),v=Boolean(M?.fills?.length)||Boolean(b?.fills?.length),f=Boolean(r.length||j.length||v||I),T=e&&g,A=(0,_.A)("woocommerce-task-payment","woocommerce-task-card",g&&"woocommerce-task-payment-not-configured","woocommerce-task-payment-"+i);return(0,k.jsxs)(m.Fragment,{children:[(0,k.jsxs)(h.CardBody,{style:{paddingLeft:0,marginBottom:0},className:A,children:[(0,k.jsx)(h.CardMedia,{isBorderless:!0,children:(0,k.jsx)("img",{src:s,alt:c,onError:e=>e.currentTarget.src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAATlBMVEUAAADz9/f0+/v29/f29vb29/f19vb09vb09PT29/fz9vb2+Pj29vb2+Pj19vb3+fn2+Pj09vb29/fDxMfj5OXW19nx8vPp6uvJys3d3t9on+F5AAAAEnRSTlMAIBDfoM/vkGDeMO+PX+6fj493pkuNAAAA9klEQVRYw+3Yyw6CMBAF0On0QUHxcXmo//+j6koaIbFwE0PSs2RxM52QNjPyofHsA34WvGtVvpkqIJ8zktILVjqm5VisZidFNRYb2IZQT1KTWmxkVd6SPm/ouAHB+3AOBJWIguGgEkERxYHCiQeFlwCKg4CkBJWgXQSNj2HemBl07xbc84Ju3aJbVlDfdf3S978FDf2MIT9oQQn6KWj//1EJ2l0Q4WIjX7UYh3mPcffvWgkqQTloIwRtqOGNWREUrSinSSpSUU5GHNcpJV1JK41aOUuW2nDWPnXDWUS96pnQanWfVVLGId/paOSbts6HjBB/jpNqnnilXjdVFKs9AAAAAElFTkSuQmCC"})}),(0,k.jsxs)("div",{className:"woocommerce-task-payment__description",children:[(0,k.jsxs)(w.Text,{as:"h3",className:"woocommerce-task-payment__title",children:[(0,k.jsx)("span",{children:c}),T&&(0,k.jsx)(x.Pill,{className:!N&&"pill-green",children:N?(0,a.__)("Local Partner","woocommerce"):(0,a.__)("Recommended","woocommerce")}),p&&g&&!!r.length&&(0,k.jsx)(u.SetupRequired,{})]}),(0,k.jsx)("div",{className:"woocommerce-task-payment__content",children:n}),C&&(0,k.jsx)("div",{className:"woocommerce-task-payment__transaction-processors_images",children:Object.keys(C).map((e=>(0,k.jsx)("img",{src:C[e],alt:e},e)))})]}),(0,k.jsx)("div",{className:"woocommerce-task-payment__footer",children:(0,k.jsx)(y,{manageUrl:S,id:i,hasSetup:f,needsSetup:g,isEnabled:d,isInstalled:p,hasPlugins:Boolean(r.length),isRecommended:e,isLoading:l,markConfigured:t,externalLink:I})})]}),(0,k.jsx)(h.CardDivider,{})]},i)},N=({heading:e,headingDescription:t,markConfigured:o,recommendation:s,paymentGateways:n,footerLink:i})=>(0,k.jsxs)(h.Card,{children:[e&&(0,k.jsxs)(h.CardHeader,{as:"h2",children:[e,t&&(0,k.jsx)("p",{className:"woocommerce-task-payment-header__description",children:t})]}),n.map((e=>{const{id:t}=e;return(0,k.jsx)(S,{isRecommended:s===t,markConfigured:o,paymentGateway:e},t)})),i&&(0,k.jsx)(h.CardFooter,{isBorderless:!0,children:i})]}),I=()=>{const e=(0,_.A)("woocommerce-task-payment","woocommerce-task-card");return(0,k.jsxs)(m.Fragment,{children:[(0,k.jsxs)(h.CardBody,{style:{paddingLeft:0,marginBottom:0},className:e,children:[(0,k.jsx)(h.CardMedia,{isBorderless:!0,children:(0,k.jsx)("span",{className:"is-placeholder"})}),(0,k.jsxs)("div",{className:"woocommerce-task-payment__description",children:[(0,k.jsx)(w.Text,{as:"h3",className:"woocommerce-task-payment__title",children:(0,k.jsx)("span",{className:"is-placeholder"})}),(0,k.jsx)("div",{className:"woocommerce-task-payment__content",children:(0,k.jsx)("span",{className:"is-placeholder"})})]}),(0,k.jsx)("div",{className:"woocommerce-task-payment__footer",children:(0,k.jsx)("span",{className:"is-placeholder"})})]}),(0,k.jsx)(h.CardDivider,{})]})},C=()=>(0,k.jsxs)(h.Card,{"aria-hidden":"true",className:"is-loading woocommerce-payment-gateway-suggestions-list-placeholder",children:[(0,k.jsx)(h.CardHeader,{as:"h2",children:(0,k.jsx)("span",{className:"is-placeholder"})}),(0,k.jsx)(I,{}),(0,k.jsx)(I,{}),(0,k.jsx)(I,{})]});var M=o(46772),b=o(12974);const v=({markConfigured:e,paymentGateway:t})=>{const{id:o,connectionUrl:s,setupHelpText:n,settingsUrl:i,title:m,requiredSettings:d}=t,{createNotice:p}=(0,r.useDispatch)("core/notices"),{updatePaymentGateway:g}=(0,r.useDispatch)(c.PAYMENT_GATEWAYS_STORE_NAME),_=(0,w.useSlot)(`woocommerce_payment_gateway_configure_${o}`),j=Boolean(_?.fills?.length),{isUpdating:y}=(0,r.useSelect)((e=>{const{isPaymentGatewayUpdating:t}=e(c.PAYMENT_GATEWAYS_STORE_NAME);return{isUpdating:t()}})),S=t=>{g(o,{enabled:!0,settings:t}).then((t=>{t&&t.id===o&&(e(o),p("success",(0,a.sprintf)((0,a.__)("%s configured successfully","woocommerce"),m)))})).catch((()=>{p("error",(0,a.__)("There was a problem saving your payment settings","woocommerce"))}))},N=n&&(0,k.jsx)("p",{dangerouslySetInnerHTML:(0,b.Ay)(n)}),I=(0,k.jsx)(x.DynamicForm,{fields:d,isBusy:y,onSubmit:S,submitLabel:(0,a.__)("Continue","woocommerce"),validate:e=>((e,t)=>{const o={},s=e=>t.find((t=>t.id===e));for(const[t,n]of Object.entries(e)){const e=s(t),i=e.label.replace(/([A-Z][a-z]+)/g,(e=>e.toLowerCase()));n||"checkbox"===e.type||(o[t]=`Please enter your ${i}`)}return o})(e,d)});return j?(0,k.jsx)(u.WooPaymentGatewayConfigure.Slot,{fillProps:{defaultForm:I,defaultSubmit:S,defaultFields:d,markConfigured:()=>e(o),paymentGateway:t},id:o}):s?(0,k.jsxs)(k.Fragment,{children:[N,(0,k.jsx)(h.Button,{isPrimary:!0,onClick:()=>(0,l.recordEvent)("tasklist_payment_connect_start",{payment_method:o}),href:s,children:(0,a.__)("Connect","woocommerce")})]}):d.length?(0,k.jsxs)(k.Fragment,{children:[N,I]}):(0,k.jsxs)(k.Fragment,{children:[N||(0,k.jsx)("p",{children:(0,a.__)("You can manage this payment gateway’s settings by clicking the button below","woocommerce")}),(0,k.jsx)(h.Button,{isPrimary:!0,href:i,children:(0,a.__)("Get started","woocommerce")})]})},f=({markConfigured:e,paymentGateway:t})=>{const{id:o,plugins:s=[],title:n,postInstallScripts:i,installed:d}=t,p=(0,w.useSlot)(`woocommerce_payment_gateway_setup_${o}`),g=Boolean(p?.fills?.length),[_,j]=(0,m.useState)(!1);(0,m.useEffect)((()=>{(0,l.recordEvent)("payments_task_stepper_view",{payment_method:o})}),[]);const{invalidateResolutionForStoreSelector:y}=(0,r.useDispatch)(c.PAYMENT_GATEWAYS_STORE_NAME),{isOptionUpdating:S,isPaymentGatewayResolving:N,needsPluginInstall:I}=(0,r.useSelect)((e=>{const{isOptionsUpdating:t}=e(c.optionsStore),{isResolving:o}=e(c.PAYMENT_GATEWAYS_STORE_NAME),n=e(c.pluginsStore).getActivePlugins(),i=s.filter((e=>!n.includes(e)));return{isOptionUpdating:t(),isPaymentGatewayResolving:o("getPaymentGateways"),needsPluginInstall:!!i.length}}));(0,m.useEffect)((()=>{if(!I)if(i&&i.length){const e=i.map((e=>function(e){return new Promise(((t,o)=>{document.querySelector(`#${e.handle}-js`)&&t();const s=document.createElement("script");s.src=e.src,s.id=`${e.handle}-js`,s.async=!0,s.onload=t,s.onerror=o,document.body.appendChild(s)}))}(e)));Promise.all(e).then((()=>{j(!0)}))}else j(!0)}),[i,I]);const C=(0,m.useMemo)((()=>s&&s.length?{key:"install",label:(0,a.sprintf)((0,a.__)("Install %s","woocommerce"),n),content:(0,k.jsx)(x.Plugins,{onComplete:(e,t)=>{(0,M.R)(t),y("getPaymentGateways"),(0,l.recordEvent)("tasklist_payment_install_method",{plugins:s})},onError:(e,t)=>(0,M.R)(t),autoInstall:!0,pluginSlugs:s})}:null),[]),b=(0,m.useMemo)((()=>({key:"configure",label:(0,a.sprintf)((0,a.__)("Configure your %(title)s account","woocommerce"),{title:n}),content:d?(0,k.jsx)(v,{markConfigured:e,paymentGateway:t}):null})),[d]),f=I||S||N||!_,T=(0,k.jsx)(x.Stepper,{isVertical:!0,isPending:f,currentStep:I?"install":"configure",steps:[C,b].filter(Boolean)});return(0,k.jsx)(h.Card,{className:"woocommerce-task-payment-method woocommerce-task-card",children:(0,k.jsx)(h.CardBody,{children:g?(0,k.jsx)(u.WooPaymentGatewaySetup.Slot,{fillProps:{defaultStepper:T,defaultInstallStep:C,defaultConfigureStep:b,markConfigured:()=>e(o),paymentGateway:t},id:o}):T})})},T=()=>{const e=(0,_.A)("is-loading","woocommerce-task-payment-method","woocommerce-task-card");return(0,k.jsx)(h.Card,{"aria-hidden":"true",className:e,children:(0,k.jsx)(h.CardBody,{children:(0,k.jsx)(x.Stepper,{isVertical:!0,currentStep:"none",steps:[{key:"first",label:""},{key:"second",label:""}]})})})};var A=o(1455),P=o.n(A);const L=({paymentGateway:e,onSetupCallback:t=null})=>{const{id:o,needsSetup:s,installed:n,enabled:i,installed:l}=e,m=(0,r.useSelect)((e=>e(c.paymentSettingsStore).getIsWooPayEligible()),[]),{createNotice:d}=(0,r.useDispatch)("core/notices");return n&&null===t&&(t=()=>{!function(e,t){const o=(0,a.__)("There was an error connecting to WooPayments. Please try again or connect later in store settings.","woocommerce");P()({path:c.WC_ADMIN_NAMESPACE+"/plugins/connect-wcpay",method:"POST"}).then((e=>{window.location=e.connectUrl})).catch((()=>{e("error",o),"function"==typeof t&&t()}))}(d)}),(0,k.jsx)("div",{className:"woocommerce-wcpay-suggestion",children:(0,k.jsxs)(u.WCPayBanner,{children:[(0,k.jsx)(u.WCPayBannerBody,{textPosition:"left",actionButton:(0,k.jsx)(y,{id:o,hasSetup:!0,needsSetup:s,isEnabled:i,isRecommended:!0,isInstalled:l,hasPlugins:!0,setupButtonText:(0,a.__)("Get started","woocommerce"),onSetupCallback:t}),bannerImage:(0,k.jsx)(u.WCPayBannerImageCut,{}),isWooPayEligible:m}),(0,k.jsx)(u.WCPayBenefits,{isWooPayEligible:m}),(0,k.jsx)(u.WCPayBannerFooter,{isWooPayEligible:m})]})})};var D=o(93832);const E=()=>{(0,l.recordEvent)("tasklist_payments_wcpay_bnpl_click")},B=({paymentGateway:e})=>{const{id:t,title:o,content:s,settingsUrl:n,image:i}=e;if(!n)return null;const r=(0,D.addQueryArgs)(n,{from:"WCADMIN_PAYMENT_TASK"});return(0,k.jsxs)(h.Card,{className:"woocommerce-wcpay-bnpl-suggestion",size:"medium",children:[(0,k.jsxs)("div",{className:"woocommerce-wcpay-bnpl-suggestion__contents-container",children:[(0,k.jsx)(h.CardHeader,{as:"h2",isBorderless:!0,style:{padding:0},children:o}),(0,k.jsx)(h.CardBody,{className:"woocommerce-wcpay-bnpl-suggestion__body",style:{padding:0},children:(0,k.jsxs)("div",{className:"woocommerce-wcpay-bnpl-suggestion__contents",style:i?{}:{maxWidth:"100%"},children:[(0,k.jsx)("p",{className:"woocommerce-wcpay-bnpl-suggestion__description",children:s}),(0,k.jsx)(h.Button,{className:"woocommerce-wcpay-bnpl-suggestion__button",variant:"primary",href:r,onClick:E,children:(0,a.__)("Get started","woocommerce")})]})})]}),i&&(0,k.jsx)("img",{alt:(0,a.__)("WooPayments BNPL illustration","woocommerce"),src:i,className:"svg-background"})]},t)};var O=o(18886);const z=(e,t)=>e.recommendation_priority-t.recommendation_priority,R=e=>1===e.plugins?.length&&"woocommerce-payments"===e.plugins[0],W=(e,t)=>e.category_other&&-1!==e.category_other.indexOf(t),Y={account_name:"",account_number:"",bank_name:"",sort_code:"",iban:"",bic:""};(0,d.registerPlugin)("wc-admin-payment-gateway-setup-bacs",{render:()=>{const e=(0,r.useSelect)((e=>e(c.optionsStore).isOptionsUpdating())),{createNotice:t}=(0,r.useDispatch)("core/notices"),{updateOptions:o}=(0,r.useDispatch)(c.optionsStore),s=e=>{const t={};return e.account_number||e.iban||(t.account_number=t.iban=(0,a.__)("Please enter an account number or IBAN","woocommerce")),t};return(0,k.jsx)(k.Fragment,{children:(0,k.jsx)(u.WooPaymentGatewaySetup,{id:"bacs",children:({markConfigured:n})=>(0,k.jsx)(x.Form,{initialValues:Y,onSubmit:e=>(async(e,s)=>{if((await o({woocommerce_bacs_settings:{enabled:"yes"},woocommerce_bacs_accounts:[e]})).success)return s(),void t("success",(0,a.__)("Direct bank transfer details added successfully","woocommerce"));t("error",(0,a.__)("There was a problem saving your payment settings","woocommerce"))})(e,n),validate:s,children:({getInputProps:t,handleSubmit:o})=>(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(x.H,{children:(0,a.__)("Add your bank details","woocommerce")}),(0,k.jsx)("p",{children:(0,a.__)("These details are required to receive payments via bank transfer","woocommerce")}),(0,k.jsxs)("div",{className:"woocommerce-task-payment-method__fields",children:[(0,k.jsx)(x.TextControl,{__nextHasNoMarginBottom:!0,label:(0,a.__)("Account name","woocommerce"),required:!0,...t("account_name")}),(0,k.jsx)(x.TextControl,{__nextHasNoMarginBottom:!0,label:(0,a.__)("Account number","woocommerce"),required:!0,...t("account_number")}),(0,k.jsx)(x.TextControl,{__nextHasNoMarginBottom:!0,label:(0,a.__)("Bank name","woocommerce"),required:!0,...t("bank_name")}),(0,k.jsx)(x.TextControl,{__nextHasNoMarginBottom:!0,label:(0,a.__)("Sort code","woocommerce"),required:!0,...t("sort_code")}),(0,k.jsx)(x.TextControl,{__nextHasNoMarginBottom:!0,label:(0,a.__)("IBAN","woocommerce"),required:!0,...t("iban")}),(0,k.jsx)(x.TextControl,{__nextHasNoMarginBottom:!0,label:(0,a.__)("BIC / Swift","woocommerce"),required:!0,...t("bic")})]}),(0,k.jsx)(h.Button,{variant:"primary",isBusy:e,onClick:o,children:(0,a.__)("Save","woocommerce")})]})})})})},scope:"woocommerce-tasks"});var H=o(3246);const F=({onComplete:e,query:t})=>{const{updatePaymentGateway:o}=(0,r.useDispatch)(c.PAYMENT_GATEWAYS_STORE_NAME),{getPaymentGateway:s,paymentGatewaySuggestions:n,installedPaymentGateways:i,isResolving:d,countryCode:u}=(0,r.useSelect)((e=>{const{getSettings:t}=e(c.settingsStore),{general:o={}}=t("general");return{getPaymentGateway:e(c.PAYMENT_GATEWAYS_STORE_NAME).getPaymentGateway,getOption:e(c.optionsStore).getOption,installedPaymentGateways:e(c.PAYMENT_GATEWAYS_STORE_NAME).getPaymentGateways(),isResolving:e(c.onboardingStore).isResolving("getPaymentGatewaySuggestions"),paymentGatewaySuggestions:e(c.onboardingStore).getPaymentGatewaySuggestions(!0),countryCode:(0,O.gI)(o.woocommerce_default_country)}}),[]),h=(0,m.useMemo)((()=>((e,t)=>{const o=e.reduce(((e,t)=>(e[t.id]=t,e)),{});return t.reduce(((e,t)=>{const s=(0,j.vK)(t.id),n=o[s]?o[s]:{},i={installed:!!o[s],postInstallScripts:n.post_install_scripts,hasPlugins:!(!t.plugins||!t.plugins.length),enabled:n.enabled||!1,needsSetup:n.needs_setup,settingsUrl:n.settings_url,connectionUrl:n.connection_url,setupHelpText:n.setup_help_text,title:n.title,requiredSettings:n.required_settings_keys?n.required_settings_keys.map((e=>n.settings[e])).filter(Boolean):[],...t};return e.set(t.id,i),e}),new Map)})(i,n)),[i,n]),_=(0,m.useCallback)((t=>{t&&s(t)&&o(t,{enabled:!0}).then((()=>{e(h.get(t)?.hasPlugins?{}:{redirectPath:(0,p.getQuery)()?.task?(0,p.getNewPath)({task:(0,p.getQuery)().task},{},"/"):(0,p.getNewPath)({task:"payments"},{},"/")})}))}),[s,o,e,h]),x=(0,m.useCallback)((async e=>{if(!h.get(e))throw`Payment gateway ${e} not found in available gateways list`;(0,l.recordEvent)("tasklist_payment_connect_method",{payment_method:e}),_(e)}),[h,_]),w=(0,m.useMemo)((()=>Array.from(h.values()).filter((e=>e.recommendation_priority)).sort(z).map((e=>e.id)).shift()),[h]),y=(0,m.useMemo)((()=>{if(!t.id||d||!h.size)return null;const e=(0,j.vK)(t.id),o=Array.from(h.entries()).find((([t])=>(0,j.vK)(t)===e))?.[1];if(!o)throw`Current gateway ${t.id} not found in available gateways list`;return o}),[d,t,h]),S=(0,m.useMemo)((()=>((e,t)=>{for(const[,o]of e.entries())if(o.installed&&!o.needsSetup){if(R(o))return!0;if(W(o,t))return!0}return!1})(h,u)),[u,h]),I=-1!==Array.from(h.values()).findIndex(R),[M,b,v,A]=(0,m.useMemo)((()=>((e,t,o,s)=>Array.from(e.values()).sort(z).reduce(((e,n)=>{const[i,a,r,c]=e;return R(n)?o&&("woocommerce_payments:bnpl"===n.id?n.installed&&!n.needsSetup&&c.push(n):n.installed&&!n.needsSetup||i.push(n)):n.is_offline?a.push(n):n.enabled||(s?((e,t)=>e.category_additional&&-1!==e.category_additional.indexOf(t))(n,t)&&r.push(n):W(n,t)&&r.push(n)),e}),[[],[],[],[]]))(h,u,I,S)),[h,u,I,S]);if((0,m.useEffect)((()=>{let e=[];y||(M.length&&e.push(M[0].id),A.length&&e.push(A[0].id),v.length&&(e=e.concat(v.map((e=>e.id)))),e.length&&(0,l.recordEvent)("tasklist_payments_options",{options:e}))}),[v,y,M,A]),t.id&&!y)return(0,k.jsx)(T,{});if(y)return(0,k.jsx)(f,{paymentGateway:y,markConfigured:x});let P=(0,a.__)("Choose a payment provider","woocommerce"),D=(0,a.__)("To start accepting online payments","woocommerce");S?(P=(0,a.__)("Additional payment options","woocommerce"),D=(0,a.__)("Give your customers additional choices in ways to pay.","woocommerce")):I&&(P=(0,a.__)("Other payment providers","woocommerce"),D=(0,a.__)("Try one of the alternative payment providers.","woocommerce"));const E=!!v.length&&(0,k.jsx)(N,{heading:P,headingDescription:D,recommendation:!I&&w,paymentGateways:v,markConfigured:x,footerLink:(0,k.jsx)(H.d,{message:(0,a.__)("Visit the {{Link}}Official WooCommerce Marketplace{{/Link}} to find additional payment providers.","woocommerce"),onClickCallback:()=>{(0,l.recordEvent)("tasklist_payment_see_more",{})},targetUrl:(0,g.getAdminLink)("admin.php?page=wc-admin&tab=extensions&path=/extensions&category=payment-gateways")})}),Y=!!b.length&&(0,k.jsx)(N,{heading:(0,a.__)("Offline payment methods","woocommerce"),recommendation:!I&&w,paymentGateways:b,markConfigured:x});return(0,k.jsxs)("div",{className:"woocommerce-task-payments",children:[!h.size&&(0,k.jsx)(C,{}),M.length?(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(L,{paymentGateway:M[0]}),E,Y]}):(0,k.jsxs)(k.Fragment,{children:[E,!!A.length&&(0,k.jsx)(B,{paymentGateway:A[0]}),Y]})]})};(0,d.registerPlugin)("wc-admin-onboarding-task-payments",{scope:"woocommerce-tasks",render:()=>(0,k.jsx)(u.WooOnboardingTask,{id:"payments",children:({onComplete:e,query:t})=>(0,k.jsx)(F,{onComplete:e,query:t})})});var U=o(29491),G=o(66087),Z=o(36849),Q=o(68191),J=o(62513),V=o(24148),q=o(97897),K=o(94111);const X=({zone:e})=>(0,k.jsx)("div",{className:"woocommerce-shipping-rate__icon",children:e.locations?e.locations.map((e=>(0,k.jsx)(x.Flag,{size:24,code:e.code},e.code))):(0,k.jsx)(V.A,{icon:q.A})}),$=({zone:e,getInputProps:t})=>(0,k.jsxs)("label",{htmlFor:`woocommerce-shipping-rate__toggle-${e.id}`,className:"woocommerce-shipping-rate__name",children:[e.name,(0,k.jsx)(h.FormToggle,{id:`woocommerce-shipping-rate__toggle-${e.id}`,...t(`${e.id}_enabled`)})]}),ee=({zone:e,values:t,setTouched:o,setValue:s,getFormattedRate:n,renderInputPrefix:i,renderInputSuffix:r,inputProps:{className:c,...l}})=>{const d=(0,_.A)("muriel-input-text","woocommerce-shipping-rate__control-wrapper",c);return(0,k.jsxs)(m.Fragment,{children:[!e.toggleable&&(0,k.jsx)("div",{className:"woocommerce-shipping-rate__name",children:e.name}),(!e.toggleable||t[`${e.id}_enabled`])&&(0,k.jsx)(x.TextControlWithAffixes,{__nextHasNoMarginBottom:!0,label:(0,a.__)("Shipping cost","woocommerce"),required:!0,className:d,...l,onBlur:()=>{o(`${e.id}_rate`),s(`${e.id}_rate`,n(t[`${e.id}_rate`]))},prefix:i(),suffix:r(t[`${e.id}_rate`])})]})};class te extends m.Component{constructor(){super(...arguments),this.updateShippingZones=this.updateShippingZones.bind(this),this.getFormattedRate=this.getFormattedRate.bind(this),this.renderInputPrefix=this.renderInputPrefix.bind(this),this.renderInputSuffix=this.renderInputSuffix.bind(this)}getShippingMethods(e,t=null){return e&&e.methods&&Array.isArray(e.methods)?t?e.methods?e.methods.filter((e=>e.method_id===t)):[]:e.methods:[]}disableShippingMethods(e,t){t.length&&t.forEach((t=>{P()({method:"POST",path:`/wc/v3/shipping/zones/${e.id}/methods/${t.instance_id}`,data:{enabled:!1}})}))}async updateShippingZones(e){const{createNotice:t,shippingZones:o}=this.props;let s=!1,n=!1;o.forEach((t=>{0===t.id?s=t.toggleable&&e[`${t.id}_enabled`]:n=""!==e[`${t.id}_rate`]&&parseFloat(e[`${t.id}_rate`])!==parseFloat(0);const o=this.getShippingMethods(t),i=parseFloat(e[`${t.id}_rate`])===parseFloat(0)?"free_shipping":"flat_rate",a=this.getShippingMethods(t,i).length?this.getShippingMethods(t,i)[0]:null;if(!t.toggleable||e[`${t.id}_enabled`]){if(a){const e=o.filter((e=>e.instance_id!==a.instance_id));this.disableShippingMethods(t,e)}P()({method:"POST",path:a?`/wc/v3/shipping/zones/${t.id}/methods/${a.instance_id}`:`/wc/v3/shipping/zones/${t.id}/methods`,data:{method_id:i,enabled:!0,settings:{cost:e[`${t.id}_rate`]}}})}else this.disableShippingMethods(t,o)})),(0,l.recordEvent)("tasklist_shipping_set_costs",{shipping_cost:n,rest_world:s}),t("success",(0,a.__)("Your shipping rates have been updated","woocommerce")),this.props.onComplete()}renderInputPrefix(){const{symbolPosition:e,symbol:t}=this.context.getCurrencyConfig();return 0===e.indexOf("right")?null:(0,k.jsx)("span",{className:"woocommerce-shipping-rate__control-prefix",children:t})}renderInputSuffix(e){const{symbolPosition:t,symbol:o}=this.context.getCurrencyConfig();return 0===t.indexOf("right")?(0,k.jsx)("span",{className:"woocommerce-shipping-rate__control-suffix",children:o}):parseFloat(e)===parseFloat(0)?(0,k.jsx)("span",{className:"woocommerce-shipping-rate__control-suffix",children:(0,a.__)("Free shipping","woocommerce")}):null}getFormattedRate(e){const{formatDecimalString:t}=this.context,o=t(e);return e.length&&o.length?t(e):t(0)}getInitialValues(){const{formatDecimalString:e}=this.context,t={};return this.props.shippingZones.forEach((o=>{const s=this.getShippingMethods(o),n=s.length&&s[0].settings.cost?this.getFormattedRate(s[0].settings.cost.value):e(0);t[`${o.id}_rate`]=n,s.length&&s[0].enabled?t[`${o.id}_enabled`]=!0:t[`${o.id}_enabled`]=!1})),t}validate(e){const t={};return Object.keys(e).filter((e=>e.endsWith("_rate"))).forEach((o=>{e[o]<0&&(t[o]=(0,a.__)("Shipping rates can not be negative numbers.","woocommerce"))})),t}render(){const{buttonText:e,shippingZones:t}=this.props;return t.length?(0,k.jsx)(x.Form,{initialValues:this.getInitialValues(),onSubmit:this.updateShippingZones,validate:this.validate,children:({getInputProps:o,handleSubmit:s,setTouched:n,setValue:i,values:r})=>(0,k.jsxs)(m.Fragment,{children:[(0,k.jsx)("div",{className:"woocommerce-shipping-rates",children:t.map((e=>(0,k.jsxs)("div",{className:"woocommerce-shipping-rate",children:[(0,k.jsx)(X,{zone:e}),(0,k.jsxs)("div",{className:"woocommerce-shipping-rate__main",children:[e.toggleable&&(0,k.jsx)($,{zone:e,getInputProps:o}),(0,k.jsx)(ee,{zone:e,values:r,inputProps:o(`${e.id}_rate`),setTouched:n,setValue:i,getFormattedRate:this.getFormattedRate,renderInputPrefix:this.renderInputPrefix,renderInputSuffix:this.renderInputSuffix})]})]},e.id)))}),(0,k.jsx)(h.Button,{isPrimary:!0,onClick:s,children:e||(0,a.__)("Update","woocommerce")})]})}):null}}te.defaultProps={shippingZones:[]},te.contextType=K.CurrencyContext;const oe=te;var se=o(41040);const ne=({shippingMethod:e})=>(0,k.jsx)(se.y,{features:e.layout_column?.features||[],logo:{image:e.layout_column?.image||""}}),ie=({shippingMethod:e,children:t})=>(0,k.jsx)(se.y,{layout:"dual",features:e.layout_row?.features||[],logo:{image:e.layout_row?.image||""},description:e.description,children:t});var ae=o(98642);class re extends m.Component{constructor(e){super(e),this.initialState={isPending:!1,step:"store_location",shippingZones:[]},this.activePlugins=e.activePlugins,this.state=this.initialState,this.completeStep=this.completeStep.bind(this),this.shippingSmartDefaultsEnabled=window.wcAdminFeatures&&window.wcAdminFeatures["shipping-smart-defaults"],this.storeLocationCompleted=!1,this.shippingPartners=e.shippingPartners,this.jetpackAuthRedirectUrl=(0,g.getAdminLink)("admin.php?page=wc-admin")}componentDidMount(){this.reset()}reset(){this.setState(this.initialState)}async fetchShippingZones(){const{countryCode:e,countryName:t}=this.props,o=[],s=await P()({path:"/wc/v3/shipping/zones"});let n=!1;if(await Promise.all(s.map((async t=>{if(0===t.id)return t.methods=await P()({path:`/wc/v3/shipping/zones/${t.id}/methods`}),t.name=(0,a.__)("Rest of the world","woocommerce"),t.toggleable=!0,void o.push(t);t.locations=await P()({path:`/wc/v3/shipping/zones/${t.id}/locations`}),t.locations.find((t=>e===t.code))&&(t.methods=await P()({path:`/wc/v3/shipping/zones/${t.id}/methods`}),o.push(t),n=!0)}))),!n){const s=await P()({method:"POST",path:"/wc/v3/shipping/zones",data:{name:t}});s.locations=await P()({method:"POST",path:`/wc/v3/shipping/zones/${s.id}/locations`,data:[{code:e,type:"country"}]}),o.push(s)}o.reverse(),this.setState({isPending:!1,shippingZones:o})}componentDidUpdate(e,t){const{countryCode:o,countryName:s,settings:n}=this.props,{woocommerce_store_address:i,woocommerce_default_country:a,woocommerce_store_postcode:r}=n,{step:c}=this.state;"rates"!==c||e.countryCode===o&&e.countryName===s&&"rates"===t.step||(this.setState({isPending:!0}),s&&this.fetchShippingZones());const l=Boolean(i&&a&&r);"store_location"===c&&l&&(this.shippingSmartDefaultsEnabled&&!this.storeLocationCompleted?(this.completeStep(),this.storeLocationCompleted=!0):this.shippingSmartDefaultsEnabled||this.completeStep())}completeStep(){const{createNotice:e,onComplete:t}=this.props,{step:o}=this.state,s=this.getSteps(),n=s.findIndex((e=>e.key===o)),i=s[n+1];i?this.setState({step:i.key}):(e("success",(0,a.__)("📦 Shipping is done! Don’t worry, you can always change it later","woocommerce")),t())}getSteps(){const{countryCode:e,createNotice:t,invalidateResolutionForStoreSelector:o,isJetpackConnected:s,onComplete:n,optimisticallyCompleteTask:i,settings:r,task:c,updateAndPersistSettingsForGroup:m,shippingPartners:d}=this.props,u=d,g=u.map((e=>e.slug)),w=()=>{(0,l.recordEvent)("tasklist_shipping_label_printing",{install:!1,plugins_to_activate:g}),(0,p.getHistory)().push((0,p.getNewPath)({},"/",{})),n()},j=!s&&"US"===e;let y=[{key:"store_location",label:(0,a.__)("Set store location","woocommerce"),description:(0,a.__)("The address from which your business operates","woocommerce"),content:(0,k.jsx)(J.A,{createNotice:t,updateAndPersistSettingsForGroup:m,settings:r,onComplete:e=>{const t=(0,O.gI)(e.countryState);(0,l.recordEvent)("tasklist_shipping_set_location",{country:t}),this.shippingSmartDefaultsEnabled&&this.completeStep()}}),visible:!0},{key:"rates",label:(0,a.__)("Set shipping costs","woocommerce"),description:(0,a.__)("Define how much customers pay to ship to different destinations","woocommerce"),content:(0,k.jsx)(oe,{buttonText:g.length||j?(0,a.__)("Continue","woocommerce"):(0,a.__)("Complete task","woocommerce"),shippingZones:this.state.shippingZones,onComplete:()=>{const{id:e}=c;i(e),o(),this.completeStep()},createNotice:t}),visible:"disabled"!==r.woocommerce_ship_to_countries},{key:"label_printing",label:(0,a.__)("Enable shipping label printing","woocommerce"),description:g.includes("woocommerce-shipstation-integration")?(0,Z.A)({mixedString:(0,a.__)("We recommend using ShipStation to save time at the post office by printing your shipping labels at home. Try ShipStation free for 30 days. {{link}}Learn more{{/link}}.","woocommerce"),components:{link:(0,k.jsx)(x.Link,{href:"https://woocommerce.com/products/shipstation-integration?utm_medium=product",target:"_blank",type:"external"})}}):(0,a.__)("With WooCommerce Shipping you can save time by printing your USPS and DHL Express shipping labels at home","woocommerce"),content:(0,k.jsxs)(k.Fragment,{children:[!s&&g.includes("woocommerce-services")&&(0,k.jsx)(ae.U,{buttonText:(0,a.__)("Install & enable","woocommerce")}),(0,k.jsx)(x.Plugins,{onComplete:(e,t)=>{(0,M.R)(t),(0,l.recordEvent)("tasklist_shipping_label_printing",{install:!0,plugins_to_activate:g}),this.completeStep()},onError:(e,t)=>(0,M.R)(t),onSkip:()=>{(0,l.recordEvent)("tasklist_shipping_label_printing",{install:!1,plugins_to_activate:g}),o(),(0,p.getHistory)().push((0,p.getNewPath)({},"/",{})),n()},pluginSlugs:g})]}),visible:g.length},{key:"connect",label:(0,a.__)("Connect your store","woocommerce"),description:(0,a.__)("Connect your store to WordPress.com to enable label printing","woocommerce"),content:(0,k.jsx)(Q.A,{redirectUrl:this.jetpackAuthRedirectUrl,completeStep:this.completeStep,onConnect:()=>{(0,l.recordEvent)("tasklist_shipping_connect_store")}}),visible:j}];if(this.shippingSmartDefaultsEnabled){const e={rates:{label:(0,a.__)("Review your shipping options","woocommerce"),description:(0,a.__)("We recommend the following shipping options based on your location. You can manage your shipping options again at any time in WooCommerce Shipping settings.","woocommerce"),onClick:"rates"!==this.state.step?()=>{this.setState({step:"rates"})}:void 0,content:(0,k.jsx)(oe,{buttonText:(0,a.__)("Save shipping options","woocommerce"),shippingZones:this.state.shippingZones,onComplete:()=>{const{id:e}=c;i(e),o(),this.completeStep()},createNotice:t})},label_printing:{label:(0,a.__)("Enable shipping label printing and discounted rates","woocommerce"),description:1===u.length?(S=u[0].name,N=u[0].learn_more_link,(0,Z.A)({mixedString:(0,a.sprintf)((0,a.__)("Save time and money by printing your shipping labels right from your computer with %1$s. Try %2$s for free. {{link}}Learn more{{/link}}","woocommerce"),S,S),components:{link:(0,k.jsx)(x.Link,{href:N,target:"_blank",type:"external"})}})):(0,a.__)("Save time and money by printing your shipping labels right from your computer with one of these shipping solutions.","woocommerce"),content:(0,k.jsxs)(k.Fragment,{children:[1===u.length?(0,k.jsx)(ne,{shippingMethod:u[0]}):(0,k.jsx)("div",{className:"woocommerce-task-shipping-recommendation_plugins-install-container",children:u.map((e=>{var t;const s=[e?.slug,...null!==(t=e?.dependencies)&&void 0!==t?t:[]].filter((e=>void 0!==e));return(0,k.jsx)(ie,{shippingMethod:e,children:(0,k.jsx)("div",{className:"woocommerce-task-shipping-recommendations_plugins-buttons",children:(0,k.jsx)(x.Plugins,{onComplete:e=>{(0,M.R)(e),(0,l.recordEvent)("tasklist_shipping_label_printing",{install:!0,plugins_to_activate:s}),o(),this.completeStep()},onError:(e,t)=>(0,M.R)(t),installText:(0,a.__)("Install and enable","woocommerce"),learnMoreLink:e.learn_more_link,onLearnMore:()=>{(0,l.recordEvent)("tasklist_shipping_label_printing_learn_more",{plugin:e.slug})},pluginSlugs:s,installButtonVariant:"secondary"})})},e.name)}))}),1===u.length&&void 0===u[0].slug&&(0,k.jsx)("a",{href:u[0].learn_more_link,target:"_blank",rel:"noreferrer",children:(0,k.jsx)(h.Button,{variant:"primary",children:(0,a.__)("Download","woocommerce")})}),1===u.length&&u[0].slug?(0,k.jsxs)(k.Fragment,{children:[!s&&"woocommerce-services"===u[0].slug&&(0,k.jsx)(ae.U,{buttonText:(0,a.__)("Install and enable","woocommerce")}),(0,k.jsx)(x.Plugins,{onComplete:(e,t)=>{(0,M.R)(t),(0,l.recordEvent)("tasklist_shipping_label_printing",{install:!0,plugins_to_activate:g}),o(),this.completeStep()},onError:(e,t)=>(0,M.R)(t),onSkip:w,pluginSlugs:g,installText:(0,a.__)("Install and enable","woocommerce")})]}):(0,k.jsx)(h.Button,{isTertiary:!0,onClick:w,className:(0,_.A)("woocommerce-task-shipping-recommendations_skip-button",2===u.length?"dual":""),children:(0,a.__)("No Thanks","woocommerce")})]})},store_location:{label:(0,a.__)("Set your store location","woocommerce"),description:(0,a.__)("Add your store location to help us calculate shipping rates and the best shipping options for you. You can manage your store location again at any time in WooCommerce Settings General.","woocommerce"),onClick:"store_location"!==this.state.step?()=>{this.setState({step:"store_location"})}:void 0,buttonText:(0,a.__)("Save store location","woocommerce")}};y=y.map((t=>(e.hasOwnProperty(t.key)&&(t={...t,...e[t.key]}),t.key!==this.state.step&&(t.description=""),t)))}var S,N;return(0,G.filter)(y,(e=>e.visible))}render(){const{isPending:e,step:t}=this.state,{isUpdateSettingsRequesting:o}=this.props,s=this.getSteps();return(0,k.jsxs)("div",{className:"woocommerce-task-shipping",children:[(0,k.jsx)(h.Card,{className:"woocommerce-task-card",children:(0,k.jsx)(h.CardBody,{children:(0,k.jsx)(x.Stepper,{isPending:e||o,isVertical:!0,currentStep:t,steps:s})})}),(0,k.jsx)(H.d,{textProps:{as:"div",className:"woocommerce-task-dashboard__container woocommerce-task-marketplace-link"},message:(0,a.__)("Visit the {{Link}}Official WooCommerce Marketplace{{/Link}} to find more shipping, delivery, and fulfillment solutions.","woocommerce"),eventName:"tasklist_shipping_visit_marketplace_click",targetUrl:(0,g.getAdminLink)("admin.php?page=wc-admin&tab=extensions&path=/extensions&category=shipping-delivery-and-fulfillment")})]})}}const ce=(0,U.compose)((0,r.withSelect)((e=>{const{getSettings:t,isUpdateSettingsRequesting:o}=e(c.settingsStore),{getActivePlugins:s,isJetpackConnected:n}=e(c.pluginsStore),{getCountry:i}=e(c.COUNTRIES_STORE_NAME),{general:a={}}=t("general"),r=(0,O.gI)(a.woocommerce_default_country),l=e(c.shippingMethodsStore).getShippingMethods(),m=r?i(r):null,d=m?m.name:null,u=s();return{countryCode:r,countryName:d,isUpdateSettingsRequesting:o("general"),settings:a,activePlugins:u,isJetpackConnected:n(),shippingPartners:l}})),(0,r.withDispatch)((e=>{const{createNotice:t}=e("core/notices"),{updateAndPersistSettingsForGroup:o}=e(c.settingsStore),{invalidateResolutionForStoreSelector:s,optimisticallyCompleteTask:n}=e(c.onboardingStore);return{createNotice:t,invalidateResolutionForStoreSelector:s,optimisticallyCompleteTask:n,updateAndPersistSettingsForGroup:o}})))(re);(0,d.registerPlugin)("wc-admin-onboarding-task-shipping",{scope:"woocommerce-tasks",render:()=>(0,k.jsx)(u.WooOnboardingTask,{id:"shipping",children:({onComplete:e,task:t})=>(0,k.jsx)(ce,{onComplete:e,task:t})})});const le={marketplace:(0,a.__)("Marketplace","woocommerce")},me=({description:e,imageUrl:t,installAndActivate:o=()=>{},onManage:s=()=>{},isActive:n,isBusy:i,isBuiltByWC:r,isDisabled:c,isInstalled:m,manageUrl:d,name:u,slug:p,tags:_,learnMoreLink:j="",installExternal:y=!1})=>(0,k.jsxs)("div",{className:"woocommerce-plugin-list__plugin",children:[t&&(0,k.jsx)("div",{className:"woocommerce-plugin-list__plugin-logo",children:(0,k.jsx)("img",{src:t,alt:(0,a.sprintf)((0,a.__)("%s logo","woocommerce"),u)})}),(0,k.jsxs)("div",{className:"woocommerce-plugin-list__plugin-text",children:[(0,k.jsxs)(w.Text,{variant:"subtitle.small",as:"h4",children:[u,r&&(0,k.jsx)(x.Pill,{children:(0,a.__)("Built by WooCommerce","woocommerce")}),_?.map((e=>le[e]&&(0,k.jsx)(x.Pill,{children:le[e]},e)))]}),(0,k.jsx)(w.Text,{variant:"subtitle.small",children:e})]}),(0,k.jsxs)("div",{className:"woocommerce-plugin-list__plugin-action",children:[n&&d&&(0,k.jsx)(h.Button,{disabled:c,isBusy:i,variant:"secondary",href:(0,g.getAdminLink)(d),onClick:()=>{(0,l.recordEvent)("marketing_manage",{extension_name:p}),s(p)},children:(0,a.__)("Manage","woocommerce")}),m&&!n&&(0,k.jsx)(h.Button,{disabled:c,isBusy:i,variant:"secondary",onClick:()=>o(p),children:(0,a.__)("Activate","woocommerce")}),!m&&!y&&(0,k.jsx)(h.Button,{disabled:c,isBusy:i,variant:"secondary",onClick:()=>{o(p)},children:(0,a.__)("Get started","woocommerce")}),!m&&y&&(0,k.jsx)(k.Fragment,{children:j?(0,k.jsx)(h.Button,{disabled:c,isBusy:i,variant:"secondary",onClick:()=>{window.open(j,"_blank")},children:(0,a.__)("View extension","woocommerce")}):(0,k.jsx)(h.Button,{disabled:!0,variant:"secondary",children:(0,a.__)("View extension","woocommerce")})})]})]}),de=({currentPlugin:e,installAndActivate:t=()=>{},onManage:o=()=>{},plugins:s=[],title:n})=>(0,k.jsxs)("div",{className:"woocommerce-plugin-list",children:[n&&(0,k.jsx)("div",{className:"woocommerce-plugin-list__title",children:(0,k.jsx)(w.Text,{variant:"sectionheading",as:"h3",children:n})}),s.map((s=>{const{description:n,imageUrl:i,isActive:a,isBuiltByWC:r,isInstalled:c,manageUrl:l,slug:m,name:d,tags:u,learnMoreLink:p,installExternal:g}=s;return(0,k.jsx)(me,{description:n,manageUrl:l,name:d,imageUrl:i,installAndActivate:t,onManage:o,isActive:a,isBuiltByWC:r,isBusy:e===m,isDisabled:!!e,isInstalled:c,slug:m,tags:u,learnMoreLink:p,installExternal:g},m)}))]}),ue=({title:e="",iconSrc:t=`${s.GZ}images/woo-app-icon.svg`,iconAlt:o=(0,a.__)("Woo icon","woocommerce"),name:n=(0,a.__)("WooCommerce Marketplace","woocommerce"),text:i="",buttonHref:r="",buttonText:c="",onButtonClick:d})=>((0,m.useEffect)((()=>{(0,l.recordEvent)("task_marketing_marketplace_promo_shown",{task:"marketing"})}),[]),(0,k.jsxs)(h.Card,{className:"woocommerce-task-card woocommerce-task-promo",children:[e&&(0,k.jsx)(h.CardHeader,{children:(0,k.jsx)(w.Text,{variant:"title.small",as:"h2",className:"woocommerce-task-card__title",children:e})}),(0,k.jsxs)(h.CardBody,{children:[t&&o&&(0,k.jsx)("div",{className:"woocommerce-plugin-list__plugin-logo",children:(0,k.jsx)("img",{src:t,alt:o})}),(0,k.jsxs)("div",{className:"woocommerce-plugin-list__plugin-text",children:[(0,k.jsx)(w.Text,{variant:"subtitle.small",as:"h4",children:n}),(0,k.jsx)(w.Text,{variant:"subtitle.small",children:i})]}),(0,k.jsx)("div",{className:"woocommerce-plugin-list__plugin-action",children:(0,k.jsx)(h.Button,{isSecondary:!0,href:r,onClick:d,children:c})})]})]})),pe=["task-list/grow","task-list/reach"],ge=({onComplete:e})=>{const[t,o]=(0,m.useState)(null),{actionTask:s}=(0,r.useDispatch)(c.onboardingStore),{installAndActivatePlugins:n}=(0,r.useDispatch)(c.pluginsStore),{activePlugins:i,freeExtensions:d,installedPlugins:u,isResolving:_}=(0,r.useSelect)((e=>{const{getActivePlugins:t,getInstalledPlugins:o}=e(c.pluginsStore),{getFreeExtensions:s,hasFinishedResolution:n}=e(c.onboardingStore);return{activePlugins:t(),freeExtensions:s(),installedPlugins:o(),isResolving:!n("getFreeExtensions",[])}}),[]),[x,y]=(0,m.useMemo)((()=>((e,t,o)=>{const s=[],n=[];return e.sort(((e,t)=>pe.indexOf(e.key)-pe.indexOf(t.key))).forEach((e=>{if(!pe.includes(e.key))return;const i=[];if(e.plugins.forEach((e=>{const n=((e,t,o)=>{const{description:s,image_url:n,is_built_by_wc:i,key:a,manage_url:r,name:c,learn_more_link:l,tags:m,install_external:d}=e,u=(0,j.vK)(a);return{description:s,slug:u,imageUrl:n,isActive:t.includes(u),isInstalled:o.includes(u),isBuiltByWC:i,manageUrl:r,name:c,tags:m,learnMoreLink:l,installExternal:d}})(e,t,o);n.isInstalled?s.push(n):i.push(n)})),!i.length)return;const a={...e,plugins:i};n.push(a)})),[s,n]})(d,i,u)),[u,i,d]),S=t=>{o(t),s("marketing"),n([t]).then((s=>{(0,l.recordEvent)("tasklist_marketing_install",{selected_extension:t,installed_extensions:x.map((e=>e.slug)),section_order:y.map((e=>e.key)).join(", ")}),(0,M.R)(s),o(null),e({redirectPath:(0,p.getNewPath)({task:"marketing"})})})).catch((e=>{(0,M.R)(e),o(null)}))},N=()=>{s("marketing")};return _?(0,k.jsx)(h.Spinner,{}):(0,k.jsxs)("div",{className:"woocommerce-task-marketing",children:[!!x.length&&(0,k.jsxs)(h.Card,{className:"woocommerce-task-card",children:[(0,k.jsx)(h.CardHeader,{children:(0,k.jsx)(w.Text,{variant:"title.small",as:"h2",className:"woocommerce-task-card__title",children:(0,a.__)("Installed marketing extensions","woocommerce")})}),(0,k.jsx)(de,{currentPlugin:t,installAndActivate:S,onManage:N,plugins:x})]}),!!y.length&&(0,k.jsxs)(h.Card,{className:"woocommerce-task-card",children:[(0,k.jsxs)(h.CardHeader,{children:[(0,k.jsx)(w.Text,{variant:"title.small",as:"h2",className:"woocommerce-task-card__title",children:(0,a.__)("Recommended marketing extensions","woocommerce")}),(0,k.jsx)(w.Text,{as:"span",children:(0,a.__)('We recommend adding one of the following marketing tools for your store. The extension will be installed and activated for you when you click "Get started".',"woocommerce")})]}),y.map((e=>{const{key:o,title:s,plugins:n}=e;return(0,k.jsx)(de,{currentPlugin:t,installAndActivate:S,onManage:N,plugins:n,title:s},o)}))]}),window?.wcTracks?.isEnabled&&(0,k.jsx)(ue,{title:(0,a.__)("Boost your store's potential","woocommerce"),text:(0,a.__)("Discover hand-picked extensions to grow your business in the official WooCommerce marketplace.","woocommerce"),buttonHref:(0,g.getAdminLink)("admin.php?page=wc-admin&tab=extensions&path=%2Fextensions&category=marketing-extensions"),buttonText:(0,a.__)("Start growing","woocommerce"),onButtonClick:()=>{(0,l.recordEvent)("task_marketing_marketplace_promo_clicked",{task:"marketing"})}})]})};(0,d.registerPlugin)("wc-admin-onboarding-task-marketing",{scope:"woocommerce-tasks",render:()=>(0,k.jsx)(u.WooOnboardingTask,{id:"marketing",children:({onComplete:e})=>(0,k.jsx)(ge,{onComplete:e})})});const he=()=>({onClick:()=>{window.location=(0,g.getAdminLink)("theme-install.php?browse=block-themes")}}),_e=()=>{const{onClick:e}=he();return(0,k.jsx)(u.WooOnboardingTaskListItem,{id:"appearance",children:({defaultTaskItem:t})=>(0,k.jsx)(t,{onClick:e})})};(0,d.registerPlugin)("wc-admin-onboarding-task-appearance",{scope:"woocommerce-tasks",render:()=>(0,k.jsx)(_e,{})});var xe=o(41418);const we=()=>(0,k.jsx)("svg",{width:"13",height:"10",viewBox:"0 0 13 10",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,k.jsx)("path",{d:"M12.1883 1.1814L4.7091 8.66062L1.48438 5.4359",stroke:"#4AB866",strokeWidth:"1.5"})}),je=({name:e,logo:t,description:o,benefits:s,terms:n,actionText:i,onClick:a,isBusy:r,children:c})=>(0,k.jsxs)("div",{className:"woocommerce-tax-partner-card",children:[(0,k.jsx)("div",{className:"woocommerce-tax-partner-card__logo",children:(0,k.jsx)("img",{src:t,alt:e})}),(0,k.jsx)("div",{className:"woocommerce-tax-partner-card__description",children:o}),(0,k.jsx)("ul",{className:"woocommerce-tax-partner-card__benefits",children:s.map(((e,t)=>(0,k.jsxs)("li",{className:"woocommerce-tax-partner-card__benefit",children:[(0,k.jsx)("span",{className:"woocommerce-tax-partner-card__benefit-bullet",children:(0,k.jsx)(we,{})}),(0,k.jsx)("span",{className:"woocommerce-tax-partner-card__benefit-text",children:e})]},t)))}),(0,k.jsxs)("div",{className:"woocommerce-tax-partner-card__action",children:[(0,k.jsx)("div",{className:"woocommerce-tax-partner-card__terms",children:n}),c||(0,k.jsx)(h.Button,{isSecondary:!0,onClick:a,isBusy:r,disabled:r,children:i})]})]}),ke=()=>(0,k.jsx)(je,{name:(0,a.__)("WooCommerce Tax","woocommerce"),logo:"data:image/png;base64,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",description:(0,a.__)("WooCommerce Tax, recommended for new stores","woocommerce"),benefits:[(0,a.__)("Real-time sales tax calculation","woocommerce"),(0,Z.A)({mixedString:(0,a.__)("{{strong}}Single{{/strong}} economic nexus compliance","woocommerce"),components:{strong:(0,k.jsx)("strong",{})}}),(0,a.__)("100% free","woocommerce")],terms:(0,k.jsx)(ae.U,{buttonText:(0,a.__)("Continue setup","woocommerce")}),actionText:(0,a.__)("Continue setup","woocommerce"),onClick:()=>{(0,l.recordEvent)("tasklist_tax_select_option",{selected_option:"woocommerce-tax"}),(0,p.updateQueryString)({partner:"woocommerce-tax"})}}),ye="stripe-tax-for-woocommerce",Se=()=>{window.location.href=(0,g.getAdminLink)("/admin.php?page=wc-settings&tab=stripe_tax_for_woocommerce")},Ne=({task:{additionalData:{stripeTaxActivated:e}={stripeTaxActivated:!1}}})=>{const{createSuccessNotice:t}=(0,r.useDispatch)("core/notices");return(0,k.jsx)(je,{name:(0,a.__)("Stripe Tax","woocommerce"),logo:"data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIGlkPSJMYXllcl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjAiIHk9IjAiIHZpZXdCb3g9IjAgMCA0NjggMjIyLjUiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDQ2OCAyMjIuNSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHN0eWxlPi5zdDB7ZmlsbC1ydWxlOmV2ZW5vZGQ7Y2xpcC1ydWxlOmV2ZW5vZGQ7ZmlsbDojMGEyNTQwfTwvc3R5bGU+PHBhdGggY2xhc3M9InN0MCIgZD0iTTQxNCAxMTMuNGMwLTI1LjYtMTIuNC00NS44LTM2LjEtNDUuOC0yMy44IDAtMzguMiAyMC4yLTM4LjIgNDUuNiAwIDMwLjEgMTcgNDUuMyA0MS40IDQ1LjMgMTEuOSAwIDIwLjktMi43IDI3LjctNi41di0yMGMtNi44IDMuNC0xNC42IDUuNS0yNC41IDUuNS05LjcgMC0xOC4zLTMuNC0xOS40LTE1LjJoNDguOWMwLTEuMy4yLTYuNS4yLTguOXptLTQ5LjQtOS41YzAtMTEuMyA2LjktMTYgMTMuMi0xNiA2LjEgMCAxMi42IDQuNyAxMi42IDE2aC0yNS44ek0zMDEuMSA2Ny42Yy05LjggMC0xNi4xIDQuNi0xOS42IDcuOGwtMS4zLTYuMmgtMjJ2MTE2LjZsMjUtNS4zLjEtMjguM2MzLjYgMi42IDguOSA2LjMgMTcuNyA2LjMgMTcuOSAwIDM0LjItMTQuNCAzNC4yLTQ2LjEtLjEtMjktMTYuNi00NC44LTM0LjEtNDQuOHptLTYgNjguOWMtNS45IDAtOS40LTIuMS0xMS44LTQuN2wtLjEtMzcuMWMyLjYtMi45IDYuMi00LjkgMTEuOS00LjkgOS4xIDAgMTUuNCAxMC4yIDE1LjQgMjMuMyAwIDEzLjQtNi4yIDIzLjQtMTUuNCAyMy40ek0yMjMuOCA2MS43bDI1LjEtNS40VjM2bC0yNS4xIDUuM3pNMjIzLjggNjkuM2gyNS4xdjg3LjVoLTI1LjF6TTE5Ni45IDc2LjdsLTEuNi03LjRoLTIxLjZ2ODcuNWgyNVY5Ny41YzUuOS03LjcgMTUuOS02LjMgMTktNS4ydi0yM2MtMy4yLTEuMi0xNC45LTMuNC0yMC44IDcuNHpNMTQ2LjkgNDcuNmwtMjQuNCA1LjItLjEgODAuMWMwIDE0LjggMTEuMSAyNS43IDI1LjkgMjUuNyA4LjIgMCAxNC4yLTEuNSAxNy41LTMuM1YxMzVjLTMuMiAxLjMtMTkgNS45LTE5LTguOVY5MC42aDE5VjY5LjNoLTE5bC4xLTIxLjd6TTc5LjMgOTQuN2MwLTMuOSAzLjItNS40IDguNS01LjQgNy42IDAgMTcuMiAyLjMgMjQuOCA2LjRWNzIuMmMtOC4zLTMuMy0xNi41LTQuNi0yNC44LTQuNkM2Ny41IDY3LjYgNTQgNzguMiA1NCA5NS45YzAgMjcuNiAzOCAyMy4yIDM4IDM1LjEgMCA0LjYtNCA2LjEtOS42IDYuMS04LjMgMC0xOC45LTMuNC0yNy4zLTh2MjMuOGM5LjMgNCAxOC43IDUuNyAyNy4zIDUuNyAyMC44IDAgMzUuMS0xMC4zIDM1LjEtMjguMi0uMS0yOS44LTM4LjItMjQuNS0zOC4yLTM1Ljd6Ii8+PC9zdmc+",description:(0,a.__)("Powerful global tax tool","woocommerce"),benefits:[(0,a.__)("Real-time sales tax calculation","woocommerce"),(0,a.__)("Multi-economic nexus compliance","woocommerce"),(0,a.__)("Detailed tax transaction reports","woocommerce"),(0,a.__)("Coverage in over 55 countries","woocommerce")],terms:(0,a.__)("Free to install, then pay as you go.","woocommerce"),onClick:()=>{},children:e?(0,k.jsx)(h.Button,{variant:"secondary",onClick:()=>{(0,l.recordEvent)("tasklist_tax_setup_stripe_tax_to_settings"),Se()},children:(0,a.__)("Continue to setttings","woocommerce")}):(0,k.jsx)(x.Plugins,{installText:(0,a.__)("Install for free","woocommerce"),onClick:()=>{(0,l.recordEvent)("tasklist_tax_select_option",{selected_option:ye})},onComplete:()=>{(0,l.recordEvent)("tasklist_tax_install_plugin_success",{selected_option:ye});const{updateAndPersistSettingsForGroup:e}=(0,r.dispatch)(c.settingsStore);e("general",{general:{woocommerce_calc_taxes:"yes"}}).then((()=>{t((0,a.__)("Stripe Tax for Woocommerce has been successfully installed. Let's configure it now.","woocommerce")),Se()}))},onError:(e,t)=>{(0,l.recordEvent)("tasklist_tax_install_plugin_error",{selected_option:ye,errors:e}),(0,M.R)(t)},installButtonVariant:"secondary",pluginSlugs:[ye]})})},Ie=({isPending:e,onManual:t})=>{const{generalSettings:o}=(0,r.useSelect)((e=>{const{getSettings:t}=e(c.settingsStore);return{generalSettings:t("general")?.general}}),[]);return(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(h.Button,{isPrimary:!0,disabled:e,isBusy:e,onClick:()=>{(0,l.recordEvent)("tasklist_tax_config_rates",{}),t()},children:(0,a.__)("Configure","woocommerce")}),(0,k.jsx)("p",{children:"yes"!==o?.woocommerce_calc_taxes&&(0,Z.A)({mixedString:(0,a.__)('By clicking "Configure" you\'re enabling tax rates and calculations. More info {{link}}here{{/link}}.',"woocommerce"),components:{link:(0,k.jsx)(x.Link,{href:"https://woocommerce.com/document/setting-up-taxes-in-woocommerce/?utm_medium=product#section-1",target:"_blank",type:"external",children:(0,k.jsx)(k.Fragment,{})})}})})]})},Ce=e=>{const t=(0,J.a)(e);return document.getElementById("woocommerce-store-address-form-address_1")&&!e.addressLine1.trim().length&&(t.addressLine1=(0,a.__)("Please enter an address","woocommerce")),document.getElementById("woocommerce-store-address-form-postcode")&&!e.postCode.trim().length&&(t.postCode=(0,a.__)("Please enter a post code","woocommerce")),document.getElementById("woocommerce-store-address-form-city")&&!e.city.trim().length&&(t.city=(0,a.__)("Please enter a city","woocommerce")),t},Me=({nextStep:e})=>{const{createNotice:t}=(0,r.useDispatch)("core/notices"),{updateAndPersistSettingsForGroup:o}=(0,r.useDispatch)(c.settingsStore),{generalSettings:s,isResolving:n,isUpdating:i}=(0,r.useSelect)((e=>{const{getSettings:t,hasFinishedResolution:o,isUpdateSettingsRequesting:s}=e(c.settingsStore);return{generalSettings:t("general")?.general,isResolving:!o("getSettings",["general"]),isUpdating:s("general")}}),[]);return(0,m.useEffect)((()=>{n||i||!(0,xe.Ud)(s||{},Boolean(document.getElementById("woocommerce-store-address-form-postcode")))||e()}),[n,s,i]),n?null:(0,k.jsx)(J.A,{validate:Ce,onComplete:e=>{const t=(0,O.gI)(e.countryState);(0,l.recordEvent)("tasklist_tax_set_location",{country:t})},isSettingsRequesting:!1,settings:s,updateAndPersistSettingsForGroup:o,createNotice:t})},be=({isPending:e,onDisable:t,onAutomate:o,onManual:s})=>{const[n,i]=(0,m.useState)(0),r={isPending:e,onAutomate:o,onDisable:t,nextStep:()=>{i(n+1)},onManual:s},c=[{key:"store_location",label:(0,a.__)("Set store location","woocommerce"),description:(0,a.__)("The address from which your business operates","woocommerce"),content:(0,k.jsx)(Me,{...r})},{key:"manual_configuration",label:(0,a.__)("Configure tax rates","woocommerce"),description:(0,a.__)("Head over to the tax rate settings screen to configure your tax rates","woocommerce"),content:(0,k.jsx)(Ie,{...r})}],l=c[n];return(0,k.jsx)(x.Stepper,{isVertical:!0,currentStep:l.key,steps:c})},ve=({children:e,isPending:t,onManual:o,onDisable:s})=>{const n=(0,_.A)("woocommerce-task-card","woocommerce-tax-partners",`woocommerce-tax-partners__partners-count-${m.Children.count(e)}`);return(0,k.jsxs)(k.Fragment,{children:[(0,k.jsxs)(h.Card,{className:n,children:[(0,k.jsx)(h.CardHeader,{children:(0,a.__)("Choose a tax partner","woocommerce")}),(0,k.jsxs)(h.CardBody,{children:[(0,k.jsx)("div",{className:"woocommerce-tax-partners__partners",children:e}),(0,k.jsxs)("ul",{className:"woocommerce-tax-partners__other-actions",children:[(0,k.jsx)("li",{children:(0,k.jsx)(h.Button,{isTertiary:!0,disabled:t,isBusy:t,onClick:()=>{o()},children:(0,a.__)("Set up taxes manually","woocommerce")})}),(0,k.jsx)("li",{children:(0,k.jsx)(h.Button,{isTertiary:!0,disabled:t,isBusy:t,onClick:()=>{s()},children:(0,a.__)("I don’t charge sales tax","woocommerce")})})]})]})]}),(0,k.jsx)(H.d,{textProps:{as:"div",className:"woocommerce-task-dashboard__container woocommerce-task-marketplace-link"},message:(0,a.__)("Visit the {{Link}}Official WooCommerce Marketplace{{/Link}} to find more tax solutions.","woocommerce"),eventName:"tasklist_tax_visit_marketplace_click",targetUrl:(0,g.getAdminLink)("admin.php?page=wc-admin&tab=extensions&path=/extensions&category=operations")})]})},fe=({isPending:e,onAutomate:t,onManual:o,onDisable:s})=>(0,k.jsxs)("div",{className:"woocommerce-task-tax__success",children:[(0,k.jsx)("span",{className:"woocommerce-task-tax__success-icon",role:"img","aria-labelledby":"woocommerce-task-tax__success-message",children:"🎊"}),(0,k.jsx)(x.H,{id:"woocommerce-task-tax__success-message",children:(0,a.__)("Good news!","woocommerce")}),(0,k.jsx)("p",{children:(0,Z.A)({mixedString:(0,a.__)("{{strong}}WooCommerce Tax{{/strong}} can automate your sales tax calculations for you.","woocommerce"),components:{strong:(0,k.jsx)("strong",{})}})}),(0,k.jsx)(h.Button,{isPrimary:!0,isBusy:e,onClick:()=>{(0,l.recordEvent)("tasklist_tax_setup_automated_proceed",{setup_automatically:!0}),t()},children:(0,a.__)("Yes please","woocommerce")}),(0,k.jsx)(h.Button,{disabled:e,isTertiary:!0,onClick:()=>{(0,l.recordEvent)("tasklist_tax_setup_automated_proceed",{setup_automatically:!1}),o()},children:(0,a.__)("No thanks, I'll set up manually","woocommerce")}),(0,k.jsx)(h.Button,{disabled:e,isTertiary:!0,onClick:s,children:(0,a.__)("I don't charge sales tax","woocommerce")})]}),Te=({onDisable:e,onManual:t})=>(0,k.jsx)(Q.A,{onConnect:()=>{(0,l.recordEvent)("tasklist_tax_connect_store",{connect:!0,no_tax:!1})},onSkip:()=>{(0,l.queueRecordEvent)("tasklist_tax_connect_store",{connect:!1,no_tax:!1}),t()},skipText:(0,a.__)("Set up tax rates manually","woocommerce"),onAbort:()=>e(),abortText:(0,a.__)("My business doesn't charge sales tax","woocommerce")}),Ae=e=>"object"==typeof e&&null!==e,Pe=({nextStep:e,onDisable:t,onManual:o,pluginsToActivate:s})=>{const{updateOptions:n}=(0,r.useDispatch)(c.optionsStore),{isResolving:i,tosAccepted:d}=(0,r.useSelect)((e=>{const{getOption:t,hasFinishedResolution:o}=e(c.optionsStore),s=t("wc_connect_options");return{isResolving:!o("getOption",["woocommerce_setup_jetpack_opted_in"])||!o("getOption",["wc_connect_options"]),tosAccepted:Ae(s)&&s?.tos_accepted||"1"===t("woocommerce_setup_jetpack_opted_in")}}),[]);return(0,m.useEffect)((()=>{d&&!s.length&&e()}),[i,e,s.length,d]),i?null:(0,k.jsxs)(k.Fragment,{children:[!d&&(0,k.jsx)(ae.U,{buttonText:(0,a.__)("Install & enable","woocommerce")}),(0,k.jsx)(x.Plugins,{onComplete:(t,o)=>{(0,M.R)(o),(0,l.recordEvent)("tasklist_tax_install_extensions",{install_extensions:!0}),n({woocommerce_setup_jetpack_opted_in:!0}),e()},onError:(e,t)=>(0,M.R)(t),onSkip:()=>{(0,l.queueRecordEvent)("tasklist_tax_install_extensions",{install_extensions:!1}),o()},skipText:(0,a.__)("Set up manually","woocommerce"),onAbort:()=>t(),abortText:(0,a.__)("I don't charge sales tax","woocommerce"),pluginSlugs:s})]})},Le=({isPending:e,onDisable:t,onAutomate:o,onManual:s})=>{const[n,i]=(0,m.useState)([]),{activePlugins:l,isResolving:d}=(0,r.useSelect)((e=>{const{getSettings:t}=e(c.settingsStore),{hasFinishedResolution:o}=e(c.optionsStore),{getActivePlugins:s}=e(c.pluginsStore);return{activePlugins:s(),generalSettings:t("general")?.general,isResolving:!o("getOption",["woocommerce_setup_jetpack_opted_in"])||!o("getOption",["wc_connect_options"])}}),[]),[u,p]=(0,m.useState)(0);(0,m.useEffect)((()=>{const e=(0,G.difference)(xe.Bo,l);e.length<=n.length||i(e)}),[l,n.length]);const g={isPending:e,isResolving:d,onAutomate:o,onDisable:t,nextStep:()=>{p(u+1)},onManual:s,pluginsToActivate:n},h=[{key:"store_location",label:(0,a.__)("Set store location","woocommerce"),description:(0,a.__)("The address from which your business operates","woocommerce"),content:(0,k.jsx)(Me,{...g})},{key:"plugins",label:(0,a.__)("Install WooCommerce Tax","woocommerce"),description:(0,a.__)("WooCommerce Tax allows you to automate sales tax calculations","woocommerce"),content:(0,k.jsx)(Pe,{...g})},{key:"connect",label:(0,a.__)("Connect your store","woocommerce"),description:(0,a.__)("Connect your store to WordPress.com to enable automated sales tax calculations","woocommerce"),content:(0,k.jsx)(Te,{...g})}],_=h[u];return(0,k.jsx)(x.Stepper,{isPending:d,isVertical:!0,currentStep:_.key,steps:h})},De=({isPending:e,onAutomate:t,onManual:o,onDisable:s})=>{const{generalSettings:n,isJetpackConnected:i,isResolving:a,pluginsToActivate:l}=(0,r.useSelect)((e=>{const{getSettings:t}=e(c.settingsStore),{getActivePlugins:o,hasFinishedResolution:s}=e(c.pluginsStore),n=o();return{generalSettings:t("general").general,isJetpackConnected:e(c.pluginsStore).isJetpackConnected(),isResolving:!s("isJetpackConnected",void 0)||!e(c.settingsStore).hasFinishedResolution("getSettings",["general"])||!s("getActivePlugins",void 0),pluginsToActivate:(0,G.difference)(xe.Bo,n)}}),[]);if(a)return(0,k.jsx)(x.Spinner,{});const m={isPending:e,onAutomate:t,onManual:o,onDisable:s};return(0,xe.Ud)(n||{})&&!l.length&&i?(0,k.jsx)(fe,{...m}):(0,k.jsx)(Le,{...m})},Ee=({children:e})=>(0,k.jsx)(h.Card,{className:"woocommerce-task-card",children:(0,k.jsx)(h.CardBody,{children:e})}),Be=({onComplete:e,query:t,task:o})=>{const[s,n]=(0,m.useState)(!1),{updateOptions:i}=(0,r.useDispatch)(c.optionsStore),{createNotice:d}=(0,r.useDispatch)("core/notices"),{updateAndPersistSettingsForGroup:u}=(0,r.useDispatch)(c.settingsStore),{generalSettings:p,isResolving:_,taxSettings:x}=(0,r.useSelect)((e=>{const{getSettings:t,hasFinishedResolution:o}=e(c.settingsStore);return{generalSettings:t("general").general,isResolving:!o("getSettings",["general"]),taxSettings:t("tax").tax||{}}}),[]),w=(0,m.useCallback)((async()=>{n(!0),"yes"!==p?.woocommerce_calc_taxes?(u("tax",{tax:{...x,wc_connect_taxes_enabled:"no"}}),u("general",{general:{...p,woocommerce_calc_taxes:"yes"}}).then((()=>(0,xe.El)())).catch((e=>{n(!1),(0,M.R)(e)}))):(0,xe.El)()}),[p,x,u]),j=(0,m.useCallback)((async()=>{n(!0);try{await Promise.all([u("tax",{tax:{...x,wc_connect_taxes_enabled:"yes"}}),u("general",{general:{...p,woocommerce_calc_taxes:"yes"}})])}catch(e){return n(!1),void d("error",(0,a.__)("There was a problem setting up automated taxes. Please try again.","woocommerce"))}d("success",(0,a.__)("You’re awesome! One less item on your to-do list ✅","woocommerce")),e()}),[d,p,e,x,u]),y=(0,m.useCallback)((()=>{n(!0),(0,l.queueRecordEvent)("tasklist_tax_connect_store",{connect:!1,no_tax:!0}),i({woocommerce_no_sales_tax:!0,woocommerce_calc_taxes:"no"}).then((()=>{window.location.href=(0,g.getAdminLink)("admin.php?page=wc-admin")}))}),[i]),S=(0,m.useMemo)((()=>{const e=(0,O.gI)(p?.woocommerce_default_country)||"",{additionalData:{woocommerceTaxCountries:t=[],stripeTaxCountries:s=[],taxJarActivated:n,woocommerceTaxActivated:i,woocommerceShippingActivated:a}={}}=o;return[{id:"woocommerce-tax",card:ke,component:De,isVisible:!n&&!i&&!a&&t.includes(e)},{id:"stripe-tax",card:Ne,isVisible:s.includes(e)}].filter((e=>e.isVisible))}),[]),{auto:N}=t;(0,m.useEffect)((()=>{"true"===N&&j()}),[N,j]),(0,m.useEffect)((()=>{t.partner||(0,l.recordEvent)("tasklist_tax_view_options",{options:S.map((e=>e.id))})}),[S,t.partner]);const I=(0,m.useMemo)((()=>t.partner&&S.find((e=>e.id===t.partner))||null),[S,t.partner]),C={isPending:s,onAutomate:j,onManual:w,onDisable:y,task:o};return _?(0,k.jsx)(h.Spinner,{}):S.length?I?(0,k.jsx)(Ee,{children:I.component&&(0,m.createElement)(I.component,C)}):(0,k.jsx)(ve,{...C,children:S.map((e=>e.card&&(0,m.createElement)(e.card,{key:e.id,...C})))}):(0,k.jsx)(Ee,{children:(0,k.jsx)(be,{...C})})};(0,d.registerPlugin)("wc-admin-onboarding-task-tax",{scope:"woocommerce-tasks",render:()=>(0,k.jsx)(u.WooOnboardingTask,{id:"tax",children:({onComplete:e,query:t,task:o})=>(0,k.jsx)(Be,{onComplete:e,query:t,task:o})})}),(0,d.registerPlugin)("woocommerce-admin-task-wcpay-page",{scope:"woocommerce-tasks",render:()=>(0,k.jsx)(u.WooOnboardingTask,{id:"woocommerce-payments",children:({onComplete:e,query:t})=>(0,k.jsx)(F,{onComplete:e,query:t})})});var Oe=o(99915);const ze=()=>{const{isResolving:e,deprecatedTasks:t}=(0,r.useSelect)((e=>{const t=e(c.onboardingStore).getTaskLists();if(!t||0===t.length)return{isResolving:!1,deprecatedTasks:[]};const o=[];for(const e of t)for(const t of e.tasks)"isDeprecated"in t&&t.isDeprecated&&"container"in t&&t.container&&o.push(t);return{isResolving:e(c.onboardingStore).isResolving("getTaskLists",[]),deprecatedTasks:o}}),[]);return e?null:(0,k.jsx)(k.Fragment,{children:t.map((e=>(0,k.jsx)(u.WooOnboardingTask,{id:"id"in e?e.id:e.key,children:()=>"container"in e?e.container:null},"id"in e?e.id:e.key)))})};(0,d.registerPlugin)("wc-admin-deprecated-task-container",{scope:"woocommerce-tasks",render:()=>(0,Oe.EM)("setup")||(0,Oe.EM)("extended")?(0,k.jsx)(ze,{}):null}),(0,d.registerPlugin)("woocommerce-admin-task-launch-your-store",{scope:"woocommerce-tasks",render:()=>(0,k.jsx)(u.WooOnboardingTaskListItem,{id:"launch-your-store",children:({defaultTaskItem:e,isComplete:t})=>(0,k.jsx)(e,{isClickable:!t})})}),(async()=>{i()?Promise.all([o.e(9670),o.e(3699)]).then(o.bind(o,94328)):Promise.all([o.e(9670),o.e(5841)]).then(o.bind(o,57506))})(),window.wcAdminFeatures&&window.wcAdminFeatures["shipping-smart-defaults"]&&o.e(6568).then(o.bind(o,46568));var Re=o(72744),We=o(63861),Ye=o(45155);var He=o(97687);var Fe=o(18537),Ue=o(7905);class Ge extends m.Component{constructor(e){super(e),this.state={purchaseNowButtonBusy:!1,purchaseLaterButtonBusy:!1}}onClickPurchaseNow(){const{productIds:e,onClickPurchaseNow:t}=this.props;if(this.setState({purchaseNowButtonBusy:!0}),!e.length)return;(0,l.recordEvent)("tasklist_modal_proceed_checkout",{product_ids:e,purchase_install:!0});const o=(0,Ue.r)("https://woocommerce.com/cart?utm_medium=product",{"wccom-replace-with":e.join(",")});t?t(o):window.location=o}onClickPurchaseLater(){const{productIds:e}=this.props;(0,l.recordEvent)("tasklist_modal_proceed_checkout",{product_ids:e,purchase_install:!1}),this.setState({purchaseLaterButtonBusy:!0}),this.props.onClickPurchaseLater()}onClose(){const{onClose:e,productIds:t}=this.props;(0,l.recordEvent)("tasklist_modal_proceed_checkout",{product_ids:t,purchase_install:!1}),e()}renderProducts(){const{productIds:e,productTypes:t}=this.props,{themes:o=[]}=(0,s.Qk)("onboarding",{}),n=[];return e.forEach((e=>{const s=(0,G.find)(t,(t=>t.product===e));s&&n.push({title:s.label,content:s.description});const i=(0,G.find)(o,(t=>t.id===e));i&&n.push({title:(0,a.sprintf)((0,a.__)("%1$s — %2$s per year","woocommerce"),i.title,(0,Fe.decodeEntities)(i.price)),content:(0,k.jsx)("span",{dangerouslySetInnerHTML:(0,b.Ay)(i.excerpt)})})})),(0,k.jsx)(x.List,{items:n})}render(){const{purchaseNowButtonBusy:e,purchaseLaterButtonBusy:t}=this.state;return(0,k.jsxs)(h.Modal,{title:(0,a.__)("Would you like to add the following paid features to your store now?","woocommerce"),onRequestClose:()=>this.onClose(),className:"woocommerce-cart-modal",children:[this.renderProducts(),(0,k.jsx)("p",{className:"woocommerce-cart-modal__help-text",children:(0,a.__)("You won’t have access to this functionality until the extensions have been purchased and installed.","woocommerce")}),(0,k.jsxs)("div",{className:"woocommerce-cart-modal__actions",children:[(0,k.jsx)(h.Button,{isLink:!0,isBusy:t,onClick:()=>this.onClickPurchaseLater(),children:(0,a.__)("I’ll do it later","woocommerce")}),(0,k.jsx)(h.Button,{isPrimary:!0,isBusy:e,onClick:()=>this.onClickPurchaseNow(),children:(0,a.__)("Buy now","woocommerce")})]})]})}}const Ze=(0,U.compose)((0,r.withSelect)((e=>{const{getInstalledPlugins:t}=e(c.pluginsStore),{getProductTypes:o,getProfileItems:s}=e(c.onboardingStore),n=s(),i=t(),a=o();return{profileItems:n,productIds:(0,O.Dr)(a,n,!1,i),productTypes:a}})))(Ge),Qe={store_details:({task:e,goToTask:t})=>(0,k.jsxs)("div",{className:"woocommerce-task-header__contents-container",children:[(0,k.jsx)("img",{alt:(0,a.__)("Store location illustration","woocommerce"),src:s.GZ+"images/task_list/store-details-illustration.png",className:"svg-background"}),(0,k.jsxs)("div",{className:"woocommerce-task-header__contents",children:[(0,k.jsx)("h1",{children:(0,a.__)("First, tell us about your store","woocommerce")}),(0,k.jsx)("p",{children:(0,a.__)("Get your store up and running in no time. Add your store’s address to set up shipping, tax and payments faster.","woocommerce")}),(0,k.jsx)(h.Button,{isSecondary:e.isComplete,isPrimary:!e.isComplete,onClick:t,children:(0,a.__)("Add details","woocommerce")})]})]}),"customize-store":({task:e,goToTask:t})=>{const o=(0,He.E)()?(0,a.__)("Use our built-in AI tools to design your store and populate it with content, or select a pre-built theme and customize it to fit your brand.","woocommerce"):(0,a.__)("Quickly create a beautiful looking store using our built-in store designer, or select a pre-built theme and customize it to fit your brand.","woocommerce");return(0,k.jsxs)("div",{className:`woocommerce-task-header__contents-container woocommerce-task-header__${e.id}`,children:[(0,k.jsx)("img",{alt:(0,a.__)("Customize your store illustration","woocommerce"),src:s.GZ+"images/task_list/customize-store-illustration.svg",className:"svg-background"}),(0,k.jsxs)("div",{className:"woocommerce-task-header__contents",children:[(0,k.jsx)("h1",{children:(0,a.__)("Start customizing your store","woocommerce")}),(0,k.jsx)("p",{children:o}),(0,k.jsx)(h.Button,{isSecondary:e.isComplete,isPrimary:!e.isComplete,onClick:t,children:(0,a.__)("Start customizing","woocommerce")})]})]})},tax:({task:e,goToTask:t})=>(0,k.jsxs)("div",{className:"woocommerce-task-header__contents-container",children:[(0,k.jsx)("img",{alt:(0,a.__)("Tax illustration","woocommerce"),src:s.GZ+"images/task_list/tax-illustration.svg",className:"svg-background"}),(0,k.jsxs)("div",{className:"woocommerce-task-header__contents",children:[(0,k.jsx)("h1",{children:(0,a.__)("Configure your tax settings","woocommerce")}),(0,k.jsx)("p",{children:(0,a.__)("Choose to set up your tax rates manually, or use one of our tax automation tools.","woocommerce")}),(0,k.jsx)(h.Button,{isSecondary:e.isComplete,isPrimary:!e.isComplete,onClick:t,children:(0,a.__)("Collect sales tax","woocommerce")})]})]}),shipping:({task:e,goToTask:t})=>(0,k.jsxs)("div",{className:"woocommerce-task-header__contents-container",children:[(0,k.jsx)("img",{alt:(0,a.__)("Shipping illustration","woocommerce"),src:s.GZ+"images/task_list/shipping-illustration.svg",className:"svg-background"}),(0,k.jsxs)("div",{className:"woocommerce-task-header__contents",children:[(0,k.jsx)("h1",{children:(0,a.__)("Get your products shipped","woocommerce")}),(0,k.jsx)("p",{children:(0,a.__)("Choose where and how you’d like to ship your products, along with any fixed or calculated rates.","woocommerce")}),(0,k.jsx)(h.Button,{isSecondary:e.isComplete,isPrimary:!e.isComplete,onClick:t,children:(0,a.__)("Start shipping","woocommerce")})]})]}),marketing:({task:e,goToTask:t})=>(0,k.jsxs)("div",{className:"woocommerce-task-header__contents-container",children:[(0,k.jsx)("img",{alt:(0,a.__)("Marketing illustration","woocommerce"),src:s.GZ+"images/task_list/sales-illustration.svg",className:"svg-background"}),(0,k.jsxs)("div",{className:"woocommerce-task-header__contents",children:[(0,k.jsx)("h1",{children:(0,a.__)("Reach more customers","woocommerce")}),(0,k.jsx)("p",{children:(0,a.__)("Start growing your business by showcasing your products on social media and Google, boost engagement with email marketing, and more!","woocommerce")}),(0,k.jsx)(h.Button,{isSecondary:e.isComplete,isPrimary:!e.isComplete,onClick:t,children:(0,a.__)("Grow your business","woocommerce")})]})]}),appearance:({task:e})=>{const{onClick:t}=he(),o=e.title,n=e.content,i=e.actionLabel;return(0,k.jsxs)("div",{className:"woocommerce-task-header__contents-container",children:[(0,k.jsx)("img",{alt:(0,a.__)("Appearance illustration","woocommerce"),src:s.GZ+"images/task_list/expand-section-illustration.png",className:"svg-background"}),(0,k.jsxs)("div",{className:"woocommerce-task-header__contents",children:[(0,k.jsx)("h1",{children:o}),(0,k.jsx)("p",{children:n}),(0,k.jsx)(h.Button,{isSecondary:e.isComplete,isPrimary:!e.isComplete,onClick:t,children:i})]})]})},payments:({task:e,goToTask:t})=>(0,k.jsxs)("div",{className:"woocommerce-task-header__contents-container",children:[(0,k.jsx)("img",{alt:(0,a.__)("Payment illustration","woocommerce"),src:s.GZ+"images/task_list/payment-illustration.svg",className:"svg-background"}),(0,k.jsxs)("div",{className:"woocommerce-task-header__contents",children:[(0,k.jsx)("h1",{children:(0,a.__)("It’s time to get paid","woocommerce")}),(0,k.jsx)("p",{children:(0,a.__)("Give your customers an easy and convenient way to pay! Set up one (or more!) of our fast and secure online or in person payment methods.","woocommerce")}),(0,k.jsx)(h.Button,{isSecondary:e.isComplete,isPrimary:!e.isComplete,onClick:t,children:(0,a.__)("Get paid","woocommerce")})]})]}),products:({task:e,goToTask:t})=>{const o=i();return(0,k.jsxs)("div",{className:"woocommerce-task-header__contents-container",children:[(0,k.jsx)("img",{alt:(0,a.__)("Products illustration","woocommerce"),src:s.GZ+"images/task_list/sales-section-illustration.svg",className:"svg-background"}),(0,k.jsxs)("div",{className:"woocommerce-task-header__contents",children:[(0,k.jsx)("h1",{children:o?(0,a.__)("Import your products","woocommerce"):(0,a.__)("List your products","woocommerce")}),(0,k.jsx)("p",{children:(0,a.__)("Start selling by adding products or services to your store. Choose to list products manually, or import them from a different store. ","woocommerce")}),(0,k.jsx)(h.Button,{isSecondary:e.isComplete,isPrimary:!e.isComplete,onClick:t,children:o?(0,a.__)("Import products","woocommerce"):(0,a.__)("Add products","woocommerce")})]})]})},purchase:({task:e})=>{const[t,o]=(0,m.useState)(!1),n=(0,m.useCallback)((()=>{t||(0,l.recordEvent)("tasklist_purchase_extensions"),o(!t)}),[t]);return(0,k.jsxs)("div",{className:"woocommerce-task-header__contents-container",children:[(0,k.jsx)("img",{alt:(0,a.__)("Purchase illustration","woocommerce"),src:s.GZ+"images/task_list/purchase-illustration.png",className:"svg-background"}),(0,k.jsxs)("div",{className:"woocommerce-task-header__contents",children:[(0,k.jsx)("h1",{children:e.title}),(0,k.jsx)("p",{children:(0,a.__)("Good choice! You chose to add amazing new features to your store. Continue to checkout to complete your purchase.","woocommerce")}),(0,k.jsx)(h.Button,{isSecondary:e.isComplete,isPrimary:!e.isComplete,onClick:n,children:(0,a.__)("Continue","woocommerce")})]}),t&&(0,k.jsx)(Ze,{onClose:()=>n(),onClickPurchaseLater:()=>n()})]})},"woocommerce-payments":({task:e,trackClick:t})=>{const o=(0,s.Qk)("wcpayWelcomePageIncentive")||window.wcpaySettings?.connectIncentive,{createNotice:n}=(0,r.useDispatch)("core/notices"),[i,l]=(0,m.useState)(!1);return(0,k.jsxs)("div",{className:"woocommerce-task-header__contents-container",children:[(0,k.jsx)("img",{alt:(0,a.__)("Payment illustration","woocommerce"),src:s.GZ+"images/task_list/payment-illustration.svg",className:"svg-background"}),(0,k.jsxs)("div",{className:"woocommerce-task-header__contents",children:[(0,k.jsx)("h1",{children:(0,a.__)("It’s time to get paid","woocommerce")}),o?.task_header_content?(0,k.jsx)("p",{dangerouslySetInnerHTML:(0,b.Ay)(o.task_header_content)}):(0,k.jsx)("p",{children:(0,a.__)("Power your payments with a simple, all-in-one option. Verify your business details to start managing transactions with WooCommerce Payments.","woocommerce")}),(0,k.jsx)(h.Button,{isSecondary:e.isComplete,isPrimary:!e.isComplete,isBusy:i,disabled:i,onClick:()=>{t(),((e,t)=>{const o=(0,a.__)("There was an error connecting to WooPayments. Please try again or connect later in store settings.","woocommerce");t(!0),P()({path:c.WC_ADMIN_NAMESPACE+"/plugins/connect-wcpay",method:"POST"}).then((e=>{window.location=e.connectUrl})).catch((()=>{e("error",o),t(!1)}))})(n,l)},children:(0,a.__)("Get paid","woocommerce")})]})]})},"launch-your-store":({task:e,goToTask:t})=>(0,k.jsxs)("div",{className:`woocommerce-task-header__contents-container woocommerce-task-header__${e.id}`,children:[(0,k.jsx)("img",{alt:(0,a.__)("Launch Your Store illustration","woocommerce"),src:s.GZ+"images/task_list/launch-your-store-illustration.svg",className:"svg-background"}),(0,k.jsxs)("div",{className:"woocommerce-task-header__contents",children:[(0,k.jsx)("h1",{children:(0,a.__)("Your store is ready for launch!","woocommerce")}),(0,k.jsx)("p",{children:(0,a.__)("It’s time to celebrate – you’re ready to launch your store! Woo! Hit the button to preview your store and make it public.","woocommerce")}),(0,k.jsx)(h.Button,{variant:e.isComplete?"secondary":"primary",onClick:t,children:(0,a.__)("Launch store","woocommerce")})]})]})},Je=({showDismissModal:e,setShowDismissModal:t,hideTasks:o})=>{const s=(0,a.__)("Hide store setup tasks","woocommerce"),n=(0,a.__)("Are you sure? These tasks are required for all stores.","woocommerce"),i=(0,a.__)("Cancel","woocommerce"),r=(0,a.__)("Yes, hide store setup tasks","woocommerce");return(0,k.jsx)(k.Fragment,{children:e&&(0,k.jsx)(h.Modal,{title:s,className:"woocommerce-task-dismiss-modal",onRequestClose:()=>t(!1),children:(0,k.jsxs)("div",{className:"woocommerce-task-dismiss-modal__wrapper",children:[(0,k.jsx)("div",{className:"woocommerce-usage-modal__message",children:n}),(0,k.jsxs)("div",{className:"woocommerce-usage-modal__actions",children:[(0,k.jsx)(h.Button,{onClick:()=>t(!1),children:i}),(0,k.jsx)(h.Button,{isPrimary:!0,onClick:()=>{o("remove_card"),t(!1)},children:r})]})]})})})},Ve=o.p+"dbfe730286a89feb7ce0.svg",qe=({hideTasks:e})=>(0,k.jsx)(k.Fragment,{children:(0,k.jsx)("div",{className:(0,_.A)("woocommerce-task-dashboard__container setup-task-list"),children:(0,k.jsx)(h.Card,{size:"large",className:"woocommerce-task-card woocommerce-homescreen-card completed",children:(0,k.jsx)(h.CardHeader,{size:"medium",children:(0,k.jsxs)("div",{className:"woocommerce-task-card__header",children:[(0,k.jsx)("img",{src:Ve,alt:"Completed"}),(0,k.jsx)("h2",{children:(0,a.__)("You’ve completed store setup","woocommerce")}),(0,k.jsx)(h.Button,{variant:"primary",onClick:e,children:(0,a.__)("Hide this list","woocommerce")})]})})})})}),Ke="woocommerce_tasklist_experimental_progress_header_item",Xe=({children:e,order:t=1})=>(0,k.jsx)(h.Fill,{name:Ke,children:o=>(0,x.createOrderedChildren)(e,t,o)});Xe.Slot=({fillProps:e})=>(0,k.jsx)(h.Slot,{name:Ke,fillProps:e,children:x.sortFillsByOrder});const $e=({id:e,hideTaskListText:t})=>{const{hideTaskList:o}=(0,r.useDispatch)(c.onboardingStore);return(0,k.jsx)("div",{className:"woocommerce-card__menu woocommerce-card__header-item",children:(0,k.jsx)(x.EllipsisMenu,{label:(0,a.__)("Task List Options","woocommerce"),renderContent:()=>(0,k.jsx)("div",{className:"woocommerce-task-card__section-controls",children:(0,k.jsx)(h.Button,{onClick:()=>o(e),children:t||(0,a.__)("Hide this","woocommerce")})})})})},et=({taskListId:e})=>{const{loading:t,tasksCount:o,completedCount:s}=(0,r.useSelect)((t=>{const o=t(c.onboardingStore).getTaskList(e),s=t(c.onboardingStore).hasFinishedResolution("getTaskList",[e]),n=(0,c.getVisibleTasks)(o?.tasks||[]);return{loading:!s,tasksCount:n?.length,completedCount:n?.filter((e=>e.isComplete)).length}}),[e]);return t?null:(0,k.jsxs)("div",{className:"woocommerce-task-progress-header",children:[(0,k.jsx)($e,{id:e,hideTaskListText:(0,a.__)("Hide setup list","woocommerce")}),(0,k.jsx)("div",{className:"woocommerce-task-progress-header__contents",children:s!==o?(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)("p",{children:(0,a.sprintf)((0,a.__)("Follow these steps to start selling quickly. %1$d out of %2$d complete.","woocommerce"),s,o)}),(0,k.jsx)("progress",{className:"woocommerce-task-progress-header__progress-bar",max:o,value:s||.25})]}):null})]})},tt=({taskListId:e})=>{const t=(0,w.useSlot)(Ke);return Boolean(t?.fills?.length)?(0,k.jsx)(Xe.Slot,{fillProps:{taskListId:e}}):(0,k.jsx)(et,{taskListId:e})},ot=({task:e,activeTaskId:t,taskIndex:o,goToTask:s,trackClick:n})=>{const{createNotice:i}=(0,r.useDispatch)("core/notices"),{dismissTask:l,undoDismissTask:d}=(0,r.useDispatch)(c.onboardingStore),{id:p,title:g,badge:h,content:x,time:j,actionLabel:y,isComplete:S,additionalInfo:N,isDismissable:I}=e,C=(0,w.useSlot)(`woocommerce_onboarding_task_list_item_${p}`),M=Boolean(C?.fills?.length),b=(0,m.useCallback)((e=>{const r=(0,_.A)("woocommerce-task-list__item index-"+o,{complete:S,"is-active":p===t});return(0,k.jsx)(w.TaskItem,{className:r,title:g,badge:h,completed:S,additionalInfo:N,content:x,onClick:!1===e.isClickable?void 0:t=>{if("A"!==t.target.tagName)return e.onClick?(n(),e.onClick()):void s()},onDismiss:I?()=>(l(p),void i("success",(0,a.__)("Task dismissed","woocommerce"),{actions:[{label:(0,a.__)("Undo","woocommerce"),onClick:()=>d(p)}]})):void 0,action:()=>{},actionLabel:y},p)}),[p,g,h,x,j,y,S,t]);return M?(0,k.jsx)(u.WooOnboardingTaskListItem.Slot,{id:p,fillProps:{defaultTaskItem:b,isComplete:S}}):(0,k.jsx)(b,{})};var st=o(27752);const nt="store_setup";function it(e){if(0===e)return null;const t=Date.now()-1e3*e;return Math.round(t/c.WEEK)}const at=({hideTasks:e,customerEffortScore:t})=>{const{updateOptions:o}=(0,r.useDispatch)(c.optionsStore),[s,n]=(0,m.useState)(!1),[i,d]=(0,m.useState)(!1),[u,p]=(0,m.useState)(NaN),[g,j]=(0,m.useState)(!1),{storeAgeInWeeks:y,cesShownForActions:S,canShowCustomerEffortScore:N}=(0,r.useSelect)((e=>{const{getOption:o,hasFinishedResolution:s}=e(c.optionsStore);if(t){const e=o(st.ALLOW_TRACKING_OPTION_NAME),t=o(st.ADMIN_INSTALL_TIMESTAMP_OPTION_NAME)||0,n=o(st.SHOWN_FOR_ACTIONS_OPTION_NAME),i=!s("getOption",[st.SHOWN_FOR_ACTIONS_OPTION_NAME])||!s("getOption",[st.ADMIN_INSTALL_TIMESTAMP_OPTION_NAME]);return{storeAgeInWeeks:it(t),cesShownForActions:n,canShowCustomerEffortScore:!i&&e&&!(n||[]).includes("store_setup"),loading:i}}return{}}),[t]);(0,m.useEffect)((()=>{i&&setTimeout((()=>{j(!0)}),1200)}),[i]);const I=({firstScore:e,secondScore:t,comments:s})=>{(0,l.recordEvent)("ces_feedback",{action:nt,score:e,score_second_question:null!=t?t:null,score_combined:e+(null!=t?t:0),comments:s||"",store_age:y}),o({[st.SHOWN_FOR_ACTIONS_OPTION_NAME]:[nt,...S||[]]}),d(!0)};return(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)("div",{className:(0,_.A)("woocommerce-task-dashboard__container setup-task-list"),children:(0,k.jsxs)(h.Card,{size:"large",className:"woocommerce-task-card woocommerce-homescreen-card completed",children:[(0,k.jsx)(h.CardHeader,{size:"medium",children:(0,k.jsxs)("div",{className:"woocommerce-task-card__header",children:[(0,k.jsx)("img",{src:"data:image/svg+xml;base64,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",alt:"Completed",className:"woocommerce-task-card__finished-header-image"}),(0,k.jsx)(w.Text,{size:"title",as:"h2",lineHeight:1.4,children:(0,a.__)("You’ve completed store setup","woocommerce")}),(0,k.jsx)(w.Text,{variant:"subtitle.small",as:"p",size:"13",lineHeight:"16px",className:"woocommerce-task-card__header-subtitle",children:(0,a.__)("Congratulations! Take a moment to celebrate and look out for the first sale.","woocommerce")}),(0,k.jsx)("div",{className:"woocommerce-task-card__header-menu",children:(0,k.jsx)(x.EllipsisMenu,{label:(0,a.__)("Task List Options","woocommerce"),renderContent:()=>(0,k.jsx)("div",{className:"woocommerce-task-card__section-controls",children:(0,k.jsx)(h.Button,{onClick:()=>e(),children:(0,a.__)("Hide this","woocommerce")})})})})]})}),N&&!g&&!i&&(0,k.jsx)(st.CustomerFeedbackSimple,{label:(0,a.__)("How was your experience?","woocommerce"),onSelect:e=>{e>2?(p(e),I({firstScore:e})):(p(e),n(!0),(0,l.recordEvent)("ces_view",{action:nt,store_age:y}))}}),i&&!g&&(0,k.jsx)("div",{className:"woocommerce-task-card__header-ces-feedback",children:(0,k.jsxs)(w.Text,{variant:"subtitle.small",as:"p",size:"13",lineHeight:"16px",children:["🙌"," ",(0,a.__)("We appreciate your feedback!","woocommerce")]})})]})}),s?(0,k.jsx)(st.CustomerFeedbackModal,{title:(0,a.__)("How was your experience?","woocommerce"),firstQuestion:(0,a.__)("The store setup is easy to complete.","woocommerce"),secondQuestion:(0,a.__)("The store setup process meets my needs.","woocommerce"),defaultScore:u,recordScoreCallback:(e,t,o)=>{n(!1),I({firstScore:e,secondScore:t,comments:o})},onCloseModal:()=>{p(NaN),n(!1)}}):null]})},rt="experimental_woocommerce_tasklist_footer_item",ct=({children:e,order:t=1})=>(0,k.jsx)(h.Fill,{name:rt,children:o=>(0,x.createOrderedChildren)(e,t,o)});ct.Slot=({fillProps:e})=>(0,k.jsx)(h.Slot,{name:rt,fillProps:e,children:x.sortFillsByOrder});const lt=()=>{const e=(0,w.useSlot)(rt);return Boolean(e?.fills?.length)?(0,k.jsx)("div",{className:"woocommerce-tasklist__footer",children:(0,k.jsx)(ct.Slot,{})}):null},mt="woocommerce_experimental_task_list_completion",dt=({children:e,order:t=1})=>(0,k.jsx)(h.Fill,{name:mt,children:o=>(0,x.createOrderedChildren)(e,t,o)});dt.Slot=({fillProps:e})=>(0,k.jsx)(h.Slot,{name:mt,fillProps:e,children:x.sortFillsByOrder});const ut=({className:e,fillProps:t})=>{const o=(0,w.useSlot)(mt);return Boolean(o?.fills?.length)?(0,k.jsx)("div",{className:(0,_.A)("woocommerce-tasklist-completion-slot",e),children:(0,k.jsx)(dt.Slot,{fillProps:t})}):null},pt=({query:e,id:t,eventName:o,eventPrefix:s,tasks:n,keepCompletedTaskList:i,isComplete:d,displayProgressHeader:g,cesHeader:j=!0})=>{var y;const S=o?o+"_":s,{profileItems:N}=(0,r.useSelect)((e=>{const{getProfileItems:t}=e(c.onboardingStore);return{profileItems:t()}}),[]),{hideTaskList:I,visitedTask:C,keepCompletedTaskList:M,invalidateResolutionForStoreSelector:b}=(0,r.useDispatch)(c.onboardingStore),v=(0,c.useUserPreferences)(),[f,T]=(0,m.useState)({}),[A,P]=(0,m.useState)(""),[L,D]=(0,m.useState)(!1),{layoutString:E}=(0,Ye.useLayoutContext)(),B=(0,m.useRef)(e),O=(0,c.getVisibleTasks)(n);(0,m.useEffect)((()=>{e.task||(0,l.recordEvent)(`${S}view`,{number_tasks:O.length,store_connected:N.wccom_connected,context:E})}),[]);const z=(0,w.useSlot)(mt);(0,m.useEffect)((()=>{const{task:t}=B.current,{task:o}=e;t!==o&&(window.document.documentElement.scrollTop=0,B.current=e)}),[e]);const R=n.filter((e=>!e.isComplete&&!e.isDismissed)),W=()=>{I(t)};let Y=O.find((e=>!1===e.isComplete));Y||(Y=O[O.length-1]);const H=(0,w.useSlot)(`woocommerce_onboarding_task_list_header_${null!==(y=f?.task?.id)&&void 0!==y?y:Y?.id}`),F=Boolean(H?.fills?.length),U=async e=>{(0,l.recordEvent)(`${S}click`,{task_name:e.id,context:E,...e?.additionalData?.wooPaymentsIncentiveId&&{woopayments_incentive_id:e.additionalData.wooPaymentsIncentiveId}}),e.isComplete||await(async e=>{const t=(e=>{const t=v.task_list_tracked_started_tasks;return t&&t[e]?t[e]:0})(e)+1,o=v.task_list_tracked_started_tasks||{};C(e),await v.updateUserPreferences({task_list_tracked_started_tasks:{...o||{},[e]:t}})})(e.id)},G=e=>{U(e).then((()=>{d||b("getTaskLists")})),e.actionUrl?(0,p.navigateTo)({url:e.actionUrl}):(0,p.navigateTo)({url:(0,p.getNewPath)({task:e.id},"/",{})})};if((0,m.useEffect)((()=>{var e;Y&&(Qe[(e=Y).id]||F)&&(T({task:e,goToTask:()=>G(e),trackClick:()=>U(e)}),P(e.id))}),[Y]),!O.length)return(0,k.jsx)("div",{className:"woocommerce-task-dashboard__container"});const Z=Boolean(z?.fills?.length);return d&&"yes"!==i?Z?(0,k.jsx)(ut,{fillProps:{hideTasks:W,keepTasks:()=>{M(t)},customerEffortScore:j}}):(0,k.jsx)(k.Fragment,{children:j?(0,k.jsx)(at,{hideTasks:W,customerEffortScore:!0}):(0,k.jsx)(qe,{hideTasks:W})}):(0,k.jsxs)(k.Fragment,{children:[L&&(0,k.jsx)(Je,{showDismissModal:L,setShowDismissModal:D,hideTasks:W}),g?(0,k.jsx)(tt,{taskListId:t}):null,(0,k.jsx)("div",{className:(0,_.A)(`woocommerce-task-dashboard__container woocommerce-task-list__${t} setup-task-list`),children:(0,k.jsxs)(h.Card,{size:"large",className:"woocommerce-task-card woocommerce-homescreen-card",children:[(0,k.jsxs)("div",{className:"woocommerce-task-card__header-container",children:[(0,k.jsx)("div",{className:"woocommerce-task-card__header",children:F?(0,k.jsx)(u.WooOnboardingTaskListHeader.Slot,{id:Y?.id,fillProps:f}):f?.task&&(0,m.createElement)(Qe[f.task.id],f)}),!g&&(0,k.jsx)("div",{className:"woocommerce-card__menu woocommerce-card__header-item",children:(0,k.jsx)(x.EllipsisMenu,{className:t,label:(0,a.__)("Task List Options","woocommerce"),renderContent:({onToggle:e})=>(0,k.jsx)("div",{className:"woocommerce-task-card__section-controls",children:(0,k.jsx)(h.Button,{onClick:()=>{R.length>0?(D(!0),e()):W()},children:(0,a.__)("Hide this","woocommerce")})})})})]}),(0,k.jsx)(w.List,{animation:"custom",children:O.map(((e,t)=>(0,k.jsx)(ot,{taskIndex:++t,activeTaskId:A,task:e,goToTask:()=>G(e),trackClick:()=>U(e)},e.id)))}),(0,k.jsx)(lt,{})]})})]})},gt=e=>{const{numTasks:t=5}=e;return(0,k.jsx)("div",{className:(0,_.A)("woocommerce-task-dashboard__container setup-task-list"),children:(0,k.jsxs)("div",{className:"components-card is-size-large woocommerce-task-card woocommerce-homescreen-card is-loading",children:[(0,k.jsx)("div",{className:"components-card__header is-size-medium",children:(0,k.jsx)("div",{className:"woocommerce-task-card__header",children:(0,k.jsx)("div",{className:"is-placeholder",children:" "})})}),(0,k.jsx)("ul",{className:"woocommerce-experimental-list",children:Array.from(new Array(t)).map(((e,t)=>(0,k.jsxs)("li",{tabIndex:t,className:"woocommerce-experimental-list__item woocommerce-task-list__item",children:[(0,k.jsx)("div",{className:"woocommerce-task-list__item-before",children:(0,k.jsx)("div",{className:"is-placeholder"})}),(0,k.jsx)("div",{className:"woocommerce-task-list__item-text",children:(0,k.jsx)("div",{className:"components-truncate components-text is-placeholder"})})]},t)))})]})})};var ht=o(46445),_t=o(48558);const xt=({title:e})=>{const t=(0,a.__)("WooCommerce Home","woocommerce"),o=()=>{(0,l.recordEvent)("topbar_back_button",{page_name:e}),(0,p.updateQueryString)({},(0,p.getHistory)().location.pathname,{})};return(0,k.jsx)(h.Tooltip,{text:t,children:(0,k.jsx)("div",{tabIndex:0,role:"button","data-testid":"header-back-button",className:"woocommerce-layout__header-back-button",onKeyDown:({keyCode:e})=>{e!==_t.ENTER&&e!==_t.SPACE||o()},children:(0,k.jsx)(V.A,{icon:ht.A,onClick:o})})})},wt=({query:e,task:t})=>{const o=e.task||"";o||console.warn("No task id provided");const{invalidateResolutionForStoreSelector:s,optimisticallyCompleteTask:n}=(0,r.useDispatch)(c.onboardingStore),i=(0,m.useCallback)((async()=>{const e=document.querySelectorAll("#adminmenu .woocommerce-task-list-remaining-tasks-badge");if(!e?.length)return;const t=await(0,r.resolveSelect)(c.onboardingStore).getTaskList("setup");if(!t)return;const o=t.tasks.filter((e=>!e.isComplete)).length;e.forEach((e=>{e.textContent=o.toString()}))}),[]),a=(0,m.useCallback)((e=>{n(o),(0,p.getHistory)().push(e&&e.redirectPath?e.redirectPath:(0,p.getNewPath)({},"/",{})),s("getTaskLists"),i()}),[o,s,n,i]);return(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(Ye.WooHeaderNavigationItem,{children:(0,k.jsx)(xt,{title:t.title})}),(0,k.jsx)(Ye.WooHeaderPageTitle,{children:t.title}),(0,k.jsx)(u.WooOnboardingTask.Slot,{id:o,fillProps:{onComplete:a,query:e,task:t}})]})};var jt=o(13832);const kt=({isExpandable:e=!1,isExpanded:t=!1,setExpandedTask:o,task:s})=>{const{createNotice:n}=(0,r.useDispatch)("core/notices"),{layoutString:i}=(0,Ye.useLayoutContext)(),{dismissTask:d,snoozeTask:g,undoDismissTask:h,undoSnoozeTask:_,visitedTask:x,invalidateResolutionForStoreSelector:j}=(0,r.useDispatch)(c.onboardingStore),y=(0,c.useUserPreferences)(),{actionLabel:S,actionUrl:N,content:I,id:C,isComplete:M,isDismissable:b,isSnoozeable:v,time:f,title:T,badge:A,level:P,additionalInfo:L,recordViewEvent:D}=s;(0,m.useEffect)((()=>{D&&(0,l.recordEvent)("tasklist_item_view",{task_name:C,is_complete:M,context:i})}),[]);const E=(0,w.useSlot)(`woocommerce_onboarding_task_list_item_${C}`),B=Boolean(E?.fills?.length),O=(0,m.useCallback)((()=>{d(C),n("success",(0,a.__)("Task dismissed","woocommerce"),{actions:[{label:(0,a.__)("Undo","woocommerce"),onClick:()=>h(C)}]})}),[C]),z=(0,m.useCallback)((()=>{g(C),n("success",(0,a.__)("Task postponed until tomorrow","woocommerce"),{actions:[{label:(0,a.__)("Undo","woocommerce"),onClick:()=>_(C)}]})}),[C]),R=(0,m.useCallback)((()=>{N?(0,p.navigateTo)({url:N}):(0,p.navigateTo)({url:(0,p.getNewPath)({task:C},"/",{})})}),[C,M,N]),W={expandable:e,expanded:e&&t,completed:M,onSnooze:v?z:void 0,onDismiss:b?O:void 0},Y=(0,m.useCallback)((t=>{const s=e=>((async()=>{(0,l.recordEvent)("tasklist_click",{task_name:C,context:i}),M||await(async()=>{const e=(()=>{const e=y.task_list_tracked_started_tasks;return e&&e[C]?e[C]:0})()+1,t=y.task_list_tracked_started_tasks||{};x(C),await y.updateUserPreferences({task_list_tracked_started_tasks:{...t||{},[C]:e}})})()})().then((()=>{M||j("getTaskLists")})),t.onClick?t.onClick(e):R());return(0,k.jsx)(w.TaskItem,{title:T,badge:A,content:I,additionalInfo:L,time:f,action:s,level:P,actionLabel:S,...W,...t,onClick:!e||M?s:()=>o(C)},C)}),[C,T,A,I,f,S,e,M]);return B?(0,k.jsx)(u.WooOnboardingTaskListItem.Slot,{id:C,fillProps:{defaultTaskItem:Y,isComplete:M,...W}}):(0,k.jsx)(Y,{onClick:s.onClick})},yt=({id:e,eventPrefix:t,tasks:o,title:s,isCollapsible:n=!1,isExpandable:i=!1,displayProgressHeader:d=!1,query:u})=>{const{profileItems:p}=(0,r.useSelect)((e=>{const{getProfileItems:t}=e(c.onboardingStore);return{profileItems:t()}}),[]),g=(0,m.useRef)(u),_=(0,c.getVisibleTasks)(o),{layoutString:j}=(0,Ye.useLayoutContext)(),y=o.filter((e=>!e.isComplete&&!e.isDismissed)),[S,N]=(0,m.useState)(y[0]?.id);if((0,m.useEffect)((()=>{(0,l.recordEvent)(t+"view",{number_tasks:_.length,store_connected:p.wccom_connected,context:j})}),[]),(0,m.useEffect)((()=>{const{task:e}=g.current,{task:t}=u;e!==t&&(window.document.documentElement.scrollTop=0,g.current=u)}),[u]),!_.length)return(0,k.jsx)("div",{className:"woocommerce-task-dashboard__container"});const I=(0,a.sprintf)((0,a._n)("Show %d more task.","Show %d more tasks.",_.length-2,"woocommerce"),_.length-2),C=(0,a.__)("Show less","woocommerce"),M=_.map((e=>(0,k.jsx)(kt,{isExpanded:S===e.id,isExpandable:i,task:e,setExpandedTask:N},e.id)));return(0,k.jsx)(k.Fragment,{children:(0,k.jsxs)("div",{className:"woocommerce-task-dashboard__container woocommerce-task-list__"+e,children:[d?(0,k.jsx)(tt,{taskListId:e}):null,(0,k.jsxs)(h.Card,{size:"large",className:"woocommerce-task-card woocommerce-homescreen-card",children:[(0,k.jsxs)(h.CardHeader,{size:"medium",children:[(0,k.jsxs)("div",{className:"woocommerce-task-card__header",children:[(0,k.jsx)(w.Text,{size:"20",lineHeight:"28px",variant:"title.small",children:s}),(0,k.jsx)(x.Badge,{count:y.length})]}),(0,k.jsx)($e,{id:e})]}),n?(0,k.jsx)(w.CollapsibleList,{animation:"custom",collapseLabel:C,expandLabel:I,show:2,onCollapse:()=>(0,l.recordEvent)(t+"collapse",{}),onExpand:()=>(0,l.recordEvent)(t+"expand",{}),children:M}):(0,k.jsx)(w.List,{animation:"custom",children:M})]})]})})},St=({query:e})=>{const{task:t}=e,{hideTaskList:o}=(0,r.useDispatch)(c.onboardingStore),{isResolving:n,taskLists:i}=(0,r.useSelect)((e=>({isResolving:!e(c.onboardingStore).hasFinishedResolution("getTaskLists",[]),taskLists:e(c.onboardingStore).getTaskLists()})),[]),d=(()=>{if(!t)return null;return i.reduce(((e,t)=>[...e,...t.tasks]),[]).find((e=>e.id===t))||null})();if(t&&!d)return null;if(d)return(0,k.jsx)("div",{className:"woocommerce-task-dashboard__container",children:(0,k.jsx)(wt,{query:e,task:d})});const u="setup"===(0,s.Qk)("visibleTaskListIds",[])[0]?gt:jt.W;return n?(0,k.jsx)(u,{query:e}):(0,k.jsx)(k.Fragment,{children:i.filter((({isVisible:e})=>e)).map((t=>{const{id:s,isHidden:n,isToggleable:i}=t,r="setup"===s?pt:yt;return(0,k.jsxs)(m.Fragment,{children:[(0,k.jsx)(r,{isExpandable:!1,query:e,...t}),i&&(0,k.jsx)(We.q,{children:(0,k.jsx)(h.MenuGroup,{className:"woocommerce-layout__homescreen-display-options",label:(0,a.__)("Display","woocommerce"),children:(0,k.jsx)(h.MenuItem,{className:"woocommerce-layout__homescreen-extension-tasklist-toggle",icon:n?void 0:Re.A,isSelected:!n,role:"menuitemcheckbox",onClick:()=>(e=>{const{id:t,eventPrefix:s,isHidden:n}=e,i=!n;(0,l.recordEvent)(i?`${s}hide`:`${s}show`,{}),o(t)})(t),children:(0,a.__)("Show things to do next","woocommerce")})})})]},s)}))})};var Nt=o(11846),It=o(72685)},97687:(e,t,o)=>{o.d(t,{E:()=>s});const s=()=>void 0!==window.wcCalypsoBridge&&window.wcCalypsoBridge.isWooExpress}}]);