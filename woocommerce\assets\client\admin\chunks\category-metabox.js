"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[6666],{57173:(e,t,a)=>{a.r(t),a.d(t,{default:()=>N});var r=a(27723),c=a(86087),n=a(27655),o=a(93832),s=a(29491),l=a(98846),d=a(15703),i=a(83306),m=a(1455),u=a.n(m),_=a(39793);const h={};function p(e,t=[]){for(const a of e){const e={label:a.name,value:a.term_id.toString(),children:[]};h[a.term_id]=a,t.push(e),a.children?.length&&p(a.children,e.children)}return t.sort(((e,t)=>{const a=e.label.toUpperCase(),r=t.label.toUpperCase();return a<r?-1:a>r?1:0})),t}async function g(e){const t=await u()({url:(0,o.addQueryArgs)(new URL("admin-ajax.php",(0,d.getSetting)("adminUrl")).toString(),{term:e,action:"woocommerce_json_search_categories_tree",security:wc_product_category_metabox_params.search_categories_nonce}),method:"GET"});return t?p(Object.values(t)):[]}const y=(0,c.forwardRef)((({selectedCategoryTerms:e,onChange:t},a)=>{const[o,d]=(0,c.useState)(""),[m,u]=(0,c.useState)([]),p=(0,c.useCallback)((e=>{e&&e.length>0&&(0,i.recordEvent)("product_category_search",{page:"product",async:!0,search_string_length:e.length}),g(e).then((e=>{u(Object.values(e))}))}),[u]),y=(0,s.useDebounce)(p,250);return(0,c.useEffect)((()=>{y(o)}),[o]),(0,c.useImperativeHandle)(a,(()=>({resetInitialValues(){g("").then((e=>{u(Object.values(e))}))}})),[]),(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)("div",{className:"product-add-category__tree-control",children:(0,_.jsx)(l.TreeSelectControl,{alwaysShowPlaceholder:!0,options:m,value:e.map((e=>e.term_id.toString())),onChange:e=>{t(e.map((e=>h[e]))),(0,i.recordEvent)("product_category_update",{page:"product",async:!0,selected:e.length})},selectAllLabel:!1,onInputChange:d,placeholder:(0,r.__)("Add category","woocommerce"),includeParent:!0,minFilterQueryLength:2,clearOnSelect:!1,individuallySelectParent:!0})}),(0,_.jsx)("ul",{className:"categorychecklist form-no-clear tagchecklist",id:n.mL+"checklist",children:e.map((a=>(0,_.jsxs)("li",{children:[(0,_.jsxs)("button",{type:"button",className:"ntdelbutton",onClick:()=>{const r=e.filter((e=>e.term_id!==a.term_id));t(r)},children:[(0,_.jsx)("span",{className:"remove-tag-icon","aria-hidden":"true"}),(0,_.jsx)("span",{className:"screen-reader-text",children:(0,r.sprintf)((0,r.__)("Remove term: %s","woocommerce"),a.name)})]}),a.name]},a.term_id)))})]})})),x=({selected:e,onChange:t})=>{const[a,r]=(0,c.useState)([]);(0,c.useEffect)((()=>{u()({url:(0,o.addQueryArgs)(new URL("admin-ajax.php",(0,d.getSetting)("adminUrl")).toString(),{action:"woocommerce_json_search_taxonomy_terms",taxonomy:n.mL,limit:10,orderby:"count",order:"DESC",security:wc_product_category_metabox_params.search_taxonomy_terms_nonce}),method:"GET"}).then((e=>{e&&r(e.filter((e=>e.count>0)))}))}),[]);const s=e.map((e=>e.term_id));return(0,_.jsx)("ul",{className:"categorychecklist form-no-clear",id:n.mL+"checklist-pop",children:a.map((a=>{const r=`in-popular-${n.mL}-${a.term_id}`;return(0,_.jsx)("li",{className:"popular-category",children:(0,_.jsxs)("label",{className:"selectit",htmlFor:r,children:[(0,_.jsx)("input",{type:"checkbox",id:r,checked:s.includes(a.term_id),onChange:()=>{s.includes(a.term_id)?t(e.filter((e=>e.term_id!==a.term_id))):t([...e,a])}}),a.name]})},a.term_id)}))})};var w=a(40314);function b(e){return e?.name||""}function j(e){return String(e?.term_id)}const v=({selectedCategoryTerms:e,onChange:t})=>{const[a,s]=(0,c.useState)(!1),[m,h]=(0,c.useState)(""),[p,g]=(0,c.useState)(""),[y,x]=(0,c.useState)(),[v,S]=(0,c.useState)([]),{currentUserCan:f}=(0,w.useUser)(),C=f("edit_product_terms"),N=(0,c.useCallback)((async(e="")=>(S([]),u()({url:(0,o.addQueryArgs)(new URL("admin-ajax.php",(0,d.getSetting)("adminUrl")).toString(),{term:e,action:"woocommerce_json_search_categories",security:wc_product_category_metabox_params.search_categories_nonce}),method:"GET"}).then((e=>(e&&S(Object.values(e)),[]))))),[]),{isFetching:L,...k}=(0,l.useAsyncFilter)({filter:N});return C?(0,_.jsxs)("div",{id:n.mL+"-adder",children:[(0,_.jsx)("a",{id:"product_cat-add-toggle",href:"#taxonomy-"+n.mL,className:"taxonomy-add-new",onClick:()=>s(!a),"aria-label":(0,r.__)("Add new category","woocommerce"),children:(0,r.__)("+ Add new category","woocommerce")}),a&&(0,_.jsxs)("div",{id:"product_cat-add",className:"category-add",children:[(0,_.jsx)("label",{className:"screen-reader-text",htmlFor:"newproduct_cat",children:(0,r.__)("Add new category","woocommerce")}),(0,_.jsx)("input",{type:"text",name:"newproduct_cat",id:"newproduct_cat",className:"form-required",placeholder:(0,r.__)("New category name","woocommerce"),value:m,onChange:e=>h(e.target.value),"aria-required":"true"}),(0,_.jsx)("label",{className:"screen-reader-text",htmlFor:"newproduct_cat_parent",children:(0,r.__)("Parent category:","woocommerce")}),(0,_.jsx)(l.__experimentalSelectControl,{...k,label:(0,r.__)("Parent category:","woocommerce"),items:v,selected:y||null,placeholder:(0,r.__)("Find category","woocommerce"),onSelect:x,getItemLabel:b,getItemValue:j,onRemove:()=>x(void 0)}),p&&(0,_.jsx)("p",{className:"category-add__error",children:p}),(0,_.jsx)("input",{type:"button",id:"product_cat-add-submit",className:"button category-add-submit",value:(0,r.__)("Add new category","woocommerce"),disabled:!m.length,onClick:a=>{var r;if(a.preventDefault(),!m)return;const c={name:m,parent:null!==(r=y?.term_id)&&void 0!==r?r:-1};g(""),u()({path:"/wc/v3/products/categories",data:c,method:"POST"}).then((a=>{a&&((0,i.recordEvent)("product_category_add",{category_id:a.id,parent_id:a.parent,parent_category:a.parent>0?"Other":"None",page:"product",async:!0}),t([...e,{term_id:a.id,name:a.name,count:a.count}]),h(""),x(void 0),s(!1))})).catch((e=>{e&&e.message&&g(e.message)}))}})]})]}):null};let S="";window.getUserSetting&&(S=window.getUserSetting(n.mL+"_tab")||"");const f="pop",C="all",N=({initialSelected:e})=>{const[t,a]=(0,c.useState)(e),o=(0,c.useRef)(null),[s,l]=(0,c.useState)(S===f?S:C);return(0,_.jsxs)("div",{id:"taxonomy-"+n.mL,className:"categorydiv category-async-metabox",children:[(0,_.jsxs)("ul",{className:"category-tabs",children:[(0,_.jsx)("li",{className:s===C?"tabs":"",children:(0,_.jsx)("a",{href:"#"+n.mL+"-"+C,onClick:e=>{e.preventDefault(),l(C),window.deleteUserSetting&&window.deleteUserSetting(n.mL+"_tab")},children:(0,r.__)("All items","woocommerce")})}),(0,_.jsx)("li",{className:s===f?"tabs":"",children:(0,_.jsx)("a",{href:"#"+n.mL+"-"+f,onClick:e=>{e.preventDefault(),l(f),window.setUserSetting&&window.setUserSetting(n.mL+"_tab",f)},children:(0,r.__)("Most used","woocommerce")})})]}),(0,_.jsx)("div",{className:"tabs-panel",id:n.mL+"-"+f,style:s!==f?{display:"none"}:{},children:(0,_.jsx)("ul",{id:n.mL+"checklist-"+f,className:"categorychecklist form-no-clear",children:(0,_.jsx)(x,{selected:t,onChange:a})})}),(0,_.jsx)("div",{className:"tabs-panel",id:n.mL+"-"+C,style:s!==C?{display:"none"}:{},children:(0,_.jsx)(y,{selectedCategoryTerms:t,onChange:a,ref:o})}),(t||[]).map((e=>(0,_.jsx)("input",{type:"hidden",value:e.term_id,name:"tax_input["+n.mL+"][]"},e.term_id))),0===t.length&&(0,_.jsx)("input",{type:"hidden",value:"",name:"tax_input["+n.mL+"][]"}),(0,_.jsx)(v,{selectedCategoryTerms:t,onChange:e=>{a(e),o.current&&o.current.resetInitialValues()}})]})}}}]);