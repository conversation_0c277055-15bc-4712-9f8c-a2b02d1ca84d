"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[319],{579:(s,e,c)=>{c.r(e),c.d(e,{default:()=>r});var t=c(5460),a=c(4921),n=c(7723),o=c(371),l=c(790);const r=s=>{const{cartItemsCount:e}=(0,t.V)(),c=(0,o.p)(s);return(0,l.jsx)("span",{className:(0,a.A)(s.className,c.className),style:c.style,children:(0,n.sprintf)(/* translators: %d is the count of items in the cart. */ /* translators: %d is the count of items in the cart. */
(0,n._n)("(%d item)","(%d items)",e,"woocommerce"),e)})}}}]);