(()=>{"use strict";var o={n:e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return o.d(r,{a:r}),r},d:(e,r)=>{for(var c in r)o.o(r,c)&&!o.o(e,c)&&Object.defineProperty(e,c,{enumerable:!0,get:r[c]})},o:(o,e)=>Object.prototype.hasOwnProperty.call(o,e)};const e=window.wp.domReady;var r=o.n(e);const c=window.wc.tracks;r()((()=>{const o=document.querySelectorAll(".woocommerce-purchase-subscription");o.length>0&&((0,c.recordEvent)("woo_purchase_subscription_in_plugins_shown"),o.forEach((o=>{o.addEventListener("click",(function(){(0,c.recordEvent)("woo_purchase_subscription_in_plugins_clicked")}))})))})),(window.wc=window.wc||{}).wooPurchaseSubscription={}})();